package com.ruoyi.framework.web.service;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 验证码发送方法
 * 
 * <AUTHOR>
 */
@Component
public class SysSmsService
{
    @Autowired
    private RedisCache redisCache;

    /**
     * 校验手机验证码
     *
     * @param phoneNumber 手机号
     * @param code 验证码
     * @param key  缓存主键
     * @return 结果
     */
    public void verifySmsCode(String key,String phoneNumber,String code){

        String verifyKey = key + phoneNumber;

        String captcha = redisCache.getCacheObject(verifyKey);

        if(captcha == null) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
            // 抛出一个验证码过期异常
            throw new CaptchaExpireException();
        }
        if(!captcha.equals(code.trim())){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            // 抛出一个验证码错误的异常
            throw new CaptchaException();
        }

        redisCache.deleteObject(verifyKey);
    }

}

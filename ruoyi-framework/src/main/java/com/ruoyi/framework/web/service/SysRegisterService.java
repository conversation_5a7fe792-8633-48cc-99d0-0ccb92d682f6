package com.ruoyi.framework.web.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;

/**
 * 注册校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysRegisterService
{
    @Autowired
    private ISysUserService userService;
    @Autowired
    private SysSmsService sysSmsService;

    @Autowired
    private ISxscUserInfoService iSxscUserInfoService;

    /**
     * 注册
     */
    public String register(RegisterBody registerBody)
    {
        String msg = "", username = registerBody.getUsername(), password = registerBody.getPassword();
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);

        if (StringUtils.isEmpty(username))
        {
            msg = "用户名不能为空";
        }
        else if (StringUtils.isEmpty(password))
        {
            msg = "用户密码不能为空";
        }
        else if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            msg = "账户长度必须在2到20个字符之间";
        }
        else if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            msg = "密码长度必须在5到20个字符之间";
        }
        SxscUserInfo sxscUserInfo=new SxscUserInfo();
        if(StringUtils.isNotEmpty(registerBody.getInvitationCode())){
            SysUser sysUserInvitation=userService.selectUserByUserName(registerBody.getInvitationCode());
            if(StringUtils.isNull(sysUserInvitation)){
                return  "注册失败,邀请人不存在";
            }else{
                sxscUserInfo.setParentTime(DateUtils.getNowDate());
                sxscUserInfo.setParentId(sysUserInvitation.getUserId());
            }
        }

        // 校验验证码
        sysSmsService.verifySmsCode(CacheConstants.PHONE_CODE_KEY,registerBody.getUsername(), registerBody.getCode());

        if (!userService.checkUserNameUnique(sysUser))
        {
            msg = "账号已存在";
        }
        else
        {
            sysUser.setNickName(username.substring(0,4)+"****"+username.substring(8,11));
            sysUser.setPassword(SecurityUtils.encryptPassword(password));
            sysUser.setPhonenumber(username);
            //初始化会员角色
            sysUser.setRoleIds(new Long[]{101l});
            sysUser.setDeptId(101l);
            sysUser.setUserType("01");
            boolean regFlag = userService.insertUser(sysUser)==1;

            if (!regFlag)
            {
                msg = "注册失败,请联系系统管理人员";
            }
            else
            {
                sxscUserInfo.setUserId(sysUser.getUserId());
                iSxscUserInfoService.insertSxscUserInfo(sxscUserInfo);
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));
            }
        }
        return msg;
    }

}

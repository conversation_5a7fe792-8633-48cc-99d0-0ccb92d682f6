package com.ruoyi.framework.interceptor;

import java.lang.reflect.Method;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.seting.domain.SxscSetingVersion;
import com.ruoyi.sxsc.seting.service.ISxscSetingVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.ServletUtils;


/**
 * 防止重复提交拦截器
 *
 * <AUTHOR>
 */
@Component
public abstract class RepeatSubmitInterceptor implements HandlerInterceptor
{

    @Autowired
    ISxscSetingVersionService iSxscSetingVersionService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        SxscSetingVersion versionUrl=isVersion(request);
        if(StringUtils.isNotNull(versionUrl)){
            AjaxResult ajaxResult = new AjaxResult(HttpStatus.MOVED_PERM,"请更新APP到最新的版本",versionUrl);
            ServletUtils.renderString(response, JSON.toJSONString(ajaxResult));
            return false;
        }

        if (handler instanceof HandlerMethod)
        {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            RepeatSubmit annotation = method.getAnnotation(RepeatSubmit.class);
            if (annotation != null)
            {
                if (this.isRepeatSubmit(request, annotation))
                {
                    AjaxResult ajaxResult = AjaxResult.error(annotation.message());
                    ServletUtils.renderString(response, JSON.toJSONString(ajaxResult));
                    return false;
                }
            }
            return true;
        }
        else
        {
            return true;
        }
    }

    /**
     * 验证是否重复提交由子类实现具体的防重复提交的规则
     *
     * @param request 请求信息
     * @param annotation 防重复注解参数
     * @return 结果
     * @throws Exception
     */
    public abstract boolean isRepeatSubmit(HttpServletRequest request, RepeatSubmit annotation);

    /**
     * 验证请求版本号
     *
     * @param request 请求信息
     * @return 结果
     * @throws Exception
     */
    public  SxscSetingVersion isVersion(HttpServletRequest request){
        String appVersionCode=request.getHeader("appVersionCode");

        if("7e68cbe4abe1ad5d7a1772ae1247f895".equals(appVersionCode)){
            return null;
        }
        if(request.getRequestURI().contains("/ali-notify")){
            return null;
        }
        if(request.getRequestURI().contains("import")){
            return null;
        }
        if(request.getRequestURI().contains("export")){
            return null;
        }
        if(request.getRequestURI().contains("common")){
            return null;
        }
        if(request.getRequestURI().contains("dingdong/notify")){
            return null;
        }
        if(request.getRequestURI().contains("tool")){
            return null;
        }
        SxscSetingVersion sxscSetingVersion=iSxscSetingVersionService.selectSxscSetingVersionByForce();
        if(StringUtils.isNotNull(sxscSetingVersion)){
            if(StringUtils.isEmpty(appVersionCode)){
                return sxscSetingVersion;
            }
            if(compareVersions(sxscSetingVersion.getVersionNumber(),appVersionCode)){
                return sxscSetingVersion;
            }
        }
        return null;
    }

    public static boolean compareVersions(String version1, String version2) {
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");

        int length = Math.max(parts1.length, parts2.length);

        for (int i = 0; i < length; i++) {
            int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;

            if (num1 < num2) {
                return false;
            } else if (num1 > num2) {
                return true;
            }
        }
        return false;
    }

    public static void main(String[] args) {
        System.out.println(compareVersions("1.0.5.4","1.0.5.5"));
    }
}

package com.ruoyi.sxsc.utils;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayFundTransUniTransferModel;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.domain.Participant;
import com.alipay.api.request.AlipayFundTransToaccountTransferRequest;
import com.alipay.api.request.AlipayFundTransUniTransferRequest;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayFundTransToaccountTransferResponse;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrderRefund;
import com.ruoyi.sxsc.config.AliPayConfig;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import com.ruoyi.sxsc.payment.domain.SxscAliPayWithdrawal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Slf4j
@Component
public class AliPayUtils {

    @Autowired
    AliPayConfig aliPayConfig;


    /**
     * App跳转支付宝支付
     */
    public String aliPayOrder(SxscAliPayOrder payment)  {

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setAppId(aliPayConfig.app_id);
        alipayConfig.setPrivateKey(aliPayConfig.merchant_private_key);
        alipayConfig.setAppCertPath(aliPayConfig.app_cert_path);
        alipayConfig.setAlipayPublicCertPath(aliPayConfig.alipay_cert_path);
        alipayConfig.setRootCertPath(aliPayConfig.alipay_root_cert_path);
        alipayConfig.setServerUrl(aliPayConfig.gatewayUrl);
        alipayConfig.setFormat(aliPayConfig.format);
        alipayConfig.setCharset(aliPayConfig.charset);
        alipayConfig.setSignType(aliPayConfig.sign_type);
        //构造client
        DefaultAlipayClient alipayClient = null;
        try {
            alipayClient = new DefaultAlipayClient(alipayConfig);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        // 创建一个支付请求，设置请求参数
        AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
        AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
        model.setTotalAmount(payment.getTotalAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        model.setSubject("惠创优选<"+payment.getSubject()+">");
        model.setOutTradeNo(payment.getId());

        model.setTimeoutExpress(aliPayConfig.timeout_express);
        request.setBizModel(model);
        request.setNotifyUrl(aliPayConfig.notify_url);

        AlipayTradeAppPayResponse response = null;
        try {
            response = alipayClient.sdkExecute(request);
            //就是orderString 可以直接给客户端请求，无需再做处理。
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        return response.getBody();
    }

    /**
     * 支付宝提现信息
     */
    /**
     * 提现
     */
    public SxscAliPayWithdrawal withdraw(SxscAliPayWithdrawal aliWithdraw){
        //提现
        //TODO 1、实例化客户端

        AlipayConfig alipayConfig = config();
        alipayConfig.setServerUrl(aliPayConfig.gatewayUrl);
        alipayConfig.setFormat(aliPayConfig.format);
        alipayConfig.setCharset(aliPayConfig.charset);
        alipayConfig.setSignType(aliPayConfig.sign_type);
        //构造client
        DefaultAlipayClient alipayClient = null;
        try {
            alipayClient = new DefaultAlipayClient(alipayConfig);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }

        //TODO 2、实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.uni.app.pay
        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
        AlipayFundTransUniTransferModel model = new AlipayFundTransUniTransferModel();

        // 设置商家侧唯一订单号
        model.setOutBizNo(aliWithdraw.getId());

        // 设置订单总金额
        model.setTransAmount(String.valueOf(aliWithdraw.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP)));

        // 设置描述特定的业务场景
        model.setBizScene("DIRECT_TRANSFER");

        // 设置业务产品码
        model.setProductCode("TRANS_ACCOUNT_NO_PWD");
        String orderTile="";
        if(aliWithdraw.getOrderName().length()>80){
            orderTile=aliWithdraw.getOrderName().substring(0,79);
        }else{
            orderTile=aliWithdraw.getOrderName();
        }
        // 设置转账业务的标题
        model.setOrderTitle("惠创优选<"+orderTile+"...>");

        // 设置收款方信息
        Participant payeeInfo = new Participant();

        payeeInfo.setIdentity(aliWithdraw.getPayeeAccount());
        payeeInfo.setName(aliWithdraw.getPayeeRealName());
        payeeInfo.setIdentityType("ALIPAY_LOGON_ID");
        model.setPayeeInfo(payeeInfo);
        // 设置业务备注
        model.setRemark("惠创优选");

        request.setBizModel(model);
        //商户转账唯一订单号
        aliWithdraw.setPayeeType("ALIPAY_LOGON_ID");
        AlipayFundTransUniTransferResponse response = null;
        try {
            response = alipayClient.certificateExecute(request);
        } catch (AlipayApiException e) {
            aliWithdraw.setStatus(0l);
            throw new RuntimeException(e.getErrMsg());
        }
        log.info("支付宝的响应：{}",JSONObject.toJSON(response));
        aliWithdraw.setAlipayFundTransUniTransferResponse(response);
        aliWithdraw.setRes(JSONObject.toJSONString(response));
        return aliWithdraw;
    }


    /**
     * 退款
     */
    public String aliPayRefund(SxscCommodityOrderRefund orderRefund){
        AlipayConfig alipayConfig = config();
        alipayConfig.setServerUrl(aliPayConfig.gatewayUrl);
        alipayConfig.setFormat(aliPayConfig.format);
        alipayConfig.setCharset(aliPayConfig.charset);
        alipayConfig.setSignType(aliPayConfig.sign_type);
        //构造client
        DefaultAlipayClient alipayClient = null;
        try {
            alipayClient = new DefaultAlipayClient(alipayConfig);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        AlipayTradeRefundRequest aliPayRequest = new AlipayTradeRefundRequest();
        AlipayTradeRefundModel model = new AlipayTradeRefundModel();
        //订单支付时传入的商户订单号,不能和trade_no同时为空
        model.setOutTradeNo(orderRefund.getId());
        //支付宝交易号，和商户订单号不能同时为空
        //model.setTradeNo("2020122122001400320501115255");
        //需要退款的金额，该金额不能大于订单金额,单位为元，支持两位小数
        model.setRefundAmount(orderRefund.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        //退款的原因说明
        model.setRefundReason(orderRefund.getSubject());
        //标识一次退款请求，同一笔交易多次退款需要保证唯一，如需部分退款，则此参数必传。
        model.setOutRequestNo(orderRefund.getId());
        aliPayRequest.setBizModel(model);
        try {
            AlipayTradeRefundResponse aliPayResponse = alipayClient.certificateExecute(aliPayRequest);
            log.debug("aliPayResponse:{}", aliPayResponse);
            if (!"10000".equals(aliPayResponse.getCode())) {
                log.info("支付宝退款失败，支付宝交易号：{}，状态码：{}", orderRefund.getId(), aliPayResponse.getCode());
                orderRefund.setStatus(0l);
                return "fail";
            }
            orderRefund.setStatus(1l);
            orderRefund.setResponse(JSONObject.toJSONString(aliPayResponse));
            return aliPayResponse.getMsg();
        } catch (AlipayApiException e) {
            e.printStackTrace();
            orderRefund.setStatus(0l);
            return "fail";
        }
    }

    private AlipayConfig config(){
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setAppId(aliPayConfig.app_id);
        alipayConfig.setPrivateKey(aliPayConfig.merchant_private_key);
        alipayConfig.setAppCertPath(aliPayConfig.app_cert_path);
        alipayConfig.setAlipayPublicCertPath(aliPayConfig.alipay_cert_path);
        alipayConfig.setRootCertPath(aliPayConfig.alipay_root_cert_path);
        return alipayConfig;
    }
}

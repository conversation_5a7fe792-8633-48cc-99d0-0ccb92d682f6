package com.ruoyi.sxsc.utils;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.config.OSSCloudConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

@Component
public class OSSCloudClient {


    @Autowired
    OSSCloudConfig cloudConfig;

    /**
     * 上传文件
     * @param fileName 文件名称
     * @param userId 用户id
     * @param is 文件输入流(fileInputStream或InputStream)
     * @throws Exception
     */
    public String uploadFile(String fileName, Long userId, InputStream is) throws Exception {
        Map<String, String> nameMap = this.getObjectName(fileName, userId);
        String objectName = nameMap.get("objectName");
        OSS ossClient = createOSS();
        try {

            PutObjectRequest putObjectRequest=new PutObjectRequest(cloudConfig.getBucketName(), objectName, is);
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(getcontentType(fileName.substring(fileName.lastIndexOf("."))));
            objectMetadata.setContentDisposition("inline");
            putObjectRequest.setMetadata(objectMetadata);

            ossClient.putObject(putObjectRequest);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return nameMap.get("filePath");
    }

    public static String getcontentType(String FilenameExtension) {
        if (FilenameExtension.equalsIgnoreCase(".bmp")) {
            return "image/bmp";
        }
        if (FilenameExtension.equalsIgnoreCase(".gif")) {
            return "image/gif";
        }
        if (FilenameExtension.equalsIgnoreCase(".jpeg") ||
                FilenameExtension.equalsIgnoreCase(".jpg") ||
                FilenameExtension.equalsIgnoreCase(".png")) {
            return "image/jpg";
        }
        if (FilenameExtension.equalsIgnoreCase(".html")) {
            return "text/html";
        }
        if (FilenameExtension.equalsIgnoreCase(".txt")) {
            return "text/plain";
        }
        if (FilenameExtension.equalsIgnoreCase(".vsd")) {
            return "application/vnd.visio";
        }
        if (FilenameExtension.equalsIgnoreCase(".pptx") ||
                FilenameExtension.equalsIgnoreCase(".ppt")) {
            return "application/vnd.ms-powerpoint";
        }
        if (FilenameExtension.equalsIgnoreCase(".docx") ||
                FilenameExtension.equalsIgnoreCase(".doc")) {
            return "application/msword";
        }
        if (FilenameExtension.equalsIgnoreCase(".xml")) {
            return "text/xml";
        }
        return "image/jpg";
    }

    private Map<String, String> getObjectName(String fileName, Long userId) {
        Map<String, String> nameMap = new HashMap<>();
        String name = userId + "/" + DateUtils.datePath() + "/" + IdUtils.fastSimpleUUID() + "_" + fileName;
        nameMap.put("objectName", cloudConfig.objectPrefixName + name);
        nameMap.put("filePath", filePathPrefix() + name);
        return nameMap;
    }

    private String filePathPrefix(){
        return   "https://" + cloudConfig.bucketName + "." + cloudConfig.endpoint + "/" + cloudConfig.objectPrefixName; //带https的完整访问前缀
    }

    private OSS createOSS() {
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        // 设置连接OSS所使用的协议（HTTP或HTTPS），默认为HTTP。
        conf.setProtocol(Protocol.HTTPS);

        // 创建OSSClient实例。
        return new OSSClientBuilder().build(cloudConfig.endpoint, cloudConfig.accessKeyId, cloudConfig.accessKeySecret);
    }

}

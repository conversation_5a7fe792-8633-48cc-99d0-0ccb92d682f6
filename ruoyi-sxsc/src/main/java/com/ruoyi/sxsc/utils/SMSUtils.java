package com.ruoyi.sxsc.utils;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.google.gson.Gson;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.config.AliSMSConfig;
import com.ruoyi.system.service.ISysUserService;
import darabonba.core.client.ClientOverrideConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Component
public class SMSUtils {

    @Autowired
    AliSMSConfig aliSMSConfig;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private RedisCache redisCache;

    private static final Logger log = LoggerFactory.getLogger(SMSUtils.class);



    /**
     * 发送手机验证码
     *
     * @param phone 手机号
     * @return 结果
     */
    public AjaxResult SendSmsCode(String phone,String type)
    {

        SysUser sysUser= userService.selectUserByUserName(phone);

        if(StringUtils.isNotNull(sysUser)&&type.equals("register")){
            return AjaxResult.error("账号已注册，请勿重复操作");
        }

        if(StringUtils.isNull(sysUser)&&type.equals("reset")){
            return AjaxResult.error("账号未注册");
        }

        String smsCode = "" + (int)((Math.random()*9+1)*100000);

        String code=redisCache.getCacheObject(CacheConstants.PHONE_CODE_KEY+phone);


        if(StringUtils.isNotEmpty(code)){
            redisCache.setCacheObject(CacheConstants.PHONE_CODE_KEY+phone,code,10, TimeUnit.MINUTES);
            sendMessage(code,phone);
        }else{
            redisCache.setCacheObject(CacheConstants.PHONE_CODE_KEY+phone,smsCode,10, TimeUnit.MINUTES);
            sendMessage(smsCode,phone);
        }
        return AjaxResult.success();
    }

    /**
     * 校验手机验证码
     *
     * @param phoneNumber 手机号
     * @param code 验证码
     * @return 结果
     */
    public void verifySmsCode(String phoneNumber,String code){

        String verifyKey = CacheConstants.PHONE_CODE_KEY + phoneNumber;

        String captcha = redisCache.getCacheObject(verifyKey);
        if(captcha == null) {
            // 抛出一个验证码过期异常
            throw new CaptchaExpireException();
        }
        if(!captcha.equals(code.trim())){
            // 抛出一个验证码错误的异常
            throw new CaptchaException();
        }
        redisCache.deleteObject(verifyKey);
    }



    private  void smsCode(String code,String phone){

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder().accessKeyId(aliSMSConfig.getAccessKeyId()).accessKeySecret(aliSMSConfig.getAccessKeySecret()) .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region("cn-beijing") // Region ID
                .credentialsProvider(provider)
                .overrideConfiguration(  ClientOverrideConfiguration.create().setEndpointOverride("dysmsapi.aliyuncs.com")).build();

        // Parameter settings for API request
        SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                .signName(aliSMSConfig.getSignName())
                .templateCode(aliSMSConfig.getTemplateCode())
                .phoneNumbers(phone)
                .templateParam("{\"code\":"+"\""+code+"\""+"}")
                .build();

        CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
        SendSmsResponse resp = null;
        try {
            resp = response.get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        log.info(new Gson().toJson(resp));
        client.close();
    }



    /**
     * 发送短信的方法
     *
     * @param phone 接收短信的手机号
     * @param code  验证码
     * @return
     */
    public boolean sendMessage(String code, String phone) {
        DefaultProfile profile = DefaultProfile.getProfile("cn-beijing", aliSMSConfig.getAccessKeyId(), aliSMSConfig.getAccessKeySecret());
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", "cn-beijing");
        request.putQueryParameter("PhoneNumbers", phone);
        request.putQueryParameter("SignName", aliSMSConfig.getSignName());
        request.putQueryParameter("TemplateCode", aliSMSConfig.getTemplateCode());
        request.putQueryParameter("TemplateParam", "{\"code\":\"" + code + "\"}");
        try {
            CommonResponse response = client.getCommonResponse(request);
            log.info(response.getData());
            String status = JSONObject.parseObject(response.getData()).get("Code").toString();
            return "OK".equals(status);
        } catch (ServerException e) {
            e.printStackTrace();
            throw new ServiceException("获取验证码失败，请联系管理员");
        } catch (ClientException e) {
            e.printStackTrace();
            throw new ServiceException("获取验证码失败，请联系管理员");
        }
    }
}

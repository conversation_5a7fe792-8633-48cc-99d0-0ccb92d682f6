package com.ruoyi.sxsc.utils;

public class ExponentialSmoothingPredictor {


        private double alpha;  // 平滑常数
        private double lastSmooth;  // 上一次平滑后的值

        public ExponentialSmoothingPredictor(double alpha) {
            if (alpha < 0 || alpha > 1) {
                throw new IllegalArgumentException("Alpha must be between 0 and 1");
            }
            this.alpha = alpha;
        }

        // 初始化平滑值
        public void init(double initialValue) {
            this.lastSmooth = initialValue;
        }

        // 添加新的观测值并更新平滑值
        public double addObservationAndGetForecast(double observation) {
            double newSmooth = alpha * observation + (1 - alpha) * lastSmooth;
            lastSmooth = newSmooth;
            return newSmooth; // 这里假设最新的平滑值即为对下一期的预测
        }

        // 预测未来n期的订单量，这里假设未来与当前趋势相同
        public double[] forecastNextNPeriods(int n) {
            double[] forecasts = new double[n];
            for (int i = 0; i < n; i++) {
                forecasts[i] = lastSmooth; // 因为是单指数平滑，且不考虑季节性或趋势变化，所以预测值都相同
            }
            return forecasts;
        }

        public static void main(String[] args) {
            // 模拟数据
            double[] orders = {100, 110, 120, 115, 130, 140};

            // 初始化预测器，alpha=0.3是常用的值，但应根据实际情况调整
            ExponentialSmoothingPredictor predictor = new ExponentialSmoothingPredictor(0.3);
            predictor.init(orders[0]); // 使用第一个观测值初始化

            // 遍历数据，更新平滑值
            for (int i = 1; i < orders.length; i++) {
                System.out.println("预测值（基于" + (i-1) + "个数据点）: " + predictor.addObservationAndGetForecast(orders[i]));
            }

            // 预测未来一周的订单量
            double[] forecasts = predictor.forecastNextNPeriods(7);
            for (int i = 0; i < forecasts.length; i++) {
                System.out.println("未来第" + (i+1) + "天的预测订单量: " + forecasts[i]);
            }
        }
}

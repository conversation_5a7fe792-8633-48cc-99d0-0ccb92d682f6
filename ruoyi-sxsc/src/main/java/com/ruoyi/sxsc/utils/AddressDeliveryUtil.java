package com.ruoyi.sxsc.utils;

import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import com.kuaidi100.sdk.api.QueryTrack;
import com.kuaidi100.sdk.core.IBaseClient;
import com.kuaidi100.sdk.pojo.HttpResult;
import com.kuaidi100.sdk.request.QueryTrackParam;
import com.kuaidi100.sdk.request.QueryTrackReq;
import com.kuaidi100.sdk.utils.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AddressDeliveryUtil {

    private static final String key = "KjjWNofD9861";
    private static final String customer = "ABABFC834B8EFEC12F9C8266A929B129";

    public static Object QueryTrack(String companyConstant,String number,String phone){
        QueryTrackReq queryTrackReq = new QueryTrackReq();
        QueryTrackParam queryTrackParam = new QueryTrackParam();
        queryTrackParam.setCom(companyConstant);
        queryTrackParam.setNum(number);
        queryTrackParam.setPhone(phone);
        queryTrackParam.setResultv2("1");
        String param = new Gson().toJson(queryTrackParam);

        queryTrackReq.setParam(param);
        queryTrackReq.setCustomer(customer);
        queryTrackReq.setSign(SignUtils.querySign(param ,key,customer));

        IBaseClient baseClient = new QueryTrack();


        try {
            HttpResult httpResult=baseClient.execute(queryTrackReq);
            if(httpResult.getStatus()==200){
                JSONObject jsonObject=JSONObject.parseObject(httpResult.getBody());
                Integer code = jsonObject.getInteger("status");
                if(code==200){
                    return jsonObject.getJSONArray("data");
                }else{
                    return "暂未查询到物流信息";
                }
            }
            return "暂未查询到物流信息";
        } catch (Exception e) {
            log.error(e.getMessage());
            return "暂未查询到物流信息";

        }
    }



}


package com.ruoyi.sxsc.utils;

import com.ruoyi.common.config.RuoYiConfig;

import java.io.File;
import java.util.Properties;
import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.*;

public class EmailSender {


    // 发件人邮箱地址和 SMTP 服务器信息
    private final static String fromEmail = "<EMAIL>";
    private final static String smtpHost = "smtp.163.com";
    private final static String smtpPort = "587";  // 通常是587或465
    private final static String smtpUsername = "<EMAIL>";
    private final static String smtpPassword = "WQYzpMtSGJVCbrSA";

    //授权码
    //

    /**
     * 发送邮件
     * fileName 文件名称
     * toEmail 收件人邮箱地址
     * */
    public static void sendEmail(String fileName,String toEmail){
        // 导出文件的路径
        String filePath = RuoYiConfig.getDownloadPath()+fileName;

        // 邮件内容
        String subject = "《富星商城》商家未发货订单";
        String body = "您好：截至当前邮件收到时间为准，贵商家在富星商城所产生的未发货订单，全部以附件的形式发送给您，请注意查收";

        // 创建邮件会话
        Properties properties = new Properties();
        properties.put("mail.smtp.host", smtpHost);
        properties.put("mail.smtp.port", smtpPort);
        properties.put("mail.smtp.auth", "true");
        //properties.put("mail.smtp.starttls.enable", "true"); // 如果使用TLS
        properties.put("mail.smtp.ssl.enable", "true"); // 如果使用SSL

        // 获取默认的 Session 对象
        Session session = Session.getInstance(properties,
                new javax.mail.Authenticator() {
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(smtpUsername, smtpPassword);
                    }
                });

        try {
            // 创建默认的 MimeMessage 对象
            Message message = new MimeMessage(session);

            // 设置发件人
            message.setFrom(new InternetAddress(fromEmail));

            // 设置收件人
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmail));

            // 设置邮件主题
            message.setSubject(subject);

            // 创建消息部分
            BodyPart messageBodyPart = new MimeBodyPart();

            // 设置文本消息
            messageBodyPart.setText(body);

            // 创建多部分消息
            Multipart multipart = new MimeMultipart();

            // 设置文本消息部分
            multipart.addBodyPart(messageBodyPart);

            // 第二部分消息是附件
            messageBodyPart = new MimeBodyPart();
            String filename = new File(filePath).getName();
            DataSource source = new FileDataSource(filePath);
            messageBodyPart.setDataHandler(new DataHandler(source));
            messageBodyPart.setHeader("Content-ID", "<" + filename + ">");
            messageBodyPart.setFileName(filename);

            // 将附件添加到多部分消息中
            multipart.addBodyPart(messageBodyPart);

            // 发送完整的消息部分
            message.setContent(multipart);

            // 发送消息
            Transport.send(message);

            System.out.println("邮件发送成功....");

        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }
    }
}

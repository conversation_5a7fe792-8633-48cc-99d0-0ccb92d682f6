package com.ruoyi.sxsc.utils;


import com.aliyun.cloudauth20190307.Client;
import com.aliyun.cloudauth20190307.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.ruoyi.sxsc.config.AliAccessConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class IdCardUtil {

    @Autowired
    AliAccessConfig aliAccessConfig;


    /**
     * 身份证号验证
     *
     * @param identifyName
     * @param identifyNum
     * @return 结果
     */
    public Id2MetaVerifyResponseBody identifyAuthentication(String identifyName,String identifyNum){

        // 通过以下代码创建API请求并设置参数。
        Id2MetaVerifyRequest request = new Id2MetaVerifyRequest();

        request.setParamType("normal");
        request.setUserName(identifyName);
        request.setIdentifyNum(identifyNum);

        // 推荐，支持服务路由。
        Id2MetaVerifyResponse response = id2MetaVerifyAutoRoute(request);

        return  response.getBody();
    }

    /**
     * 银行卡验证
     *
     * @param identifyName
     * @param identifyNum
     * @param bankCard
     * @return 结果
     */
    public  BankMetaVerifyResponseBody bankCardVerification(String identifyName, String identifyNum, String bankCard){

        // 通过以下代码创建API请求并设置参数。
        BankMetaVerifyRequest request = new BankMetaVerifyRequest();

        request.setParamType("normal");
        request.setProductType("BANK_CARD_3_META");
        request.setVerifyMode("VERIFY_BANK_CARD");
        request.setUserName(identifyName);
        request.setIdentifyNum(identifyNum);
        request.setBankCard(bankCard);

        // 推荐，支持服务路由。
        BankMetaVerifyResponse response = bankMetaVerifyAutoRoute(request);

        return response.getBody();
    }

    private  Id2MetaVerifyResponse id2MetaVerifyAutoRoute(Id2MetaVerifyRequest request) {
        // 第一个为主区域Endpoint，第二个为备区域Endpoint。
        List<String> endpoints = Arrays.asList("cloudauth.cn-shanghai.aliyuncs.com", "cloudauth.cn-beijing.aliyuncs.com");
        Id2MetaVerifyResponse lastResponse = null;
        for (int i=0; i<endpoints.size(); i++) {
            try {
                Id2MetaVerifyResponse response = id2MetaVerify(endpoints.get(i), request);
                lastResponse = response;

                // 服务端错误，切换到下个区域调用。
                if(response != null){
                    if(500 == response.getStatusCode()){
                        continue;
                    }
                    if(response.getBody() != null){
                        if("500".equals(response.getBody().getCode())){
                            continue;
                        }
                    }
                }

                return response;
            } catch (Exception e) {
                e.printStackTrace();
                if(i == endpoints.size()-1){
                    throw new RuntimeException(e);
                }
            }
        }

        return lastResponse;
    }


    private  BankMetaVerifyResponse bankMetaVerifyAutoRoute(BankMetaVerifyRequest request) {
        // 第一个为主区域Endpoint，第二个为备区域Endpoint。
        List<String> endpoints = Arrays.asList("cloudauth.cn-shanghai.aliyuncs.com", "cloudauth.cn-beijing.aliyuncs.com");
        BankMetaVerifyResponse lastResponse = null;
        for (int i=0; i<endpoints.size(); i++) {
            try {
                BankMetaVerifyResponse response = bankMetaVerify(endpoints.get(i), request);
                lastResponse = response;

                // 服务端错误，切换到下个区域调用。
                if(response != null){
                    if(500 == response.getStatusCode()){
                        continue;
                    }
                    if(response.getBody() != null){
                        if("500".equals(response.getBody().getCode())){
                            continue;
                        }
                    }
                }

                return response;
            } catch (Exception e) {
                e.printStackTrace();
                if(i == endpoints.size()-1){
                    throw new RuntimeException(e);
                }
            }
        }

        return lastResponse;
    }


    private  Id2MetaVerifyResponse id2MetaVerify(String endpoint, Id2MetaVerifyRequest request)
            throws Exception {
        // 阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
        // 强烈建议不要把AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。
        // 本示例通过阿里云Credentials工具从环境变量中读取AccessKey，来实现API访问的身份验证。

        Config config = new Config();
        config.setType("access_key");
        config.setAccessKeyId(aliAccessConfig.getAccessKeyId());
        config.setAccessKeySecret(aliAccessConfig.getAccessKeySecret());
        // 设置http代理。
        //config.setHttpProxy("http://xx.xx.xx.xx:xxxx");
        // 设置https代理。
        //config.setHttpsProxy("http://xx.xx.xx.xx:xxxx");
        Client client = new Client(config);

        // 创建RuntimeObject实例并设置运行参数。
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 5000;
        runtime.connectTimeout = 5000;

        return client.id2MetaVerifyWithOptions(request, runtime);
    }



    private  BankMetaVerifyResponse bankMetaVerify(String endpoint, BankMetaVerifyRequest request)
            throws Exception {
        // 阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
        // 强烈建议不要把AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。
        // 本示例通过阿里云Credentials工具从环境变量中读取AccessKey，来实现API访问的身份验证。如何配置环境变量，请参见https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.credentials.Client credentialClient = new com.aliyun.credentials.Client();
        Config config = new Config();
        config.setType("access_key");
        config.setAccessKeyId(aliAccessConfig.getAccessKeyId());
        config.setAccessKeySecret(aliAccessConfig.getAccessKeySecret());
        // 设置http代理。
        //config.setHttpProxy("http://xx.xx.xx.xx:xxxx");
        // 设置https代理。
        //config.setHttpsProxy("http://xx.xx.xx.xx:xxxx");
        Client client = new Client(config);

        // 创建RuntimeObject实例并设置运行参数。
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 5000;
        runtime.connectTimeout = 5000;

        return client.bankMetaVerifyWithOptions(request, runtime);
    }




}

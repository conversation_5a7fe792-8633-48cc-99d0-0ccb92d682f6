package com.ruoyi.sxsc.operations.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 投诉反馈对象
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscProblemFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 父级id */
    private Long parentId;

    /** 用户主键 */
    private Long userId;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 图片地址，逗号隔开 */
    @Excel(name = "图片地址，逗号隔开")
    private String imgUrl;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contact;

    /** 状态（0正常 1关闭） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=关闭")
    private String status;


    /** 回复信息 */
    @TableField(exist = false)
    private List<SxscProblemFeedback> feedbackList;


}

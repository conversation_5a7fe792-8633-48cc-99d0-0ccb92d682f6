package com.ruoyi.sxsc.operations.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 隐私协议对象
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscPrivacyAgreement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 标题名称 */
    @Excel(name = "标题名称")
    private String name;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 状态（0正常 1关闭） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=关闭")
    private String status;




}

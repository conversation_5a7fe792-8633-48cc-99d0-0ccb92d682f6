package com.ruoyi.sxsc.operations.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.operations.domain.SxscProblemFeedback;
import com.ruoyi.sxsc.operations.service.ISxscProblemFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 投诉反馈
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@RestController
@RequestMapping("/operations/feedback")
public class SxscProblemFeedbackController extends BaseController
{
    @Autowired
    private ISxscProblemFeedbackService sxscProblemFeedbackService;

    /**
     * 查询投诉反馈列表
     */
    @PreAuthorize("@ss.hasPermi('operations:feedback:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscProblemFeedback sxscProblemFeedback)
    {
        startPage();
        List<SxscProblemFeedback> list = sxscProblemFeedbackService.selectSxscProblemFeedbackList(sxscProblemFeedback);
        return getDataTable(list);
    }

    /**
     * 获取投诉反馈详细信息
     */
    @PreAuthorize("@ss.hasPermi('operations:feedback:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscProblemFeedbackService.selectSxscProblemFeedbackById(id));
    }

    /**
     * 新增投诉反馈
     */
    @PreAuthorize("@ss.hasPermi('operations:feedback:add')")
    @Log(title = "投诉反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscProblemFeedback sxscProblemFeedback)
    {
        return toAjax(sxscProblemFeedbackService.insertSxscProblemFeedback(sxscProblemFeedback));
    }

    /**
     * 修改投诉反馈
     */
    @PreAuthorize("@ss.hasPermi('operations:feedback:edit')")
    @Log(title = "投诉反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscProblemFeedback sxscProblemFeedback)
    {
        return toAjax(sxscProblemFeedbackService.updateSxscProblemFeedback(sxscProblemFeedback));
    }

    /**
     * 删除投诉反馈
     */
    @PreAuthorize("@ss.hasPermi('operations:feedback:remove')")
    @Log(title = "投诉反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscProblemFeedbackService.deleteSxscProblemFeedbackByIds(ids));
    }
}

package com.ruoyi.sxsc.operations.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 金刚区对象
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscJingangDistrict extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 图片地址 */
    @Excel(name = "图片地址")
    private String imgUrl;

    /** 页面跳转地址 */
    @Excel(name = "页面跳转地址")
    private String pageUrl;

    /** 是否展示1是0否 */
    @Excel(name = "是否展示1是0否")
    private Long status;

    /** 是否删除1删除0未删除 */
    private Long delFlag;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;




}

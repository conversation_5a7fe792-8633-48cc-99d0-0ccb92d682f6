package com.ruoyi.sxsc.operations.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.operations.domain.SxscPrivacyAgreement;
import com.ruoyi.sxsc.operations.service.ISxscPrivacyAgreementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 隐私协议
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@RestController
@RequestMapping("/operations/agreement")
public class SxscPrivacyAgreementController extends BaseController
{
    @Autowired
    private ISxscPrivacyAgreementService sxscPrivacyAgreementService;

    /**
     * 查询隐私协议列表
     */
    @PreAuthorize("@ss.hasPermi('operations:agreement:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscPrivacyAgreement sxscPrivacyAgreement)
    {
        startPage();
        List<SxscPrivacyAgreement> list = sxscPrivacyAgreementService.selectSxscPrivacyAgreementList(sxscPrivacyAgreement);
        return getDataTable(list);
    }

    /**
     * 获取隐私协议详细信息
     */
    @PreAuthorize("@ss.hasPermi('operations:agreement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscPrivacyAgreementService.selectSxscPrivacyAgreementById(id));
    }

    /**
     * 根据标题获取隐私协议详细信息
     */
    @GetMapping(value = "/title/{name}")
    public AjaxResult getInfo(@PathVariable("name") String name)
    {
        return success(sxscPrivacyAgreementService.selectSxscPrivacyAgreementByName(name));
    }

    /**
     * 新增隐私协议
     */
    @PreAuthorize("@ss.hasPermi('operations:agreement:add')")
    @Log(title = "隐私协议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscPrivacyAgreement sxscPrivacyAgreement)
    {
        return sxscPrivacyAgreementService.insertSxscPrivacyAgreement(sxscPrivacyAgreement);
    }

    /**
     * 修改隐私协议
     */
    @PreAuthorize("@ss.hasPermi('operations:agreement:edit')")
    @Log(title = "隐私协议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscPrivacyAgreement sxscPrivacyAgreement)
    {
        return toAjax(sxscPrivacyAgreementService.updateSxscPrivacyAgreement(sxscPrivacyAgreement));
    }

    /**
     * 删除隐私协议
     */
    @PreAuthorize("@ss.hasPermi('operations:agreement:remove')")
    @Log(title = "隐私协议", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscPrivacyAgreementService.deleteSxscPrivacyAgreementByIds(ids));
    }
}

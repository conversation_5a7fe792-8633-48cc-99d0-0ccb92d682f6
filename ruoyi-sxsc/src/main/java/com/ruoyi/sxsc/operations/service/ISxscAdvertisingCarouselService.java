package com.ruoyi.sxsc.operations.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.operations.domain.SxscAdvertisingCarousel;

import java.util.List;

/**
 * 首页轮播图Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
public interface ISxscAdvertisingCarouselService extends IService<SxscAdvertisingCarousel>
{
    /**
     * 查询首页轮播图
     * 
     * @param id 首页轮播图主键
     * @return 首页轮播图
     */
    SxscAdvertisingCarousel selectSxscAdvertisingCarouselById(Long id);

    /**
     * 查询首页轮播图列表
     * 
     * @param sxscAdvertisingCarousel 首页轮播图
     * @return 首页轮播图集合
     */
    List<SxscAdvertisingCarousel> selectSxscAdvertisingCarouselList(SxscAdvertisingCarousel sxscAdvertisingCarousel);

    /**
     * 新增首页轮播图
     * 
     * @param sxscAdvertisingCarousel 首页轮播图
     * @return 结果
     */
    int insertSxscAdvertisingCarousel(SxscAdvertisingCarousel sxscAdvertisingCarousel);

    /**
     * 修改首页轮播图
     * 
     * @param sxscAdvertisingCarousel 首页轮播图
     * @return 结果
     */
    int updateSxscAdvertisingCarousel(SxscAdvertisingCarousel sxscAdvertisingCarousel);

    /**
     * 批量删除首页轮播图
     * 
     * @param ids 需要删除的首页轮播图主键集合
     * @return 结果
     */
    int deleteSxscAdvertisingCarouselByIds(Long[] ids);


}

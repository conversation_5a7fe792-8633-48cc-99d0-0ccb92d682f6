package com.ruoyi.sxsc.operations.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.operations.domain.SxscJingangDistrict;

import java.util.List;

/**
 * 金刚区Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
public interface ISxscJingangDistrictService extends IService<SxscJingangDistrict>
{
    /**
     * 查询金刚区
     * 
     * @param id 金刚区主键
     * @return 金刚区
     */
    SxscJingangDistrict selectSxscJingangDistrictById(Long id);

    /**
     * 查询金刚区列表
     * 
     * @param sxscJingangDistrict 金刚区
     * @return 金刚区集合
     */
    List<SxscJingangDistrict> selectSxscJingangDistrictList(SxscJingangDistrict sxscJingangDistrict);

    /**
     * 新增金刚区
     * 
     * @param sxscJingangDistrict 金刚区
     * @return 结果
     */
    int insertSxscJingangDistrict(SxscJingangDistrict sxscJingangDistrict);

    /**
     * 修改金刚区
     * 
     * @param sxscJingangDistrict 金刚区
     * @return 结果
     */
    int updateSxscJingangDistrict(SxscJingangDistrict sxscJingangDistrict);

    /**
     * 批量删除金刚区
     * 
     * @param ids 需要删除的金刚区主键集合
     * @return 结果
     */
    int deleteSxscJingangDistrictByIds(Long[] ids);


}

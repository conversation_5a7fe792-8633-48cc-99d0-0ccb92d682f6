package com.ruoyi.sxsc.operations.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.operations.domain.SxscAdvertisingCarousel;
import com.ruoyi.sxsc.operations.mapper.SxscAdvertisingCarouselMapper;
import com.ruoyi.sxsc.operations.service.ISxscAdvertisingCarouselService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 首页轮播图Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@Service
public class SxscAdvertisingCarouselServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscAdvertisingCarouselMapper,SxscAdvertisingCarousel> implements ISxscAdvertisingCarouselService
{

    /**
     * 查询首页轮播图
     * 
     * @param id 首页轮播图主键
     * @return 首页轮播图
     */
    @Override
    public SxscAdvertisingCarousel selectSxscAdvertisingCarouselById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询首页轮播图列表
     * 
     * @param sxscAdvertisingCarousel 首页轮播图
     * @return 首页轮播图
     */
    @Override
    public List<SxscAdvertisingCarousel> selectSxscAdvertisingCarouselList(SxscAdvertisingCarousel sxscAdvertisingCarousel)
    {
        LambdaQueryWrapper<SxscAdvertisingCarousel> wrapper=new LambdaQueryWrapper();

        wrapper.like(StringUtils.isNotNull(sxscAdvertisingCarousel.getPageUrl()),SxscAdvertisingCarousel::getPageUrl,sxscAdvertisingCarousel.getPageUrl());

        wrapper.eq(StringUtils.isNotNull(sxscAdvertisingCarousel.getStatus()),SxscAdvertisingCarousel::getStatus,sxscAdvertisingCarousel.getStatus());

        wrapper.eq(SxscAdvertisingCarousel::getDelFlag,0l);

        wrapper.orderByAsc(SxscAdvertisingCarousel::getSort);

        return list(wrapper);
    }

    /**
     * 新增首页轮播图
     * 
     * @param sxscAdvertisingCarousel 首页轮播图
     * @return 结果
     */
    @Override
    public int insertSxscAdvertisingCarousel(SxscAdvertisingCarousel sxscAdvertisingCarousel)
    {
        sxscAdvertisingCarousel.setDelFlag(0l);
        sxscAdvertisingCarousel.setCreateBy(SecurityUtils.getUsername());
        sxscAdvertisingCarousel.setCreateTime(DateUtils.getNowDate());
        return save(sxscAdvertisingCarousel)?1:0;
    }

    /**
     * 修改首页轮播图
     * 
     * @param sxscAdvertisingCarousel 首页轮播图
     * @return 结果
     */
    @Override
    public int updateSxscAdvertisingCarousel(SxscAdvertisingCarousel sxscAdvertisingCarousel)
    {
        sxscAdvertisingCarousel.setUpdateBy(SecurityUtils.getUsername());
        sxscAdvertisingCarousel.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscAdvertisingCarousel)?1:0;
    }

    /**
     * 批量删除首页轮播图
     * 
     * @param ids 需要删除的首页轮播图主键
     * @return 结果
     */
    @Override
    public int deleteSxscAdvertisingCarouselByIds(Long[] ids)
    {
        LambdaUpdateWrapper<SxscAdvertisingCarousel> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscAdvertisingCarousel::getDelFlag,1);
        updateWrapper.set(SxscAdvertisingCarousel::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscAdvertisingCarousel::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.in(SxscAdvertisingCarousel::getId,ids);
        return update(updateWrapper)?1:0;
    }


}

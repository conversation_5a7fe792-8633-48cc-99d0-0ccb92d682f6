package com.ruoyi.sxsc.operations.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.operations.domain.SxscActivity;
import com.ruoyi.sxsc.operations.mapper.SxscActivityMapper;
import com.ruoyi.sxsc.operations.service.ISxscActivityService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 首页活动区Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Service
public class SxscActivityServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscActivityMapper,SxscActivity> implements ISxscActivityService
{

    /**
     * 查询首页活动区
     * 
     * @param id 首页活动区主键
     * @return 首页活动区
     */
    @Override
    public SxscActivity selectSxscActivityById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询首页活动区列表
     * 
     * @param sxscActivity 首页活动区
     * @return 首页活动区
     */
    @Override
    public List<SxscActivity> selectSxscActivityList(SxscActivity sxscActivity)
    {
        LambdaQueryWrapper<SxscActivity> wrapper=new LambdaQueryWrapper();

        wrapper.like(StringUtils.isNotNull(sxscActivity.getTitle()),SxscActivity::getTitle,sxscActivity.getTitle());

        wrapper.eq(StringUtils.isNotNull(sxscActivity.getPageUrl()),SxscActivity::getPageUrl,sxscActivity.getPageUrl());

        wrapper.eq(StringUtils.isNotNull(sxscActivity.getStatus()),SxscActivity::getStatus,sxscActivity.getStatus());

        wrapper.eq(StringUtils.isNotNull(sxscActivity.getPosition()),SxscActivity::getPosition,sxscActivity.getPosition());

        wrapper.eq(SxscActivity::getDelFlag,0l);

        wrapper.orderByDesc(SxscActivity::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增首页活动区
     * 
     * @param sxscActivity 首页活动区
     * @return 结果
     */
    @Override
    public int insertSxscActivity(SxscActivity sxscActivity)
    {
        sxscActivity.setDelFlag(0l);
        sxscActivity.setCreateBy(SecurityUtils.getUsername());
        sxscActivity.setCreateTime(DateUtils.getNowDate());
        return save(sxscActivity)?1:0;
    }

    /**
     * 修改首页活动区
     * 
     * @param sxscActivity 首页活动区
     * @return 结果
     */
    @Override
    public int updateSxscActivity(SxscActivity sxscActivity)
    {
        sxscActivity.setUpdateBy(SecurityUtils.getUsername());
        sxscActivity.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscActivity)?1:0;
    }

    /**
     * 批量删除首页活动区
     * 
     * @param ids 需要删除的首页活动区主键
     * @return 结果
     */
    @Override
    public int deleteSxscActivityByIds(Long[] ids)
    {
        LambdaUpdateWrapper<SxscActivity> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscActivity::getDelFlag,1);
        updateWrapper.set(SxscActivity::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscActivity::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.in(SxscActivity::getId,ids);
        return update(updateWrapper)?1:0;
    }


}

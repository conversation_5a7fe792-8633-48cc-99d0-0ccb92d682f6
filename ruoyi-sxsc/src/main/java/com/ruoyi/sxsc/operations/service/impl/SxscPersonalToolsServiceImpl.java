package com.ruoyi.sxsc.operations.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.operations.domain.SxscPersonalTools;
import com.ruoyi.sxsc.operations.mapper.SxscPersonalToolsMapper;
import com.ruoyi.sxsc.operations.service.ISxscPersonalToolsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 个人工具信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
public class SxscPersonalToolsServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscPersonalToolsMapper,SxscPersonalTools> implements ISxscPersonalToolsService
{

    /**
     * 查询个人工具信息
     * 
     * @param id 个人工具信息主键
     * @return 个人工具信息
     */
    @Override
    public SxscPersonalTools selectSxscPersonalToolsById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询个人工具信息列表
     * 
     * @param sxscPersonalTools 个人工具信息
     * @return 个人工具信息
     */
    @Override
    public List<SxscPersonalTools> selectSxscPersonalToolsList(SxscPersonalTools sxscPersonalTools)
    {
        LambdaQueryWrapper<SxscPersonalTools> wrapper=new LambdaQueryWrapper();

        wrapper.like(StringUtils.isNotNull(sxscPersonalTools.getTitle()),SxscPersonalTools::getTitle,sxscPersonalTools.getTitle());

        wrapper.like(StringUtils.isNotNull(sxscPersonalTools.getPageUrl()),SxscPersonalTools::getPageUrl,sxscPersonalTools.getPageUrl());

        wrapper.eq(StringUtils.isNotNull(sxscPersonalTools.getStatus()),SxscPersonalTools::getStatus,sxscPersonalTools.getStatus());

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscPersonalTools::getType,SecurityUtils.getLoginUser().getUser().getUserType());
        }else{
            wrapper.eq(StringUtils.isNotNull(sxscPersonalTools.getType()),SxscPersonalTools::getType,sxscPersonalTools.getType());
        }

        wrapper.eq(SxscPersonalTools::getDelFlag,0l);

        wrapper.orderByAsc(SxscPersonalTools::getSort);

        return list(wrapper);
    }

    /**
     * 新增个人工具信息
     * 
     * @param sxscPersonalTools 个人工具信息
     * @return 结果
     */
    @Override
    public int insertSxscPersonalTools(SxscPersonalTools sxscPersonalTools)
    {
        sxscPersonalTools.setDelFlag(0l);
        sxscPersonalTools.setCreateBy(SecurityUtils.getUsername());
        sxscPersonalTools.setCreateTime(DateUtils.getNowDate());
        return save(sxscPersonalTools)?1:0;
    }

    /**
     * 修改个人工具信息
     * 
     * @param sxscPersonalTools 个人工具信息
     * @return 结果
     */
    @Override
    public int updateSxscPersonalTools(SxscPersonalTools sxscPersonalTools)
    {
        sxscPersonalTools.setUpdateBy(SecurityUtils.getUsername());
        sxscPersonalTools.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscPersonalTools)?1:0;
    }

    /**
     * 批量删除个人工具信息
     * 
     * @param ids 需要删除的个人工具信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscPersonalToolsByIds(Long[] ids)
    {
        LambdaUpdateWrapper<SxscPersonalTools> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscPersonalTools::getDelFlag,1);
        updateWrapper.set(SxscPersonalTools::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscPersonalTools::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.in(SxscPersonalTools::getId,ids);
        return update(updateWrapper)?1:0;
    }


}

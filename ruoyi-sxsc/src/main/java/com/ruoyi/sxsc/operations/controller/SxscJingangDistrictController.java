package com.ruoyi.sxsc.operations.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.operations.domain.SxscJingangDistrict;
import com.ruoyi.sxsc.operations.service.ISxscJingangDistrictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 首页金刚区
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@RestController
@RequestMapping("/operations/district")
public class SxscJingangDistrictController extends BaseController
{
    @Autowired
    private ISxscJingangDistrictService sxscJingangDistrictService;

    /**
     * 查询首页金刚区列表
     */
    @PreAuthorize("@ss.hasPermi('operations:district:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscJingangDistrict sxscJingangDistrict)
    {
        startPage();
        List<SxscJingangDistrict> list = sxscJingangDistrictService.selectSxscJingangDistrictList(sxscJingangDistrict);
        return getDataTable(list);
    }

    /**
     * 导出首页金刚区列表
     */
    @PreAuthorize("@ss.hasPermi('operations:district:export')")
    @Log(title = "首页金刚区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscJingangDistrict sxscJingangDistrict)
    {
        List<SxscJingangDistrict> list = sxscJingangDistrictService.selectSxscJingangDistrictList(sxscJingangDistrict);
        ExcelUtil<SxscJingangDistrict> util = new ExcelUtil<SxscJingangDistrict>(SxscJingangDistrict.class);
        util.exportExcel(response, list, "首页金刚区数据");
    }

    /**
     * 获取首页金刚区详细信息
     */
    @PreAuthorize("@ss.hasPermi('operations:district:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscJingangDistrictService.selectSxscJingangDistrictById(id));
    }

    /**
     * 新增首页金刚区
     */
    @PreAuthorize("@ss.hasPermi('operations:district:add')")
    @Log(title = "首页金刚区", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscJingangDistrict sxscJingangDistrict)
    {
        return toAjax(sxscJingangDistrictService.insertSxscJingangDistrict(sxscJingangDistrict));
    }

    /**
     * 修改首页金刚区
     */
    @PreAuthorize("@ss.hasPermi('operations:district:edit')")
    @Log(title = "首页金刚区", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscJingangDistrict sxscJingangDistrict)
    {
        return toAjax(sxscJingangDistrictService.updateSxscJingangDistrict(sxscJingangDistrict));
    }

    /**
     * 删除首页金刚区
     */
    @PreAuthorize("@ss.hasPermi('operations:district:remove')")
    @Log(title = "首页金刚区", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscJingangDistrictService.deleteSxscJingangDistrictByIds(ids));
    }
}

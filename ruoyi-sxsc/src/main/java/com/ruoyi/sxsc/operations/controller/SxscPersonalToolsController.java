package com.ruoyi.sxsc.operations.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.operations.domain.SxscPersonalTools;
import com.ruoyi.sxsc.operations.service.ISxscPersonalToolsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 个人工具信息
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@RestController
@RequestMapping("/operations/tools")
public class SxscPersonalToolsController extends BaseController
{
    @Autowired
    private ISxscPersonalToolsService sxscPersonalToolsService;

    /**
     * 查询个人工具信息列表
     */
    @PreAuthorize("@ss.hasPermi('operations:tools:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscPersonalTools sxscPersonalTools)
    {
        startPage();
        List<SxscPersonalTools> list = sxscPersonalToolsService.selectSxscPersonalToolsList(sxscPersonalTools);
        return getDataTable(list);
    }

    /**
     * 获取个人工具信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('operations:tools:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscPersonalToolsService.selectSxscPersonalToolsById(id));
    }

    /**
     * 新增个人工具信息
     */
    @PreAuthorize("@ss.hasPermi('operations:tools:add')")
    @Log(title = "个人工具信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscPersonalTools sxscPersonalTools)
    {
        return toAjax(sxscPersonalToolsService.insertSxscPersonalTools(sxscPersonalTools));
    }

    /**
     * 修改个人工具信息
     */
    @PreAuthorize("@ss.hasPermi('operations:tools:edit')")
    @Log(title = "个人工具信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscPersonalTools sxscPersonalTools)
    {
        return toAjax(sxscPersonalToolsService.updateSxscPersonalTools(sxscPersonalTools));
    }

    /**
     * 删除个人工具信息
     */
    @PreAuthorize("@ss.hasPermi('operations:tools:remove')")
    @Log(title = "个人工具信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscPersonalToolsService.deleteSxscPersonalToolsByIds(ids));
    }
}

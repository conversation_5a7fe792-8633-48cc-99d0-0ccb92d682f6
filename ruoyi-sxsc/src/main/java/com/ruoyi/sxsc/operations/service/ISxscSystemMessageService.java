package com.ruoyi.sxsc.operations.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.operations.domain.SxscSystemMessage;

import java.util.List;

/**
 * 首页消息区Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
public interface ISxscSystemMessageService extends IService<SxscSystemMessage>
{
    /**
     * 查询首页消息区
     * 
     * @param id 首页消息区主键
     * @return 首页消息区
     */
    SxscSystemMessage selectSxscSystemMessageById(Long id);

    /**
     * 查询首页消息区列表
     * 
     * @param sxscSystemMessage 首页消息区
     * @return 首页消息区集合
     */
    List<SxscSystemMessage> selectSxscSystemMessageList(SxscSystemMessage sxscSystemMessage);

    /**
     * 新增首页消息区
     * 
     * @param sxscSystemMessage 首页消息区
     * @return 结果
     */
    int insertSxscSystemMessage(SxscSystemMessage sxscSystemMessage);

    /**
     * 修改首页消息区
     * 
     * @param sxscSystemMessage 首页消息区
     * @return 结果
     */
    int updateSxscSystemMessage(SxscSystemMessage sxscSystemMessage);

    /**
     * 批量删除首页消息区
     * 
     * @param ids 需要删除的首页消息区主键集合
     * @return 结果
     */
    int deleteSxscSystemMessageByIds(Long[] ids);


}

package com.ruoyi.sxsc.operations.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.operations.domain.SxscJingangDistrict;
import com.ruoyi.sxsc.operations.mapper.SxscJingangDistrictMapper;
import com.ruoyi.sxsc.operations.service.ISxscJingangDistrictService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 金刚区Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@Service
public class SxscJingangDistrictServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscJingangDistrictMapper,SxscJingangDistrict> implements ISxscJingangDistrictService
{

    /**
     * 查询金刚区
     * 
     * @param id 金刚区主键
     * @return 金刚区
     */
    @Override
    public SxscJingangDistrict selectSxscJingangDistrictById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询金刚区列表
     * 
     * @param sxscJingangDistrict 金刚区
     * @return 金刚区
     */
    @Override
    public List<SxscJingangDistrict> selectSxscJingangDistrictList(SxscJingangDistrict sxscJingangDistrict)
    {
        LambdaQueryWrapper<SxscJingangDistrict> wrapper=new LambdaQueryWrapper();

        wrapper.like(StringUtils.isNotNull(sxscJingangDistrict.getTitle()),SxscJingangDistrict::getTitle,sxscJingangDistrict.getTitle());

        wrapper.like(StringUtils.isNotNull(sxscJingangDistrict.getPageUrl()),SxscJingangDistrict::getPageUrl,sxscJingangDistrict.getPageUrl());

        wrapper.eq(StringUtils.isNotNull(sxscJingangDistrict.getStatus()),SxscJingangDistrict::getStatus,sxscJingangDistrict.getStatus());

        wrapper.eq(SxscJingangDistrict::getDelFlag,0l);

        wrapper.orderByAsc(SxscJingangDistrict::getSort);

        return list(wrapper);
    }

    /**
     * 新增金刚区
     * 
     * @param sxscJingangDistrict 金刚区
     * @return 结果
     */
    @Override
    public int insertSxscJingangDistrict(SxscJingangDistrict sxscJingangDistrict)
    {
        sxscJingangDistrict.setDelFlag(0l);
        sxscJingangDistrict.setCreateBy(SecurityUtils.getUsername());
        sxscJingangDistrict.setCreateTime(DateUtils.getNowDate());
        return save(sxscJingangDistrict)?1:0;
    }

    /**
     * 修改金刚区
     * 
     * @param sxscJingangDistrict 金刚区
     * @return 结果
     */
    @Override
    public int updateSxscJingangDistrict(SxscJingangDistrict sxscJingangDistrict)
    {
        sxscJingangDistrict.setUpdateBy(SecurityUtils.getUsername());
        sxscJingangDistrict.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscJingangDistrict)?1:0;
    }

    /**
     * 批量删除金刚区
     * 
     * @param ids 需要删除的金刚区主键
     * @return 结果
     */
    @Override
    public int deleteSxscJingangDistrictByIds(Long[] ids)
    {
        LambdaUpdateWrapper<SxscJingangDistrict> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscJingangDistrict::getDelFlag,1);
        updateWrapper.set(SxscJingangDistrict::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscJingangDistrict::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.in(SxscJingangDistrict::getId,ids);
        return update(updateWrapper)?1:0;
    }

}

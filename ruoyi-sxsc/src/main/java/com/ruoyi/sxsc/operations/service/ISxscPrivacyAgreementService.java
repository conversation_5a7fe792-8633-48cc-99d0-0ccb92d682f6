package com.ruoyi.sxsc.operations.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.operations.domain.SxscPrivacyAgreement;

import java.util.List;

/**
 * 隐私协议Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
public interface ISxscPrivacyAgreementService extends IService<SxscPrivacyAgreement>
{
    /**
     * 查询隐私协议
     * 
     * @param id 隐私协议主键
     * @return 隐私协议
     */
    SxscPrivacyAgreement selectSxscPrivacyAgreementById(Long id);

    /**
     * 查询隐私协议
     *
     * @param name 隐私协议主键
     * @return 隐私协议
     */
    SxscPrivacyAgreement selectSxscPrivacyAgreementByName(String name);

    /**
     * 查询隐私协议列表
     * 
     * @param sxscPrivacyAgreement 隐私协议
     * @return 隐私协议集合
     */
    List<SxscPrivacyAgreement> selectSxscPrivacyAgreementList(SxscPrivacyAgreement sxscPrivacyAgreement);

    /**
     * 新增隐私协议
     * 
     * @param sxscPrivacyAgreement 隐私协议
     * @return 结果
     */
    AjaxResult insertSxscPrivacyAgreement(SxscPrivacyAgreement sxscPrivacyAgreement);

    /**
     * 修改隐私协议
     * 
     * @param sxscPrivacyAgreement 隐私协议
     * @return 结果
     */
    int updateSxscPrivacyAgreement(SxscPrivacyAgreement sxscPrivacyAgreement);

    /**
     * 批量删除隐私协议
     * 
     * @param ids 需要删除的隐私协议主键集合
     * @return 结果
     */
    int deleteSxscPrivacyAgreementByIds(Long[] ids);

    /**
     * 删除隐私协议信息
     * 
     * @param id 隐私协议主键
     * @return 结果
     */
    int deleteSxscPrivacyAgreementById(Long id);
}

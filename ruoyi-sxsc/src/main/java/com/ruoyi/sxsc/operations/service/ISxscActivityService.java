package com.ruoyi.sxsc.operations.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.operations.domain.SxscActivity;

import java.util.List;

/**
 * 首页活动区Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
public interface ISxscActivityService extends IService<SxscActivity>
{
    /**
     * 查询首页活动区
     * 
     * @param id 首页活动区主键
     * @return 首页活动区
     */
    SxscActivity selectSxscActivityById(Long id);

    /**
     * 查询首页活动区列表
     * 
     * @param sxscActivity 首页活动区
     * @return 首页活动区集合
     */
    List<SxscActivity> selectSxscActivityList(SxscActivity sxscActivity);

    /**
     * 新增首页活动区
     * 
     * @param sxscActivity 首页活动区
     * @return 结果
     */
    int insertSxscActivity(SxscActivity sxscActivity);

    /**
     * 修改首页活动区
     * 
     * @param sxscActivity 首页活动区
     * @return 结果
     */
    int updateSxscActivity(SxscActivity sxscActivity);

    /**
     * 批量删除首页活动区
     * 
     * @param ids 需要删除的首页活动区主键集合
     * @return 结果
     */
    int deleteSxscActivityByIds(Long[] ids);


}

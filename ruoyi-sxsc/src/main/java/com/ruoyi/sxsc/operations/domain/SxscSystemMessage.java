package com.ruoyi.sxsc.operations.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 首页消息区对象
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscSystemMessage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 消息类型 */
    @Excel(name = "消息类型")
    private Long type;

    /** 消息标题 */
    @Excel(name = "消息标题")
    private String title;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String message;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 是否展示 */
    @Excel(name = "是否展示")
    private Long status;

    /** 是否删除1删除0未删除 */
    private Long delFlag;



    public SxscSystemMessage(String title,String message){
        this.delFlag=0l;
        this.sort=0l;
        this.status=1l;
        this.type=1l;
        this.title=title;
        this.message=message;
    }

}

package com.ruoyi.sxsc.operations.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.operations.domain.SxscProblemFeedback;

import java.util.List;

/**
 * 投诉反馈Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public interface ISxscProblemFeedbackService extends IService<SxscProblemFeedback>
{
    /**
     * 查询投诉反馈
     * 
     * @param id 投诉反馈主键
     * @return 投诉反馈
     */
    SxscProblemFeedback selectSxscProblemFeedbackById(Long id);

    /**
     * 查询投诉反馈列表
     * 
     * @param sxscProblemFeedback 投诉反馈
     * @return 投诉反馈集合
     */
    List<SxscProblemFeedback> selectSxscProblemFeedbackList(SxscProblemFeedback sxscProblemFeedback);

    /**
     * 新增投诉反馈
     * 
     * @param sxscProblemFeedback 投诉反馈
     * @return 结果
     */
    int insertSxscProblemFeedback(SxscProblemFeedback sxscProblemFeedback);

    /**
     * 修改投诉反馈
     * 
     * @param sxscProblemFeedback 投诉反馈
     * @return 结果
     */
    int updateSxscProblemFeedback(SxscProblemFeedback sxscProblemFeedback);

    /**
     * 批量删除投诉反馈
     * 
     * @param ids 需要删除的投诉反馈主键集合
     * @return 结果
     */
    int deleteSxscProblemFeedbackByIds(Long[] ids);

    /**
     * 删除投诉反馈信息
     * 
     * @param id 投诉反馈主键
     * @return 结果
     */
    int deleteSxscProblemFeedbackById(Long id);
}

package com.ruoyi.sxsc.operations.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.operations.domain.SxscSystemMessage;
import com.ruoyi.sxsc.operations.service.ISxscSystemMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 首页消息区
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@RestController
@RequestMapping("/operations/message")
public class SxscSystemMessageController extends BaseController
{
    @Autowired
    private ISxscSystemMessageService sxscSystemMessageService;

    /**
     * 查询首页消息区列表
     */
    @PreAuthorize("@ss.hasPermi('operations:message:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscSystemMessage sxscSystemMessage)
    {
        startPage();
        List<SxscSystemMessage> list = sxscSystemMessageService.selectSxscSystemMessageList(sxscSystemMessage);
        return getDataTable(list);
    }

    /**
     * 导出首页消息区列表
     */
    @PreAuthorize("@ss.hasPermi('operations:message:export')")
    @Log(title = "首页消息区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscSystemMessage sxscSystemMessage)
    {
        List<SxscSystemMessage> list = sxscSystemMessageService.selectSxscSystemMessageList(sxscSystemMessage);
        ExcelUtil<SxscSystemMessage> util = new ExcelUtil<SxscSystemMessage>(SxscSystemMessage.class);
        util.exportExcel(response, list, "首页消息区数据");
    }

    /**
     * 获取首页消息区详细信息
     */
    @PreAuthorize("@ss.hasPermi('operations:message:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscSystemMessageService.selectSxscSystemMessageById(id));
    }

    /**
     * 新增首页消息区
     */
    @PreAuthorize("@ss.hasPermi('operations:message:add')")
    @Log(title = "首页消息区", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscSystemMessage sxscSystemMessage)
    {
        return toAjax(sxscSystemMessageService.insertSxscSystemMessage(sxscSystemMessage));
    }

    /**
     * 修改首页消息区
     */
    @PreAuthorize("@ss.hasPermi('operations:message:edit')")
    @Log(title = "首页消息区", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscSystemMessage sxscSystemMessage)
    {
        return toAjax(sxscSystemMessageService.updateSxscSystemMessage(sxscSystemMessage));
    }

    /**
     * 删除首页消息区
     */
    @PreAuthorize("@ss.hasPermi('operations:message:remove')")
    @Log(title = "首页消息区", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscSystemMessageService.deleteSxscSystemMessageByIds(ids));
    }
}

package com.ruoyi.sxsc.operations.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.operations.domain.SxscProblemFeedback;
import com.ruoyi.sxsc.operations.mapper.SxscProblemFeedbackMapper;
import com.ruoyi.sxsc.operations.service.ISxscProblemFeedbackService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 投诉反馈Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Service
public class SxscProblemFeedbackServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscProblemFeedbackMapper,SxscProblemFeedback> implements ISxscProblemFeedbackService
{

    /**
     * 查询投诉反馈
     * 
     * @param id 投诉反馈主键
     * @return 投诉反馈
     */
    @Override
    public SxscProblemFeedback selectSxscProblemFeedbackById(Long id)
    {

        SxscProblemFeedback problemFeedback=getById(id);

        if(StringUtils.isNotNull(problemFeedback)){

            LambdaQueryWrapper<SxscProblemFeedback> wrapper=new LambdaQueryWrapper();

            wrapper.eq(SxscProblemFeedback::getParentId,problemFeedback.getId());

            wrapper.orderByDesc(SxscProblemFeedback::getCreateTime);

            problemFeedback.setFeedbackList(list(wrapper));
        }

        return problemFeedback;
    }

    /**
     * 查询投诉反馈列表
     * 
     * @param sxscProblemFeedback 投诉反馈
     * @return 投诉反馈
     */
    @Override
    public List<SxscProblemFeedback> selectSxscProblemFeedbackList(SxscProblemFeedback sxscProblemFeedback)
    {
        LambdaQueryWrapper<SxscProblemFeedback> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscProblemFeedback::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.eq(StringUtils.isNotNull(sxscProblemFeedback.getUserId()),SxscProblemFeedback::getUserId,sxscProblemFeedback.getUserId());
        }
        //分页只查询父级消息
        wrapper.eq(SxscProblemFeedback::getParentId,0l);

        wrapper.like(StringUtils.isNotNull(sxscProblemFeedback.getContent()),SxscProblemFeedback::getContent,sxscProblemFeedback.getContent());

        wrapper.like(StringUtils.isNotNull(sxscProblemFeedback.getContact()),SxscProblemFeedback::getContact,sxscProblemFeedback.getContact());

        wrapper.eq(StringUtils.isNotNull(sxscProblemFeedback.getStatus()),SxscProblemFeedback::getStatus,sxscProblemFeedback.getStatus());

        wrapper.orderByDesc(SxscProblemFeedback::getCreateTime);

        List<SxscProblemFeedback> list=list(wrapper);

        for(SxscProblemFeedback feedback:list){
            LambdaQueryWrapper<SxscProblemFeedback> queryWrapper=new LambdaQueryWrapper();
            queryWrapper.eq(SxscProblemFeedback::getParentId,feedback.getId());
            queryWrapper.eq(SxscProblemFeedback::getStatus,0l);
            feedback.setFeedbackList(list(queryWrapper));
        }

        return list;
    }

    /**
     * 新增投诉反馈
     * 
     * @param sxscProblemFeedback 投诉反馈
     * @return 结果
     */
    @Override
    public int insertSxscProblemFeedback(SxscProblemFeedback sxscProblemFeedback)
    {
        sxscProblemFeedback.setUserId(SecurityUtils.getUserId());
        sxscProblemFeedback.setCreateBy(SecurityUtils.getUsername());
        sxscProblemFeedback.setCreateTime(DateUtils.getNowDate());
        return save(sxscProblemFeedback)?1:0;
    }

    /**
     * 修改投诉反馈
     * 
     * @param sxscProblemFeedback 投诉反馈
     * @return 结果
     */
    @Override
    public int updateSxscProblemFeedback(SxscProblemFeedback sxscProblemFeedback)
    {
        sxscProblemFeedback.setUpdateBy(SecurityUtils.getUsername());
        sxscProblemFeedback.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscProblemFeedback)?1:0;
    }

    /**
     * 批量删除投诉反馈
     * 
     * @param ids 需要删除的投诉反馈主键
     * @return 结果
     */
    @Override
    public int deleteSxscProblemFeedbackByIds(Long[] ids)
    {
        return removeByIds(Arrays.asList(ids))?1:0;
    }

    /**
     * 删除投诉反馈信息
     * 
     * @param id 投诉反馈主键
     * @return 结果
     */
    @Override
    public int deleteSxscProblemFeedbackById(Long id)
    {
        return removeById(id)?1:0;
    }
}

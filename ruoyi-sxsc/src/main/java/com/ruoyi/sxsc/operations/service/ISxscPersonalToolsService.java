package com.ruoyi.sxsc.operations.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.operations.domain.SxscPersonalTools;

import java.util.List;

/**
 * 个人工具信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface ISxscPersonalToolsService extends IService<SxscPersonalTools>
{
    /**
     * 查询个人工具信息
     * 
     * @param id 个人工具信息主键
     * @return 个人工具信息
     */
    SxscPersonalTools selectSxscPersonalToolsById(Long id);

    /**
     * 查询个人工具信息列表
     * 
     * @param sxscPersonalTools 个人工具信息
     * @return 个人工具信息集合
     */
    List<SxscPersonalTools> selectSxscPersonalToolsList(SxscPersonalTools sxscPersonalTools);

    /**
     * 新增个人工具信息
     * 
     * @param sxscPersonalTools 个人工具信息
     * @return 结果
     */
    int insertSxscPersonalTools(SxscPersonalTools sxscPersonalTools);

    /**
     * 修改个人工具信息
     * 
     * @param sxscPersonalTools 个人工具信息
     * @return 结果
     */
    int updateSxscPersonalTools(SxscPersonalTools sxscPersonalTools);

    /**
     * 批量删除个人工具信息
     * 
     * @param ids 需要删除的个人工具信息主键集合
     * @return 结果
     */
    int deleteSxscPersonalToolsByIds(Long[] ids);

}

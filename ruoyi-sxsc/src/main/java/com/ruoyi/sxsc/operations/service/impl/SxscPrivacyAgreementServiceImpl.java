package com.ruoyi.sxsc.operations.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.operations.domain.SxscPrivacyAgreement;
import com.ruoyi.sxsc.operations.mapper.SxscPrivacyAgreementMapper;
import com.ruoyi.sxsc.operations.service.ISxscPrivacyAgreementService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 隐私协议Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Service
public class SxscPrivacyAgreementServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscPrivacyAgreementMapper,SxscPrivacyAgreement> implements ISxscPrivacyAgreementService
{

    /**
     * 查询隐私协议
     * 
     * @param id 隐私协议主键
     * @return 隐私协议
     */
    @Override
    public SxscPrivacyAgreement selectSxscPrivacyAgreementById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询隐私协议
     *
     * @param name 隐私协议主键
     * @return 隐私协议
     */
    @Override
    public SxscPrivacyAgreement selectSxscPrivacyAgreementByName(String name)
    {
        LambdaQueryWrapper<SxscPrivacyAgreement> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(SxscPrivacyAgreement::getName,name);
        wrapper.eq(SxscPrivacyAgreement::getStatus,0);
        return getOne(wrapper);
    }

    /**
     * 查询隐私协议列表
     * 
     * @param sxscPrivacyAgreement 隐私协议
     * @return 隐私协议
     */
    @Override
    public List<SxscPrivacyAgreement> selectSxscPrivacyAgreementList(SxscPrivacyAgreement sxscPrivacyAgreement)
    {
        LambdaQueryWrapper<SxscPrivacyAgreement> wrapper=new LambdaQueryWrapper();

        wrapper.like(StringUtils.isNotNull(sxscPrivacyAgreement.getName()),SxscPrivacyAgreement::getName,sxscPrivacyAgreement.getName());

        wrapper.eq(StringUtils.isNotNull(sxscPrivacyAgreement.getStatus()),SxscPrivacyAgreement::getStatus,sxscPrivacyAgreement.getStatus());

        wrapper.orderByAsc(SxscPrivacyAgreement::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增隐私协议
     * 
     * @param sxscPrivacyAgreement 隐私协议
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscPrivacyAgreement(SxscPrivacyAgreement sxscPrivacyAgreement)
    {
        SxscPrivacyAgreement data=selectSxscPrivacyAgreementByName(sxscPrivacyAgreement.getName());
        if(StringUtils.isNotNull(data)){
            return AjaxResult.error("标题已存在，请先修改已存在的标题状态");
        }
        sxscPrivacyAgreement.setCreateBy(SecurityUtils.getUsername());
        sxscPrivacyAgreement.setCreateTime(DateUtils.getNowDate());
        save(sxscPrivacyAgreement);
        return AjaxResult.success();
    }

    /**
     * 修改隐私协议
     * 
     * @param sxscPrivacyAgreement 隐私协议
     * @return 结果
     */
    @Override
    public int updateSxscPrivacyAgreement(SxscPrivacyAgreement sxscPrivacyAgreement)
    {
        sxscPrivacyAgreement.setUpdateBy(SecurityUtils.getUsername());
        sxscPrivacyAgreement.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscPrivacyAgreement)?1:0;
    }

    /**
     * 批量删除隐私协议
     * 
     * @param ids 需要删除的隐私协议主键
     * @return 结果
     */
    @Override
    public int deleteSxscPrivacyAgreementByIds(Long[] ids)
    {
        return removeByIds(Arrays.asList(ids))?1:0;
    }

    /**
     * 删除隐私协议信息
     * 
     * @param id 隐私协议主键
     * @return 结果
     */
    @Override
    public int deleteSxscPrivacyAgreementById(Long id)
    {
        return removeById(id)?1:0;
    }
}

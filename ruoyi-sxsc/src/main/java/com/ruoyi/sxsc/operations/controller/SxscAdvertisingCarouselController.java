package com.ruoyi.sxsc.operations.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.operations.domain.SxscAdvertisingCarousel;
import com.ruoyi.sxsc.operations.service.ISxscAdvertisingCarouselService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 首页轮播图
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@RestController
@RequestMapping("/operations/carousel")
public class SxscAdvertisingCarouselController extends BaseController
{
    @Autowired
    private ISxscAdvertisingCarouselService sxscAdvertisingCarouselService;

    /**
     * 查询首页轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('operations:carousel:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscAdvertisingCarousel sxscAdvertisingCarousel)
    {
        startPage();
        List<SxscAdvertisingCarousel> list = sxscAdvertisingCarouselService.selectSxscAdvertisingCarouselList(sxscAdvertisingCarousel);
        return getDataTable(list);
    }

    /**
     * 导出首页轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('operations:carousel:export')")
    @Log(title = "首页轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscAdvertisingCarousel sxscAdvertisingCarousel)
    {
        List<SxscAdvertisingCarousel> list = sxscAdvertisingCarouselService.selectSxscAdvertisingCarouselList(sxscAdvertisingCarousel);
        ExcelUtil<SxscAdvertisingCarousel> util = new ExcelUtil<SxscAdvertisingCarousel>(SxscAdvertisingCarousel.class);
        util.exportExcel(response, list, "首页轮播图数据");
    }

    /**
     * 获取首页轮播图详细信息
     */
    @PreAuthorize("@ss.hasPermi('operations:carousel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscAdvertisingCarouselService.selectSxscAdvertisingCarouselById(id));
    }

    /**
     * 新增首页轮播图
     */
    @PreAuthorize("@ss.hasPermi('operations:carousel:add')")
    @Log(title = "首页轮播图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscAdvertisingCarousel sxscAdvertisingCarousel)
    {
        return toAjax(sxscAdvertisingCarouselService.insertSxscAdvertisingCarousel(sxscAdvertisingCarousel));
    }

    /**
     * 修改首页轮播图
     */
    @PreAuthorize("@ss.hasPermi('operations:carousel:edit')")
    @Log(title = "首页轮播图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscAdvertisingCarousel sxscAdvertisingCarousel)
    {
        return toAjax(sxscAdvertisingCarouselService.updateSxscAdvertisingCarousel(sxscAdvertisingCarousel));
    }

    /**
     * 删除首页轮播图
     */
    @PreAuthorize("@ss.hasPermi('operations:carousel:remove')")
    @Log(title = "首页轮播图", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscAdvertisingCarouselService.deleteSxscAdvertisingCarouselByIds(ids));
    }
}

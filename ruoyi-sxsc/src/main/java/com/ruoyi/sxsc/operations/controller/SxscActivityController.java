package com.ruoyi.sxsc.operations.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.operations.domain.SxscActivity;
import com.ruoyi.sxsc.operations.service.ISxscActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 首页活动区
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@RestController
@RequestMapping("/operations/activity")
public class SxscActivityController extends BaseController
{
    @Autowired
    private ISxscActivityService sxscActivityService;

    /**
     * 查询首页活动区列表
     */
    @PreAuthorize("@ss.hasPermi('operations:activity:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscActivity sxscActivity)
    {
        startPage();
        List<SxscActivity> list = sxscActivityService.selectSxscActivityList(sxscActivity);
        return getDataTable(list);
    }

    /**
     * 导出首页活动区列表
     */
    @PreAuthorize("@ss.hasPermi('operations:activity:export')")
    @Log(title = "首页活动区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscActivity sxscActivity)
    {
        List<SxscActivity> list = sxscActivityService.selectSxscActivityList(sxscActivity);
        ExcelUtil<SxscActivity> util = new ExcelUtil<SxscActivity>(SxscActivity.class);
        util.exportExcel(response, list, "首页活动区数据");
    }

    /**
     * 获取首页活动区详细信息
     */
    @PreAuthorize("@ss.hasPermi('operations:activity:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscActivityService.selectSxscActivityById(id));
    }

    /**
     * 新增首页活动区
     */
    @PreAuthorize("@ss.hasPermi('operations:activity:add')")
    @Log(title = "首页活动区", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscActivity sxscActivity)
    {
        return toAjax(sxscActivityService.insertSxscActivity(sxscActivity));
    }

    /**
     * 修改首页活动区
     */
    @PreAuthorize("@ss.hasPermi('operations:activity:edit')")
    @Log(title = "首页活动区", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscActivity sxscActivity)
    {
        return toAjax(sxscActivityService.updateSxscActivity(sxscActivity));
    }

    /**
     * 删除首页活动区
     */
    @PreAuthorize("@ss.hasPermi('operations:activity:remove')")
    @Log(title = "首页活动区", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscActivityService.deleteSxscActivityByIds(ids));
    }
}

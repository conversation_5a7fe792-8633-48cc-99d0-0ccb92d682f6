package com.ruoyi.sxsc.operations.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.operations.domain.SxscSystemMessage;
import com.ruoyi.sxsc.operations.mapper.SxscSystemMessageMapper;
import com.ruoyi.sxsc.operations.service.ISxscSystemMessageService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 首页消息区Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Service
public class SxscSystemMessageServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscSystemMessageMapper,SxscSystemMessage> implements ISxscSystemMessageService
{

    /**
     * 查询首页消息区
     * 
     * @param id 首页消息区主键
     * @return 首页消息区
     */
    @Override
    public SxscSystemMessage selectSxscSystemMessageById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询首页消息区列表
     * 
     * @param sxscSystemMessage 首页消息区
     * @return 首页消息区
     */
    @Override
    public List<SxscSystemMessage> selectSxscSystemMessageList(SxscSystemMessage sxscSystemMessage)
    {
        LambdaQueryWrapper<SxscSystemMessage> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscSystemMessage::getStatus,1l);
        }
        wrapper.eq(StringUtils.isNotNull(sxscSystemMessage.getType()),SxscSystemMessage::getType,sxscSystemMessage.getType());

        wrapper.like(StringUtils.isNotNull(sxscSystemMessage.getTitle()),SxscSystemMessage::getTitle,sxscSystemMessage.getTitle());

        wrapper.eq(StringUtils.isNotNull(sxscSystemMessage.getStatus()),SxscSystemMessage::getStatus,sxscSystemMessage.getStatus());

        wrapper.eq(SxscSystemMessage::getDelFlag,0l);

        wrapper.orderByAsc(SxscSystemMessage::getSort);

        return list(wrapper);
    }

    /**
     * 新增首页消息区
     * 
     * @param sxscSystemMessage 首页消息区
     * @return 结果
     */
    @Override
    public int insertSxscSystemMessage(SxscSystemMessage sxscSystemMessage)
    {
        sxscSystemMessage.setDelFlag(0l);
        sxscSystemMessage.setCreateBy(SecurityUtils.getUsername());
        sxscSystemMessage.setCreateTime(DateUtils.getNowDate());
        return save(sxscSystemMessage)?1:0;
    }

    /**
     * 修改首页消息区
     * 
     * @param sxscSystemMessage 首页消息区
     * @return 结果
     */
    @Override
    public int updateSxscSystemMessage(SxscSystemMessage sxscSystemMessage)
    {
        sxscSystemMessage.setUpdateBy(SecurityUtils.getUsername());
        sxscSystemMessage.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscSystemMessage)?1:0;
    }

    /**
     * 批量删除首页消息区
     * 
     * @param ids 需要删除的首页消息区主键
     * @return 结果
     */
    @Override
    public int deleteSxscSystemMessageByIds(Long[] ids)
    {
        LambdaUpdateWrapper<SxscSystemMessage> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscSystemMessage::getDelFlag,1);
        updateWrapper.set(SxscSystemMessage::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscSystemMessage::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.in(SxscSystemMessage::getId,ids);
        return update(updateWrapper)?1:0;
    }


}

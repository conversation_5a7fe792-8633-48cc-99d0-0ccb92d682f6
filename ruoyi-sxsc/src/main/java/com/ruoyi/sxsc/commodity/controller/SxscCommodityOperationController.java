package com.ruoyi.sxsc.commodity.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOperation;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品操作记录
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@RestController
@RequestMapping("/commodity/operation")
public class SxscCommodityOperationController extends BaseController
{
    @Autowired
    private ISxscCommodityOperationService sxscCommodityOperationService;

    /**
     * 查询商品操作记录列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:operation:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityOperation sxscCommodityOperation)
    {
        startPage();
        List<SxscCommodityOperation> list = sxscCommodityOperationService.selectSxscCommodityOperationList(sxscCommodityOperation);
        return getDataTable(list);
    }

    /**
     * 新增商品操作记录
     */
    @PreAuthorize("@ss.hasPermi('commodity:operation:add')")
    @Log(title = "商品操作记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodityOperation sxscCommodityOperation)
    {
        return sxscCommodityOperationService.insertSxscCommodityOperation(sxscCommodityOperation);
    }


}

package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterprise;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品管理对象
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户主键 */
    private Long userId;

    /** 商品标题图URl,以逗号隔开 */
    @Excel(name = "商品标题图URl,以逗号隔开")
    private String titleImgUrl;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String name;

    /** 分类主键 */
    @Excel(name = "分类主键")
    private Long type;

    /** 板块主键
     *     普通商品：0
     *     超值购商品：根据查询专区id查询
     *     品牌购商品：2
     *     拼团购商品：3
     *     优选购：4
     * */
    @Excel(name = "板块主键")
    private Long plateId;

    /** 板块名称 */
    @TableField(exist = false)
    private String plateName;

    /** 零售价格 */
    @Excel(name = "零售价格")
    private BigDecimal retailAmount;

    /** 商品详情 */
    @Excel(name = "商品详情")
    private String details;

    /** 商品详情图片 */
    @Excel(name = "商品详情图片")
    private String detailsImgUrl;

    /** 运费 */
    @Excel(name = "运费")
    private BigDecimal freight;

    /** 运费类型 1包邮 2不包邮 3偏远地区不包邮*/
    @Excel(name = "运费类型")
    private Long freightType;

    /** 产品性质 1自营产品  2共识企业 */
    @Excel(name = "产品性质")
    private Long productNature;

    /** 商品状态(-1待支付0待审核，1待上架，2出售中，3已下架，4删除，5审核不通过) */
    @Excel(name = "商品状态(0待审核，1待上架，2出售中，3已下架，4删除，5审核不通过)")
    private Long status;

    /** 排序字段 */
    @Excel(name = "排序字段")
    private Long sortNumber;

    /** 置顶时间 */
    @Excel(name = "置顶时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date topUpTime;

    /** 商品来源1自行创建2叮咚供应商 */
    private Long sourceType;

    /** 商品关联支付订单号 */
    private String payOrderId;

    /** 商品关联支付类型*/
    private Long payType;

    /** 商品规格信息 */
    @TableField(exist = false)
    private List<SxscCommoditySpecifications> specifications;

    /** 商品板块信息 */
    @TableField(exist = false)
    private SxscCommodityPlate commodityPlate;

    /** 分类名称 */
    @TableField(exist = false)
    private String typeName;

    /** 销售总数 */
    @TableField(exist = false)
    private BigDecimal totalSales;

    /** 销售总额 */
    @TableField(exist = false)
    private long amountSales;

    /** 账号信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 企业信息 */
    @TableField(exist = false)
    private SxscEnterprise enterprise;

    public String getPlateName(){
        if(this.plateId==0){
            return "平价商品";
        }else if(this.plateId==1){
            return "超值购";
        }else if(this.plateId==2){
            return "品牌购";
        }else if(this.plateId==3){
            return "拼团购";
        }else if(this.plateId==4){
            return "优选购";
        }else if(this.plateId==9){
            return "共识购";
        }else if(this.plateId==10){
            return "新人购";
        }else{
            return "超值购";
        }
    }
}

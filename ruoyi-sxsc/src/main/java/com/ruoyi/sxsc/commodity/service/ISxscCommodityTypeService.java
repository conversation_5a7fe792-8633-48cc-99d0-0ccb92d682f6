package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityType;

import java.util.List;

/**
 * 商品分类Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public interface ISxscCommodityTypeService extends IService<SxscCommodityType>
{
    /**
     * 查询商品分类
     * 
     * @param id 商品分类主键
     * @return 商品分类
     */
    SxscCommodityType selectSxscCommodityTypeById(Long id);

    /**
     * 查询商品分类列表
     * 
     * @param sxscCommodityType 商品分类
     * @return 商品分类集合
     */
    List<SxscCommodityType> selectSxscCommodityTypeList(SxscCommodityType sxscCommodityType);

    /**
     * 新增商品分类
     * 
     * @param sxscCommodityType 商品分类
     * @return 结果
     */
    int insertSxscCommodityType(SxscCommodityType sxscCommodityType);

    /**
     * 修改商品分类
     * 
     * @param sxscCommodityType 商品分类
     * @return 结果
     */
    int updateSxscCommodityType(SxscCommodityType sxscCommodityType);


    /**
     * 删除商品分类信息
     * 
     * @param id 商品分类主键
     * @return 结果
     */
    AjaxResult deleteSxscCommodityTypeById(Long id);
}

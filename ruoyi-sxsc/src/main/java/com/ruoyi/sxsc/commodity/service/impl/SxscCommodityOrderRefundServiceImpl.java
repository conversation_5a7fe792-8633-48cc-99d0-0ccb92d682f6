package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrderRefund;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityOrderRefundMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderRefundService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumePurchase;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import com.ruoyi.sxsc.person.domain.SxscUserCommissionOrder;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionOrderService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import com.ruoyi.sxsc.utils.AliPayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单退款Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
public class SxscCommodityOrderRefundServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityOrderRefundMapper,SxscCommodityOrderRefund> implements ISxscCommodityOrderRefundService
{


    @Autowired
    AliPayUtils aliPayUtils;

    @Autowired
    ISxscAliPayOrderService iSxscAliPayOrderService;

    @Autowired
    ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    SxscCommodityOrderRefundMapper sxscCommodityOrderRefundMapper;

    @Autowired
    private ISxscUserCommissionOrderService iSxscUserCommissionOrderService;

    @Autowired
    private ISxscUserCommissionService iSxscUserCommissionService;

    /**
     * 查询订单退款
     * 
     * @param id 订单退款主键
     * @return 订单退款
     */
    @Override
    public SxscCommodityOrderRefund selectSxscCommodityOrderRefundById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询订单退款总金额
     *
     * @param month 月份
     * @return 订单退款
     */
    @Override
    public BigDecimal aliPayRefundSum(String month){
        return sxscCommodityOrderRefundMapper.aliPayRefundSum(month);
    }

    /**
     * 查询订单退款列表
     * 
     * @param sxscCommodityOrderRefund 订单退款
     * @return 订单退款
     */
    @Override
    public List<SxscCommodityOrderRefund> selectSxscCommodityOrderRefundList(SxscCommodityOrderRefund sxscCommodityOrderRefund)
    {
        LambdaQueryWrapper<SxscCommodityOrderRefund> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderRefund.getId()),SxscCommodityOrderRefund::getId,sxscCommodityOrderRefund.getId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderRefund.getStatus()),SxscCommodityOrderRefund::getStatus,sxscCommodityOrderRefund.getStatus());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderRefund.getAmount()),SxscCommodityOrderRefund::getAmount,sxscCommodityOrderRefund.getAmount());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderRefund.getUserId()),SxscCommodityOrderRefund::getUserId,sxscCommodityOrderRefund.getUserId());

        wrapper.like(StringUtils.isNotNull(sxscCommodityOrderRefund.getSubject()),SxscCommodityOrderRefund::getSubject,sxscCommodityOrderRefund.getSubject());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderRefund.getType()),SxscCommodityOrderRefund::getType,sxscCommodityOrderRefund.getType());

        wrapper.orderByDesc(SxscCommodityOrderRefund::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增订单退款
     * 
     * @param order 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public String insertSxscCommodityOrderRefund(SxscCommodityOrder order)
    {
        SxscCommodityOrderRefund orderRefundData= getById(order.getId());
        if(StringUtils.isNotNull(orderRefundData)&&orderRefundData.getStatus()==1){
            return "success";
        }
        SxscCommodityOrderRefund orderRefund=new SxscCommodityOrderRefund();
        orderRefund.setId(order.getId());
        orderRefund.setAmount(order.getActualPayment());
        orderRefund.setSubject("退款-"+order.getCommodityName());
        orderRefund.setUserId(order.getBuyerUserId());
        orderRefund.setType(order.getPayType());
        orderRefund.setCreateTime(DateUtils.getNowDate());
        try {
            switch (order.getPayType().intValue()){
                case 1:
                    SxscAliPayOrder aliPayOrder=iSxscAliPayOrderService.selectSxscAliPayOrderById(order.getId());
                    if(StringUtils.isNull(aliPayOrder)||aliPayOrder.getPayStatus()!=1){
                        return "fail";
                    }
                    aliPayUtils.aliPayRefund(orderRefund);
                    break;
                case 2:
                    break;
                case 3:
                    break;
                case 4:
                    SxscUserCommissionOrder sxscUserCommissionOrder=iSxscUserCommissionOrderService.selectSxscUserCommissionOrderById(order.getId());
                    if(StringUtils.isNotNull(sxscUserCommissionOrder)){
                        iSxscUserCommissionService.insertSxscUserCommission(order.getBuyerUserId(),order.getId(),sxscUserCommissionOrder.getAmount(),"退款-"+order.getCommodityName());
                    }
                    orderRefund.setStatus(1l);
                    break;
                default:
                    break;

            }
        }catch (Exception e){
            orderRefund.setStatus(0l);
            return  "fail";
        }finally {
            save(orderRefund);
        }
        return "success";
    }


    /**
     * 新增订单退款
     *
     * @param consumePurchase 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public String insertSxscCommodityOrderRefund(SxscUserConsumePurchase consumePurchase,BigDecimal sumAmount){
        SxscCommodityOrderRefund orderRefundData= getById(consumePurchase.getId());
        if(StringUtils.isNotNull(orderRefundData)&&orderRefundData.getStatus()==1){
            return "success";
        }
        String orderName="优惠券<规格:"+consumePurchase.getPurchaseAmount()+">("+consumePurchase.getPurchaseNum()+"张)";
        SxscCommodityOrderRefund orderRefund=new SxscCommodityOrderRefund();
        orderRefund.setId(consumePurchase.getId());
        orderRefund.setAmount(sumAmount);
        orderRefund.setSubject("退款-"+orderName);
        orderRefund.setUserId(consumePurchase.getUserId());
        orderRefund.setType(consumePurchase.getPayType());
        orderRefund.setCreateTime(DateUtils.getNowDate());
        try {
            switch (consumePurchase.getPayType().intValue()){
                case 1:
                    SxscAliPayOrder aliPayOrder=iSxscAliPayOrderService.selectSxscAliPayOrderById(consumePurchase.getId());
                    if(StringUtils.isNull(aliPayOrder)||aliPayOrder.getPayStatus()!=1){
                        return "fail";
                    }
                    aliPayUtils.aliPayRefund(orderRefund);
                    break;
                case 2:
                    break;
                case 3:
                    break;
                case 4:
                    SxscUserCommissionOrder sxscUserCommissionOrder=iSxscUserCommissionOrderService.selectSxscUserCommissionOrderById(consumePurchase.getId());
                    if(StringUtils.isNotNull(sxscUserCommissionOrder)){
                        iSxscUserCommissionService.insertSxscUserCommission(consumePurchase.getUserId(),consumePurchase.getId(),sxscUserCommissionOrder.getAmount(),"退款-"+orderName);
                    }
                    orderRefund.setStatus(1l);
                    break;
                default:
                    break;
            }
        }catch (Exception e){
            orderRefund.setStatus(0l);
            return  "fail";
        }finally {
            save(orderRefund);
        }
        return "success";
    }

    /**
     * 新增订单退款
     *
     * @param sxscCommodity 商品信息
     * @return 结果
     */
    @Override
    @Transactional
    public String insertSxscCommodityOrderRefund(SxscCommodity sxscCommodity){
        SxscCommodityOrderRefund orderRefundData= getById(sxscCommodity.getPayOrderId());
        if(StringUtils.isNotNull(orderRefundData)&&orderRefundData.getStatus()==1){
            return "success";
        }
        String orderName="商品保证金<名称:"+sxscCommodity.getName()+">";
        SxscCommodityOrderRefund orderRefund=new SxscCommodityOrderRefund();
        orderRefund.setId(sxscCommodity.getPayOrderId());
        orderRefund.setAmount(new BigDecimal("100"));
        orderRefund.setSubject("退款-"+orderName);
        orderRefund.setUserId(sxscCommodity.getUserId());
        orderRefund.setType(sxscCommodity.getPayType());
        orderRefund.setCreateTime(DateUtils.getNowDate());
        try {
            switch (sxscCommodity.getPayType().intValue()){
                case 1:
                    SxscAliPayOrder aliPayOrder=iSxscAliPayOrderService.selectSxscAliPayOrderById(sxscCommodity.getPayOrderId());
                    if(StringUtils.isNull(aliPayOrder)||aliPayOrder.getPayStatus()!=1){
                        return "fail";
                    }
                    aliPayUtils.aliPayRefund(orderRefund);
                    break;
                case 2:
                    break;
                case 3:
                    break;
                case 4:
                    SxscUserCommissionOrder sxscUserCommissionOrder=iSxscUserCommissionOrderService.selectSxscUserCommissionOrderById(sxscCommodity.getPayOrderId());
                    if(StringUtils.isNotNull(sxscUserCommissionOrder)){
                        iSxscUserCommissionService.insertSxscUserCommission(sxscCommodity.getUserId(),sxscCommodity.getPayOrderId(),sxscUserCommissionOrder.getAmount(),"退款-"+orderName);
                    }
                    orderRefund.setStatus(1l);
                    break;
                default:
                    break;
            }
        }catch (Exception e){
            orderRefund.setStatus(0l);
            return  "fail";
        }finally {
            save(orderRefund);
        }
        return "success";
    }
    /**
     * 修改订单退款
     * 
     * @param orderId 订单退款
     * @return 结果
     */
    @Override
    public AjaxResult reissueRefund(String orderId)
    {
        SxscCommodityOrderRefund orderRefund=getById(orderId);
        if(StringUtils.isNull(orderRefund)||orderRefund.getStatus()!=0){
            return AjaxResult.error("无法发起退款");
        }
        SxscCommodityOrder order=iSxscCommodityOrderService.getById(orderId);
        if(StringUtils.isNull(order)){
            return AjaxResult.error("无法发起退款");
        }
        switch (order.getPayType().intValue()){
            case 1:
                aliPayUtils.aliPayRefund(orderRefund);
                break;
            case 2:
                break;
            case 3:
                break;
            case 4:
                SxscUserCommissionOrder sxscUserCommissionOrder=iSxscUserCommissionOrderService.selectSxscUserCommissionOrderById(order.getId());
                if(StringUtils.isNotNull(sxscUserCommissionOrder)){
                    iSxscUserCommissionService.insertSxscUserCommission(order.getBuyerUserId(),order.getId(),sxscUserCommissionOrder.getAmount(),"退款-"+order.getCommodityName());
                }
                break;
            default:
                break;

        }
        orderRefund.setUpdateBy(SecurityUtils.getUsername());
        orderRefund.setUpdateTime(DateUtils.getNowDate());
        updateById(orderRefund);
        return AjaxResult.success();
    }

}

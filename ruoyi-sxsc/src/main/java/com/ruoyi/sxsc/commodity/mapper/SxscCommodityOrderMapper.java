package com.ruoyi.sxsc.commodity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 商品订单信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface SxscCommodityOrderMapper extends BaseMapper<SxscCommodityOrder>
{

    /**
     * 查询订单总数
     * @param  createTime 创建日期
     * @return  利润总额
     */
    @Select("select count(1) from sxsc_commodity_order " +
            "where DATE_FORMAT(create_time,'%Y-%m-%d') =#{createTime} ")
    Long orderCount(@Param("createTime")String createTime);

    /**
     * 查询支付订单总数
     * @param  payDateTime 支付日期
     * @param  plateId 板块主键
     * @return  订单总数
     */
    @Select("<script>"+
            "select count(1) from sxsc_commodity_order " +
            "where DATE_FORMAT(pay_date_time,'%Y-%m-%d') =#{payDateTime}" +
            "<if test=' plateId!=null and plateId!=1 '>  and commodity_id in (select id from sxsc_commodity where plate_id=#{plateId} )  </if>"+
            "<if test=' plateId!=null and plateId==1 '>  and commodity_id in (select id from sxsc_commodity where plate_id in (select id from sxsc_commodity_plate where parent_id!=0 ) )  </if>"+
            "</script>")
    Long orderPayCount(@Param("payDateTime")String payDateTime,@Param("plateId")Long plateId);


    /**
     * 查询支付订单总利润
     * @param  payDateTime 支付日期
     * @param  plateId 板块主键
     * @return  利润总额
     */
    @Select("<script>"+
            "select IFNULL(sum(actual_payment), 0)-IFNULL(sum(total_supply_amount), 0) from sxsc_commodity_order " +
            "where DATE_FORMAT(pay_date_time,'%Y-%m-%d') =#{payDateTime}" +
            "<if test=' plateId!=null and plateId!=1 '>  and commodity_id in (select id from sxsc_commodity where plate_id=#{plateId} )  </if>"+
            "<if test=' plateId!=null and plateId==1 '>  and commodity_id in (select id from sxsc_commodity where plate_id in (select id from sxsc_commodity_plate where parent_id!=0 ) )  </if>"+
            "</script>")
    BigDecimal orderPayProfitAmount(@Param("payDateTime")String payDateTime,@Param("plateId")Long plateId);

    /**
     * 根据板块查询订单总成本
     * @param  plateId 板块主键
     * @return  利润总额
     */
    @Select("<script>"+
            "select IFNULL(sum(total_supply_amount),0) from sxsc_commodity_order " +
            "where (status=4 or status=5)" +
            "<if test=' month!=null and month!=\"\" '>  and DATE_FORMAT(receiving_date_time,'%Y-%m') =#{month}   </if>"+
            "<if test=' plateId!=null and plateId!=1 '>  and commodity_id in (select id from sxsc_commodity where plate_id=#{plateId} )  </if>"+
            "<if test=' plateId!=null and plateId==1 '>  and commodity_id in (select id from sxsc_commodity where plate_id in (select id from sxsc_commodity_plate where parent_id!=0 ) )  </if>"+
            "</script>")
    BigDecimal orderSupplyAmount(@Param("month")String month,@Param("plateId")Long plateId);

    /**
     * 查询支付订单总金额
     * @param  payDateTime 支付日期
     * @return  利润总额
     */
    @Select("select IFNULL(sum(actual_payment), 0) from sxsc_commodity_order " +
            "where DATE_FORMAT(pay_date_time,'%Y-%m-%d') =#{payDateTime} ")
    BigDecimal orderPaySumAmount(@Param("payDateTime")String payDateTime);

    /**
     * 查询支付订单总成本
     * @param  payDateTime 支付日期
     * @return  利润总额
     */
    @Select("select IFNULL(sum(total_supply_amount), 0) from sxsc_commodity_order " +
            "where DATE_FORMAT(pay_date_time,'%Y-%m-%d') =#{payDateTime} ")
    BigDecimal orderPaySumCost(@Param("payDateTime")String payDateTime);

    /**
     * 查询订单总销售额
     * 平台所有的收入
     * @param  status 订单状态
     * @return  利润总额
     */
    @Select("select IFNULL(sum(actual_payment), 0) from sxsc_commodity_order " +
            "where status=#{status} ")
    BigDecimal orderTotalAmount(@Param("status")Long status);

    /**
     * 查询地区订单总销售额
     * @param  status 订单状态
     * @return  利润总额
     */
    @Select("<script>"+
            "select IFNULL(sum(actual_payment), 0) from sxsc_commodity_order " +
            "where status=#{status} and   #{overDateTime}  <![CDATA[ >= ]]>DATE_FORMAT(over_date_time,'%Y-%m-%d')"+
            "and  address_id in (select id from sxsc_user_address where  1=1 " +
                    "<if test=' province != null and province != \"\" '> and  province=#{province} </if>" +
                    "<if test=' city != null and city != \"\" '> and  city=#{city} </if>" +
                    "<if test=' county != null and county != \"\" '> and  county=#{county} </if>" +
            "     )"+
            "</script>")
    BigDecimal agentOrderTotalAmount(@Param("status")Long status,@Param("overDateTime")String overDateTime,@Param("province") String province,
                                     @Param("city") String city,@Param("county") String county);

    /**
     * 查询订单利润总额
     * 平台所有的收入-所有的支出
     * @param  status 订单状态
     * @return  利润总额
     */
    @Select("select IFNULL(sum(actual_payment), 0)-IFNULL(sum(total_supply_amount), 0) from sxsc_commodity_order " +
            "where status=#{status} ")
    BigDecimal orderProfitTotalAmount(@Param("status")Long status);


    /**
     * 查询当日流水总额
     * @param  payDateTime 支付时间
     * @return  利润总额
     */
    @Select("select IFNULL(sum(actual_payment), 0) from sxsc_commodity_order " +
            "where DATE_FORMAT(pay_date_time,'%Y-%m-%d') =#{payDateTime}")
    BigDecimal transactionAmount(@Param("payDateTime")String payDateTime);


    /**
     * 查询商品销售总额
     * @param  status 订单状态
     * @param  commodityId 商品主键
     * @return  销售总额
     */
    @Select("select IFNULL(sum(actual_payment), 0)  from sxsc_commodity_order " +
            "where status=#{status} and commodity_id=#{commodityId}")
    BigDecimal totalSales(@Param("status")Long status,@Param("commodityId")Long commodityId);


    /**
     * 查询当日商品销量最高
     * 支付日期在当天的数据
     * @return  商品主键
     */
    @Select("select a.commodity_id from (select commodity_id, COUNT(1) AS number from sxsc_commodity_order " +
            "where  DATE(pay_date_time)= CURDATE() and commodity_id in (select id from sxsc_commodity where  plate_id=0 and status=2 ) group by commodity_id order by number  desc limit 1) as a  ")
    Long explosiveProducts();

    /**
     * 查询所有的消费总额
     * @param userId 买家主键
     * @return  总数
     */
    @Select("select IFNULl(sum(actual_payment),0)  from sxsc_commodity_order " +
            "where   (status=4 or status=5) and buyer_user_id=#{userId} and commodity_id in (select id from sxsc_commodity where plate_id=4) ")
    BigDecimal totalConsumption(@Param("userId")Long userId);
}

package com.ruoyi.sxsc.commodity.model;

import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 商品订单信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityOrderModel
{
    private static final long serialVersionUID = 1L;

    /** 地址主键 */
    @Excel(name = "地址主键")
    private Long addressId;

    /** 商品主键 */
    @Excel(name = "商品主键")
    private Long commodityId;

    /** 规格主键 */
    @Excel(name = "规格主键")
    private Long specificationsId;

    /** 发票主键 */
    private Long invoiceId;

    /** 商品数量 */
    @Excel(name = "商品数量")
    private Long number;


}

package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 商品操作记录对象
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class  SxscCommodityOperation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 商品主键 */
    @Excel(name = "商品主键")
    private Long commodityId;

    /** 操作类型1审核，2上架，3下架，4删除，5不通过,6同步商品7置顶8取消置顶9支付10修改 */
    @Excel(name = "操作类型1审核，2上架，3下架，4删除，5不通过，6同步商品7置顶8取消置顶9支付")
    private Long operationType;

    /** 操作说明 */
    @Excel(name = "操作说明")
    private String operationDescribe;




}

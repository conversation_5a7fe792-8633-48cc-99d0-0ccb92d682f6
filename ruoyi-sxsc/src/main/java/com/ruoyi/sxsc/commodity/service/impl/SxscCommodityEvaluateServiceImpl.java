package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityEvaluate;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityEvaluateMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityEvaluateService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 评价信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Service
public class SxscCommodityEvaluateServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityEvaluateMapper,SxscCommodityEvaluate> implements ISxscCommodityEvaluateService
{

    @Autowired
    private ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    private SxscCommodityEvaluateMapper sxscCommodityEvaluateMapper;

    @Autowired
    private ISysUserService iSysUserService;


    /**
     * 查询评价信息
     *
     * @param orderId 订单主键
     * @return 评价信息
     */
    @Override
    public SxscCommodityEvaluate selectSxscCommodityEvaluateByCommodityOrderId(String orderId)
    {
        LambdaQueryWrapper<SxscCommodityEvaluate> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(SxscCommodityEvaluate::getCommodityOrderId,orderId);
        return getOne(wrapper);
    }

    /**
     * 查询评价信息
     * 
     * @param id 评价信息主键
     * @return 评价信息
     */
    @Override
    public SxscCommodityEvaluate selectSxscCommodityEvaluateById(Long id)
    {
        return getById(id);
    }

    /**
     * 获取商品评价信息好评率
     *
     * @param commodityId 商品主键
     * @return 评价信息
     */
    public BigDecimal getRate(Long commodityId){
        LambdaQueryWrapper<SxscCommodityEvaluate> wrapper=new LambdaQueryWrapper<>();
        wrapper.ge(SxscCommodityEvaluate::getCommodityStarLevel,4);
        wrapper.eq(SxscCommodityEvaluate::getDelFlag,0);
        wrapper.eq(SxscCommodityEvaluate::getCommodityId,commodityId);
        long haoping=count(wrapper);
        LambdaQueryWrapper<SxscCommodityEvaluate>  queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.le(SxscCommodityEvaluate::getCommodityStarLevel,3);
        wrapper.eq(SxscCommodityEvaluate::getDelFlag,0);
        queryWrapper.eq(SxscCommodityEvaluate::getCommodityId,commodityId);
        long zongshu=count(queryWrapper);
        if(zongshu==0){
            return new BigDecimal("100");
        }
        return new BigDecimal(haoping).divide(new BigDecimal(zongshu),2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 获取商家商品描述评分平均值
     *
     * @param userId 用户主键
     * @return 评价信息
     */
    public BigDecimal describeStarLevel(Long userId){
        return sxscCommodityEvaluateMapper.describeStarLevel(userId).setScale(2,RoundingMode.HALF_UP);
    }

    /**
     * 获取卖家服务评分平均值
     *
     * @param userId 用户主键
     * @return 评价信息
     */
    public BigDecimal sellerStarLevel(Long userId){
        return sxscCommodityEvaluateMapper.sellerStarLevel(userId).setScale(2,RoundingMode.HALF_UP);
    }

    /**
     * 获取物流服务评分平均值
     *
     * @param userId 用户主键
     * @return 评价信息
     */
    public BigDecimal logisticsStarLevel(Long userId){
        return sxscCommodityEvaluateMapper.logisticsStarLevel(userId).setScale(2,RoundingMode.HALF_UP);
    }


    /**
     * 查询评价信息列表
     * 
     * @param sxscCommodityEvaluate 评价信息
     * @return 评价信息
     */
    @Override
    public List<SxscCommodityEvaluate> selectSxscCommodityEvaluateList(SxscCommodityEvaluate sxscCommodityEvaluate)
    {
        LambdaQueryWrapper<SxscCommodityEvaluate> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodityEvaluate.getCommodityId()),SxscCommodityEvaluate::getCommodityId,sxscCommodityEvaluate.getCommodityId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityEvaluate.getCommodityStarLevel()),SxscCommodityEvaluate::getCommodityStarLevel,sxscCommodityEvaluate.getCommodityStarLevel());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityEvaluate.getCommodityOrderId()),SxscCommodityEvaluate::getCommodityOrderId,sxscCommodityEvaluate.getCommodityOrderId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityEvaluate.getEnterpriseUserId()),SxscCommodityEvaluate::getEnterpriseUserId,sxscCommodityEvaluate.getEnterpriseUserId());

        wrapper.like(StringUtils.isNotNull(sxscCommodityEvaluate.getMessage()),SxscCommodityEvaluate::getMessage,sxscCommodityEvaluate.getMessage());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityEvaluate.getUserId()),SxscCommodityEvaluate::getUserId,sxscCommodityEvaluate.getUserId());

        wrapper.like(StringUtils.isNotNull(sxscCommodityEvaluate.getUserName()),SxscCommodityEvaluate::getUserName,sxscCommodityEvaluate.getUserName());

        wrapper.orderByDesc(SxscCommodityEvaluate::getCreateTime);

        wrapper.eq(SxscCommodityEvaluate::getDelFlag,0l);

        List<SxscCommodityEvaluate> list=list(wrapper);

        for(SxscCommodityEvaluate evaluate:list){
            evaluate.setSysUser(iSysUserService.selectUserMainById(evaluate.getUserId()));
            evaluate.setCommodityOrder(iSxscCommodityOrderService.getById(evaluate.getCommodityOrderId()));
        }
        return list;
    }

    /**
     * 新增评价信息
     * 
     * @param sxscCommodityEvaluate 评价信息
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscCommodityEvaluate(SxscCommodityEvaluate sxscCommodityEvaluate)
    {
        SxscCommodityOrder sxscCommodityOrder=iSxscCommodityOrderService.getById(sxscCommodityEvaluate.getCommodityOrderId());
        if(StringUtils.isNull(sxscCommodityOrder)){
            return AjaxResult.error("订单不存在，无法评价");
        }
        if(sxscCommodityOrder.getStatus()!=4){
            return AjaxResult.error("订单未收货，无法评价");
        }
        if(StringUtils.isNotNull(selectSxscCommodityEvaluateByCommodityOrderId(sxscCommodityEvaluate.getCommodityOrderId()))){
            return AjaxResult.error("订单已评价");
        }
        sxscCommodityEvaluate.setCommodityId(sxscCommodityOrder.getCommodityId());
        sxscCommodityEvaluate.setEnterpriseUserId(sxscCommodityOrder.getSellerUserId());
        sxscCommodityEvaluate.setUserId(SecurityUtils.getUserId());
        sxscCommodityEvaluate.setDelFlag(0l);
        sxscCommodityEvaluate.setUserName(SecurityUtils.getLoginUser().getUser().getNickName());
        sxscCommodityEvaluate.setCreateBy(SecurityUtils.getUsername());
        sxscCommodityEvaluate.setCreateTime(DateUtils.getNowDate());
        save(sxscCommodityEvaluate);
        iSxscCommodityOrderService.updateSxscCommodityOrderStatus(sxscCommodityEvaluate.getCommodityOrderId(),5l);
        return AjaxResult.success();
    }

    /**
     * 新增评价信息
     *
     * @param sxscCommodityEvaluate 评价信息
     * @return 结果
     */
    @Override
    public void insertSxscCommodityEvaluateTask(SxscCommodityEvaluate sxscCommodityEvaluate){
        sxscCommodityEvaluate.setDelFlag(0l);
        sxscCommodityEvaluate.setCreateBy("定时任务");
        sxscCommodityEvaluate.setCreateTime(DateUtils.getNowDate());
        save(sxscCommodityEvaluate);
        iSxscCommodityOrderService.updateSxscCommodityOrderStatus(sxscCommodityEvaluate.getCommodityOrderId(),5l);
    }

    /**
     * 修改评价信息
     * 
     * @param sxscCommodityEvaluate 评价信息
     * @return 结果
     */
    @Override
    public int updateSxscCommodityEvaluate(SxscCommodityEvaluate sxscCommodityEvaluate)
    {
        sxscCommodityEvaluate.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodityEvaluate.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscCommodityEvaluate)?1:0;
    }

    /**
     * 批量删除评价信息
     * 
     * @param ids 需要删除的评价信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscCommodityEvaluateByIds(Long[] ids)
    {
        LambdaUpdateWrapper<SxscCommodityEvaluate> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscCommodityEvaluate::getDelFlag,1);
        updateWrapper.set(SxscCommodityEvaluate::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscCommodityEvaluate::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.in(SxscCommodityEvaluate::getId,ids);
        return update(updateWrapper)?1:0;
    }


}

package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityStaticCodeOrder;

import java.math.BigDecimal;
import java.util.List;

/**
 * 静态收款码订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface ISxscCommodityStaticCodeOrderService extends IService<SxscCommodityStaticCodeOrder>
{
    /**
     * 查询静态收款码订单
     * 
     * @param id 静态收款码订单主键
     * @return 静态收款码订单
     */
    SxscCommodityStaticCodeOrder selectSxscCommodityStaticCodeOrderById(String id);

    /**
     * 查询静态收款码订单列表
     * 
     * @param sxscCommodityStaticCodeOrder 静态收款码订单
     * @return 静态收款码订单集合
     */
    List<SxscCommodityStaticCodeOrder> selectSxscCommodityStaticCodeOrderList(SxscCommodityStaticCodeOrder sxscCommodityStaticCodeOrder);

    /**
     * 新增静态收款码订单
     * 
     * @param sxscCommodityStaticCodeOrder 静态收款码订单
     * @return 结果
     */
    AjaxResult insertSxscCommodityStaticCodeOrder(SxscCommodityStaticCodeOrder sxscCommodityStaticCodeOrder);

    /**
     * 修改静态收款码订单
     * 
     * @param sxscCommodityStaticCodeOrder 静态收款码订单
     * @return 结果
     */
    int updateSxscCommodityStaticCodeOrder(SxscCommodityStaticCodeOrder sxscCommodityStaticCodeOrder);


    /**
     * 修改静态收款码订单
     *
     * @param id 订单主键
     * @param status 状态
     * @param amount 支付金额
     * @return 结果
     */
    void updateSxscCommodityStaticCodeOrder(String id,String orderName, Long status, BigDecimal amount);
}

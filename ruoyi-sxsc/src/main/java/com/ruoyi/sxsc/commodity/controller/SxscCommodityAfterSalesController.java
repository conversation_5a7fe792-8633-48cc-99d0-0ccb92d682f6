package com.ruoyi.sxsc.commodity.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityAfterSales;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityAfterSalesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单售后信息
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@RestController
@RequestMapping("/commodity/sales")
public class SxscCommodityAfterSalesController extends BaseController
{
    @Autowired
    private ISxscCommodityAfterSalesService sxscCommodityAfterSalesService;

    /**
     * 查询订单售后信息列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:sales:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityAfterSales sxscCommodityAfterSales)
    {
        startPage();
        List<SxscCommodityAfterSales> list = sxscCommodityAfterSalesService.selectSxscCommodityAfterSalesList(sxscCommodityAfterSales);
        return getDataTable(list);
    }

    /**
     * 获取订单售后信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:sales:query')")
    @GetMapping(value = "/{commodityOrderId}")
    public AjaxResult getInfo(@PathVariable("commodityOrderId") String commodityOrderId)
    {
        return success(sxscCommodityAfterSalesService.selectSxscCommodityAfterSales(commodityOrderId));
    }

    /**
     * 新增订单售后信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:sales:add')")
    @Log(title = "订单售后信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodityAfterSales sxscCommodityAfterSales)
    {
        return sxscCommodityAfterSalesService.insertSxscCommodityAfterSales(sxscCommodityAfterSales);
    }

    /**
     * 订单售后信息审核是否同意退货
     */
    @PreAuthorize("@ss.hasPermi('commodity:sales:edit')")
    @Log(title = "订单售后信息", businessType = BusinessType.UPDATE)
    @PutMapping("/goods")
    public AjaxResult afterSalesUpdateGoods(@RequestBody SxscCommodityAfterSales commodityAfterSales)
    {
        return sxscCommodityAfterSalesService.updateSxscCommodityAfterSales(commodityAfterSales);
    }

    /**
     * 订单售后信息审核是否同意退款
     */
    @PreAuthorize("@ss.hasPermi('commodity:sales:edit')")
    @Log(title = "商品订单售后信息", businessType = BusinessType.UPDATE)
    @PutMapping("/amount")
    public AjaxResult afterSalesUpdate(@RequestBody SxscCommodityAfterSales commodityAfterSales)
    {
        return sxscCommodityAfterSalesService.updateCommodityOrderAfterSalesAmount(commodityAfterSales);
    }

    /**
     * 关闭售后信息
     */
    @PreAuthorize("@ss.hasPermi('xgfm/commodity:order:edit')")
    @Log(title = "商品订单售后信息", businessType = BusinessType.UPDATE)
    @PutMapping("/close/{id}")
    public AjaxResult afterSalesUpdateGoods(@PathVariable String id)
    {
        return sxscCommodityAfterSalesService.updateCommodityOrderAfterSalesStatus(id,6l);
    }
}

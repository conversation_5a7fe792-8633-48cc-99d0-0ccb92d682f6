package com.ruoyi.sxsc.commodity.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityDelivery;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品订单信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityOrderExcelModel
{

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String id;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String commodityName;

    /** 规格名称 */
    @Excel(name = "规格名称")
    private String specificationsName;

    /** 商品单价 */
    @Excel(name = "商品单价")
    private BigDecimal unitPrice;

    /** 商品数量 */
    @Excel(name = "商品数量")
    private Long number;

    /** 运费 */
    @Excel(name = "运费")
    private BigDecimal freight;

    /** 税费 */
    @Excel(name = "税费")
    private BigDecimal taxesFees;

    /** 订单总价金额 */
    @Excel(name = "订单总价金额")
    private BigDecimal totalAmount;

    /** 商品供货总价 */
    @Excel(name = "商品供货总价")
    private BigDecimal totalSupplyAmount;

    /** 实际支付金额 */
    @Excel(name = "实际支付金额")
    private BigDecimal actualPayment;

    /** 订单状态1待付款2待发货3待收货4待评价5已完成6关闭7取消 */
    @Excel(name = "订单状态",dictType="commodity_order_status")
    private Long status;

    /** 支付类型 */
    @Excel(name = "支付类型",dictType = "commodity_pay_type")
    private Long payType;


    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payDateTime;

    /** 发货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发货时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryDateTime;

    /** 收货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "收货时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receivingDateTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date overDateTime;


    /** 收货人姓名 */
    @Excel(name = "收货人姓名")
    private String name;

    /** 收货人手机号 */
    @Excel(name = "收货人手机号")
    private String phone;

    /** 收货人手机号详细地址 */
    @Excel(name = "收货人手机号详细地址")
    private String address;

    /** 省 */
    @Excel(name = "省")
    private String province;

    /** 市 */
    @Excel(name = "市")
    private String city;

    /** 县 */
    @Excel(name = "县")
    private String county;


    /** 发票抬头 */
    @Excel(name = "发票抬头")
    private String invoiceHeader;

    /** 单位税号 */
    @Excel(name = "单位税号")
    private String taxId;

    /** 注册地址 */
    @Excel(name = "注册地址")
    private String registerAddress;

    /** 注册电话 */
    @Excel(name = "注册电话")
    private String registerPhone;

    /** 开户银行 */
    @Excel(name = "开户银行")
    private String openingBank;

    /** 银行账号 */
    @Excel(name = "银行账号")
    private String bankNumber;

    /** 类型*/
    @Excel(name = "类型",readConverterExp="(1=企业,2=个人)")
    private Long type;


    /** 快递公司 */
    @Excel(name = "快递公司")
    private String deliveryName;

    /** 收货人手机号 */
    @Excel(name = "收货人手机号")
    private String deliveryPhone;

    /** 快递单号 */
    @Excel(name = "快递单号")
    private String deliveryNumber;

    public SxscCommodityOrderExcelModel(SxscCommodityOrder commodityOrder, SxscCommodityDelivery sxscCommodityDelivery){

        this.id=commodityOrder.getId();

        this.commodityName=commodityOrder.getCommodityName();

        this.specificationsName=commodityOrder.getCommodityName();

        this.unitPrice=commodityOrder.getUnitPrice();

        this.number=commodityOrder.getNumber();

        this.freight=commodityOrder.getFreight();

        this.taxesFees=commodityOrder.getTaxesFees();

        this.totalAmount=commodityOrder.getTotalAmount();

        this.totalSupplyAmount=commodityOrder.getTotalSupplyAmount();

        this.actualPayment=commodityOrder.getActualPayment();

        this.status=commodityOrder.getStatus();

        this.payType=commodityOrder.getPayType();

        this.payDateTime=commodityOrder.getPayDateTime();

        this.deliveryDateTime=commodityOrder.getDeliveryDateTime();

        this.receivingDateTime=commodityOrder.getReceivingDateTime();

        this.overDateTime=commodityOrder.getOverDateTime();

        if(StringUtils.isNotNull(commodityOrder.getAddress())){
            this.name=commodityOrder.getAddress().getName();
            this.phone=commodityOrder.getAddress().getPhone();
            this.address=commodityOrder.getAddress().getAddress();
            this.province=commodityOrder.getAddress().getProvince();
            this.city=commodityOrder.getAddress().getCity();
            this.county=commodityOrder.getAddress().getCounty();
        }


        if(StringUtils.isNotNull(commodityOrder.getInvoice())){
            this.invoiceHeader=commodityOrder.getInvoice().getInvoiceHeader();
            this.taxId=commodityOrder.getInvoice().getTaxId();
            this.registerAddress=commodityOrder.getInvoice().getRegisterAddress();
            this.registerPhone=commodityOrder.getInvoice().getRegisterPhone();
            this.openingBank=commodityOrder.getInvoice().getOpeningBank();
            this.bankNumber=commodityOrder.getInvoice().getBankNumber();
            this.type=commodityOrder.getInvoice().getType();
        }

        if(StringUtils.isNotNull(sxscCommodityDelivery)){
            this.deliveryName=sxscCommodityDelivery.getDeliveryName();
            this.deliveryPhone=sxscCommodityDelivery.getDeliveryPhone();
            this.deliveryNumber=sxscCommodityDelivery.getDeliveryNumber();
        }

    }
}

package com.ruoyi.sxsc.commodity.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityType;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品分类
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@RestController
@RequestMapping("/commodity/type")
public class SxscCommodityTypeController extends BaseController
{
    @Autowired
    private ISxscCommodityTypeService sxscCommodityTypeService;

    /**
     * 查询商品分类列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:type:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityType sxscCommodityType)
    {
        List<SxscCommodityType> list = sxscCommodityTypeService.selectSxscCommodityTypeList(sxscCommodityType);
        return getDataTable(list);
    }



    /**
     * 获取商品分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:type:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscCommodityTypeService.selectSxscCommodityTypeById(id));
    }

    /**
     * 新增商品分类
     */
    @PreAuthorize("@ss.hasPermi('commodity:type:add')")
    @Log(title = "商品分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodityType sxscCommodityType)
    {
        return toAjax(sxscCommodityTypeService.insertSxscCommodityType(sxscCommodityType));
    }

    /**
     * 修改商品分类
     */
    @PreAuthorize("@ss.hasPermi('commodity:type:edit')")
    @Log(title = "商品分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscCommodityType sxscCommodityType)
    {
        return toAjax(sxscCommodityTypeService.updateSxscCommodityType(sxscCommodityType));
    }

    /**
     * 删除商品分类
     */
    @PreAuthorize("@ss.hasPermi('commodity:type:remove')")
    @Log(title = "商品分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long ids)
    {
        return sxscCommodityTypeService.deleteSxscCommodityTypeById(ids);
    }
}

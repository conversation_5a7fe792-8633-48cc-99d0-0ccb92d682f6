package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 商品订单发票信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityOrderInvoice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 关联发票信息主键 */
    @Excel(name = "关联发票信息主键")
    private Long invoiceId;

    /** 关联商品订单主键 */
    @Excel(name = "关联商品订单主键")
    private String commodityOrderId;

    /** 1订单提交2订单完成3开票完成 */
    @Excel(name = "1订单提交2订单完成3开票完成")
    private Long status;

    /** 发票图片地址 */
    @Excel(name = "发票图片地址")
    private String imgUrl;

    /** 订单完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderCompletionTime;

    /** 开票完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开票完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date invoicingTime;




}

package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityPlate;

import java.util.List;

/**
 * 商品板块Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ISxscCommodityPlateService extends IService<SxscCommodityPlate>
{
    /**
     * 查询商品板块
     * 
     * @param id 商品板块主键
     * @return 商品板块
     */
    SxscCommodityPlate selectSxscCommodityPlateById(Long id);

    /**
     * 查询商品板块列表
     * 
     * @param sxscCommodityPlate 商品板块
     * @return 商品板块集合
     */
    List<SxscCommodityPlate> selectSxscCommodityPlateList(SxscCommodityPlate sxscCommodityPlate);

    /**
     * 新增商品板块
     * 
     * @param sxscCommodityPlate 商品板块
     * @return 结果
     */
    int insertSxscCommodityPlate(SxscCommodityPlate sxscCommodityPlate);

    /**
     * 修改商品板块
     * 
     * @param sxscCommodityPlate 商品板块
     * @return 结果
     */
    int updateSxscCommodityPlate(SxscCommodityPlate sxscCommodityPlate);


    /**
     * 删除商品板块信息
     * 
     * @param id 商品板块主键
     * @return 结果
     */
    AjaxResult deleteSxscCommodityPlateById(Long id);
}

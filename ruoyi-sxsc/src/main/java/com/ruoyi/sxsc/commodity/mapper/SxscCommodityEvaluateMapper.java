package com.ruoyi.sxsc.commodity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityEvaluate;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 评价信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
public interface SxscCommodityEvaluateMapper extends BaseMapper<SxscCommodityEvaluate>
{

    /**
     * 获取商家商品描述评分平均值
     * @return  分数平均值
     */
    @Select("select IFNULL(sum(describe_star_level)/count(1),5) from sxsc_commodity_evaluate " +
            "where  enterprise_user_id=#{userId}  and del_flag=0 ")
    BigDecimal describeStarLevel(@Param("userId")Long userId);

    /**
     * 获取卖家服务评分平均值
     * @return  分数平均值
     */
    @Select("select IFNULL(sum(seller_star_level)/count(1),5) from sxsc_commodity_evaluate " +
            "where  enterprise_user_id=#{userId}  and del_flag=0 ")
    BigDecimal sellerStarLevel(@Param("userId")Long userId);


    /**
     * 获取物流服务评分平均值
     * @return  分数平均值
     */
    @Select("select IFNULL(sum(seller_star_level)/count(1),5) from sxsc_commodity_evaluate " +
            "where  enterprise_user_id=#{userId}  and del_flag=0 ")
    BigDecimal logisticsStarLevel(@Param("userId")Long userId);




}

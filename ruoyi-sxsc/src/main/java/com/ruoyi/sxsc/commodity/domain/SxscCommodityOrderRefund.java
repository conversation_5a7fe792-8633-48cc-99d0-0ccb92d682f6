package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 订单退款对象
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityOrderRefund extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单主键 */
    @TableId(type = IdType.INPUT)
    @Excel(name = "订单主键")
    private String id;

    /** 退款状态1成功0失败 */
    @Excel(name = "退款状态1成功0失败")
    private Long status;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal amount;

    /** 退款用户主键 */
    @Excel(name = "退款用户主键")
    private Long userId;

    /** 退款说明 */
    @Excel(name = "退款说明")
    private String subject;

    /** 退款返回数据 */
    @Excel(name = "退款返回数据")
    private String response;

    /** 退款渠道1支付宝2微信3银联4佣金 */
    @Excel(name = "退款渠道1支付宝2微信3银联4佣金")
    private Long type;




}

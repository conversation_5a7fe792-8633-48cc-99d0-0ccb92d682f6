package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrderInvoice;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityOrderInvoiceMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderInvoiceService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 商品订单发票信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
public class SxscCommodityOrderInvoiceServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityOrderInvoiceMapper,SxscCommodityOrderInvoice> implements ISxscCommodityOrderInvoiceService
{

    /**
     * 查询商品订单发票信息
     * 
     * @param id 商品订单发票信息主键
     * @return 商品订单发票信息
     */
    @Override
    public SxscCommodityOrderInvoice selectSxscCommodityOrderInvoiceById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询商品订单发票信息
     *
     * @param commodityOrderId 商品订单主键
     * @return 商品订单发票信息
     */
    public SxscCommodityOrderInvoice selectSxscCommodityOrderInvoice(String commodityOrderId)
    {
        LambdaQueryWrapper<SxscCommodityOrderInvoice> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(SxscCommodityOrderInvoice::getCommodityOrderId,commodityOrderId);
        return getOne(wrapper);
    }


    /**
     * 查询商品订单发票信息列表
     * 
     * @param sxscCommodityOrderInvoice 商品订单发票信息
     * @return 商品订单发票信息
     */
    @Override
    public List<SxscCommodityOrderInvoice> selectSxscCommodityOrderInvoiceList(SxscCommodityOrderInvoice sxscCommodityOrderInvoice)
    {
        LambdaQueryWrapper<SxscCommodityOrderInvoice> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getId()),SxscCommodityOrderInvoice::getId,sxscCommodityOrderInvoice.getId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getUserId()),SxscCommodityOrderInvoice::getUserId,sxscCommodityOrderInvoice.getUserId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getInvoiceId()),SxscCommodityOrderInvoice::getInvoiceId,sxscCommodityOrderInvoice.getInvoiceId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getCommodityOrderId()),SxscCommodityOrderInvoice::getCommodityOrderId,sxscCommodityOrderInvoice.getCommodityOrderId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getStatus()),SxscCommodityOrderInvoice::getStatus,sxscCommodityOrderInvoice.getStatus());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getImgUrl()),SxscCommodityOrderInvoice::getImgUrl,sxscCommodityOrderInvoice.getImgUrl());

        wrapper.like(StringUtils.isNotNull(sxscCommodityOrderInvoice.getOrderCompletionTime()),SxscCommodityOrderInvoice::getOrderCompletionTime,sxscCommodityOrderInvoice.getOrderCompletionTime());

        wrapper.like(StringUtils.isNotNull(sxscCommodityOrderInvoice.getInvoicingTime()),SxscCommodityOrderInvoice::getInvoicingTime,sxscCommodityOrderInvoice.getInvoicingTime());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getCreateBy()),SxscCommodityOrderInvoice::getCreateBy,sxscCommodityOrderInvoice.getCreateBy());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getCreateTime()),SxscCommodityOrderInvoice::getCreateTime,sxscCommodityOrderInvoice.getCreateTime());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getUpdateBy()),SxscCommodityOrderInvoice::getUpdateBy,sxscCommodityOrderInvoice.getUpdateBy());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrderInvoice.getUpdateTime()),SxscCommodityOrderInvoice::getUpdateTime,sxscCommodityOrderInvoice.getUpdateTime());
        return list(wrapper);
    }

    /**
     * 新增商品订单发票信息
     * 
     * @param sxscCommodityOrderInvoice 商品订单发票信息
     * @return 结果
     */
    @Override
    public int insertSxscCommodityOrderInvoice(SxscCommodityOrderInvoice sxscCommodityOrderInvoice)
    {
        sxscCommodityOrderInvoice.setInvoicingTime(DateUtils.getNowDate());
        sxscCommodityOrderInvoice.setCreateBy(SecurityUtils.getUsername());
        sxscCommodityOrderInvoice.setCreateTime(DateUtils.getNowDate());
        return save(sxscCommodityOrderInvoice)?1:0;
    }

    /**
     * 修改商品订单发票信息
     * 
     * @param sxscCommodityOrderInvoice 商品订单发票信息
     * @return 结果
     */
    @Override
    public int updateSxscCommodityOrderInvoice(SxscCommodityOrderInvoice sxscCommodityOrderInvoice)
    {
        sxscCommodityOrderInvoice.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodityOrderInvoice.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscCommodityOrderInvoice)?1:0;
    }

    /**
     * 批量删除商品订单发票信息
     * 
     * @param ids 需要删除的商品订单发票信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscCommodityOrderInvoiceByIds(Long[] ids)
    {
        return removeByIds(Arrays.asList(ids))?1:0;
    }

    /**
     * 删除商品订单发票信息信息
     * 
     * @param id 商品订单发票信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscCommodityOrderInvoiceById(Long id)
    {
        return removeById(id)?1:0;
    }
}

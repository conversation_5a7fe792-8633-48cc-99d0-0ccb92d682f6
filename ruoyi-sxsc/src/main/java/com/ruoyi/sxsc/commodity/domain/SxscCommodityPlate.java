package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.sxsc.annotation.TreeChildren;
import com.ruoyi.sxsc.annotation.TreeKey;
import com.ruoyi.sxsc.annotation.TreeParentKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品板块对象
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityPlate  extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TreeKey
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 父级主键 */
    @TreeParentKey
    private Long parentId;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 优惠券面额 */
    @Excel(name = "优惠券面额")
    private BigDecimal consumeAmount;

    /** 最多抵押数量 */
    @Excel(name = "最多抵押数量")
    private Long buyNumber;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 图片地址(一个类型对应一张图片) */
    @Excel(name = "图片地址(一个类型对应一张图片)")
    private String imgUrl;

    /** 页面地址 */
    @Excel(name = "页面地址")
    private String pageUrl;

    /** 是否删除1删除0未删除 */
    private Long delFlag;

    /** 子节点 */
    @TreeChildren
    @TableField(exist = false)
    private List<SxscCommodityPlate> children = new ArrayList<>();


}

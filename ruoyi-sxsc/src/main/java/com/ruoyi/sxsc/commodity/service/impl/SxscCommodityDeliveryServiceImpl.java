package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.*;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityDeliveryMapper;
import com.ruoyi.sxsc.commodity.service.*;
import com.ruoyi.sxsc.utils.AddressDeliveryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 订单物流信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
public class SxscCommodityDeliveryServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityDeliveryMapper,SxscCommodityDelivery> implements ISxscCommodityDeliveryService
{

    @Autowired
    private ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    private ISxscCommodityAfterSalesService iSxscCommodityAfterSalesService;


    /**
     * 查询订单物流信息
     * 
     * @param id 订单物流信息主键
     * @return 订单物流信息
     */
    @Override
    public SxscCommodityDelivery selectSxscCommodityDeliveryById(Long id)
    {
        SxscCommodityDelivery sxscCommodityDelivery=getById(id);
        if(StringUtils.isNotNull(sxscCommodityDelivery)){
            sxscCommodityDelivery.setTrack(AddressDeliveryUtil.QueryTrack(sxscCommodityDelivery.getDeliveryName(), sxscCommodityDelivery.getDeliveryNumber(), sxscCommodityDelivery.getDeliveryPhone()));
        }
        return sxscCommodityDelivery;
    }

    /**
     * 查询订单物流信息
     *
     * @param orderId 订单主键
     * @param type 类型1发货2退货
     * @return 订单物流信息
     */
    @Override
    public SxscCommodityDelivery selectSxscCommodityDeliveryByOrderId(String orderId,Long type){
        LambdaQueryWrapper<SxscCommodityDelivery> wrapper=new LambdaQueryWrapper();

        wrapper.eq(SxscCommodityDelivery::getCommodityOrderId,orderId);

        wrapper.eq(SxscCommodityDelivery::getType,type);

        wrapper.orderByDesc(SxscCommodityDelivery::getCreateTime);

        wrapper.last(" limit 1");

        return getOne(wrapper);
    }

    /**
     * 查询订单物流信息列表
     * 
     * @param sxscCommodityDelivery 订单物流信息
     * @return 订单物流信息
     */
    @Override
    public List<SxscCommodityDelivery> selectSxscCommodityDeliveryList(SxscCommodityDelivery sxscCommodityDelivery)
    {
        LambdaQueryWrapper<SxscCommodityDelivery> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodityDelivery.getId()),SxscCommodityDelivery::getId,sxscCommodityDelivery.getId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityDelivery.getUserId()),SxscCommodityDelivery::getUserId,sxscCommodityDelivery.getUserId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityDelivery.getCommodityOrderId()),SxscCommodityDelivery::getCommodityOrderId,sxscCommodityDelivery.getCommodityOrderId());

        wrapper.like(StringUtils.isNotNull(sxscCommodityDelivery.getDeliveryName()),SxscCommodityDelivery::getDeliveryName,sxscCommodityDelivery.getDeliveryName());

        wrapper.like(StringUtils.isNotNull(sxscCommodityDelivery.getDeliveryPhone()),SxscCommodityDelivery::getDeliveryPhone,sxscCommodityDelivery.getDeliveryPhone());

        wrapper.like(StringUtils.isNotNull(sxscCommodityDelivery.getDeliveryNumber()),SxscCommodityDelivery::getDeliveryNumber,sxscCommodityDelivery.getDeliveryNumber());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityDelivery.getType()),SxscCommodityDelivery::getType,sxscCommodityDelivery.getType());

        wrapper.orderByDesc(SxscCommodityDelivery::getCreateTime);

        return list(wrapper);
    }


    /**
     * 新增订单物流信息
     * 
     * @param sxscCommodityDelivery 订单物流信息
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertSxscCommodityDelivery(SxscCommodityDelivery sxscCommodityDelivery)
    {
        SxscCommodityOrder commodityOrder=iSxscCommodityOrderService.getById(sxscCommodityDelivery.getCommodityOrderId());
        if(StringUtils.isNull(commodityOrder)){
            return AjaxResult.error("订单编号有误，无法添加快递信息");
        }
        if(commodityOrder.getStatus()!=2&&sxscCommodityDelivery.getType()==1){
            return AjaxResult.error("订单未付款，无法添加快递信息");
        }

        sxscCommodityDelivery.setUserId(SecurityUtils.getUserId());
        sxscCommodityDelivery.setCreateBy(SecurityUtils.getUsername());
        sxscCommodityDelivery.setCreateTime(DateUtils.getNowDate());
        if(sxscCommodityDelivery.getType()==1){
            iSxscCommodityOrderService.updateSxscCommodityOrderStatus(commodityOrder.getId(), 3l);
            save(sxscCommodityDelivery);
        }else{
            if(commodityOrder.getAfterSalesFlag()!=1){
                return AjaxResult.error("订单未申请售后，无法添加快递信息");
            }
            SxscCommodityAfterSales sxscCommodityAfterSales =iSxscCommodityAfterSalesService.selectSxscCommodityAfterSalesByCommodityOrderId(commodityOrder.getId(),1l);
            if(StringUtils.isNull(sxscCommodityAfterSales)||sxscCommodityAfterSales.getStatus()!=1){
                return AjaxResult.error("订单售后未通过，无法添加快递信息");
            }
            save(sxscCommodityDelivery);
            sxscCommodityAfterSales.setDeliveryId(sxscCommodityDelivery.getId());
            iSxscCommodityAfterSalesService.updateById(sxscCommodityAfterSales);
        }
        return AjaxResult.success();
    }

    /**
     * 修改订单物流信息
     * 
     * @param sxscCommodityDelivery 订单物流信息
     * @return 结果
     */
    @Override
    public int updateSxscCommodityDelivery(SxscCommodityDelivery sxscCommodityDelivery)
    {
        sxscCommodityDelivery.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodityDelivery.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscCommodityDelivery)?1:0;
    }

    /**
     * 导入用户数据
     *
     * @param deliveries 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importDelivery(List<SxscCommodityDelivery> deliveries, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(deliveries) || deliveries.size() == 0)
        {
            throw new ServiceException("导入物流数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (SxscCommodityDelivery delivery : deliveries)
        {
            try
            {
                delivery.setType(1l);
                AjaxResult ajaxResult=insertSxscCommodityDelivery(delivery);
                if(ajaxResult.isError()){
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、订单编号《 " + delivery.getCommodityOrderId() + " 》导入失败：";
                    failureMsg.append(msg + ajaxResult.get("msg"));
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、订单编号《 " + delivery.getCommodityOrderId() + " 》导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}

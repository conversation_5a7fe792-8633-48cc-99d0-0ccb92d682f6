package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrderRefund;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumePurchase;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单退款Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface ISxscCommodityOrderRefundService extends IService<SxscCommodityOrderRefund>
{
    /**
     * 查询订单退款
     * 
     * @param id 订单退款主键
     * @return 订单退款
     */
    SxscCommodityOrderRefund selectSxscCommodityOrderRefundById(Long id);

    /**
     * 查询订单退款总金额
     *
     * @param month 月份
     * @return 订单退款
     */
    BigDecimal aliPayRefundSum(String month);
    /**
     * 查询订单退款列表
     * 
     * @param sxscCommodityOrderRefund 订单退款
     * @return 订单退款集合
     */
    List<SxscCommodityOrderRefund> selectSxscCommodityOrderRefundList(SxscCommodityOrderRefund sxscCommodityOrderRefund);

    /**
     * 新增订单退款
     * 
     * @param order 订单信息
     * @return 结果
     */
    String insertSxscCommodityOrderRefund(SxscCommodityOrder order);

    /**
     * 新增订单退款
     *
     * @param consumePurchase 订单信息
     * @return 结果
     */
    String insertSxscCommodityOrderRefund(SxscUserConsumePurchase consumePurchase,BigDecimal sumAmount);

    /**
     * 新增订单退款
     *
     * @param sxscCommodity 商品信息
     * @return 结果
     */
    String insertSxscCommodityOrderRefund(SxscCommodity sxscCommodity);

    /**
     * 修改订单退款
     * 
     * @param orderId 订单退款
     * @return 结果
     */
    AjaxResult reissueRefund(String orderId);


}

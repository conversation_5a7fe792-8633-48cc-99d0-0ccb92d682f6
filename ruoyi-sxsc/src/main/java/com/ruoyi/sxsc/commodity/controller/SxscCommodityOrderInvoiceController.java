package com.ruoyi.sxsc.commodity.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrderInvoice;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品订单发票信息
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@RestController
@RequestMapping("/commodity/invoice")
public class SxscCommodityOrderInvoiceController extends BaseController
{
    @Autowired
    private ISxscCommodityOrderInvoiceService sxscCommodityOrderInvoiceService;

    /**
     * 查询商品订单发票信息列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:invoice:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityOrderInvoice sxscCommodityOrderInvoice)
    {
        startPage();
        List<SxscCommodityOrderInvoice> list = sxscCommodityOrderInvoiceService.selectSxscCommodityOrderInvoiceList(sxscCommodityOrderInvoice);
        return getDataTable(list);
    }


    /**
     * 获取商品订单发票信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:invoice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscCommodityOrderInvoiceService.selectSxscCommodityOrderInvoiceById(id));
    }

    /**
     * 新增商品订单发票信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:invoice:add')")
    @Log(title = "商品订单发票信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodityOrderInvoice sxscCommodityOrderInvoice)
    {
        return toAjax(sxscCommodityOrderInvoiceService.insertSxscCommodityOrderInvoice(sxscCommodityOrderInvoice));
    }

    /**
     * 修改商品订单发票信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:invoice:edit')")
    @Log(title = "商品订单发票信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscCommodityOrderInvoice sxscCommodityOrderInvoice)
    {
        return toAjax(sxscCommodityOrderInvoiceService.updateSxscCommodityOrderInvoice(sxscCommodityOrderInvoice));
    }

    /**
     * 删除商品订单发票信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:invoice:remove')")
    @Log(title = "商品订单发票信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscCommodityOrderInvoiceService.deleteSxscCommodityOrderInvoiceByIds(ids));
    }
}

package com.ruoyi.sxsc.commodity.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityPlate;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityPlateService;
import com.ruoyi.sxsc.utils.TreeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品板块
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@RestController
@RequestMapping("/commodity/plate")
public class SxscCommodityPlateController extends BaseController
{
    @Autowired
    private ISxscCommodityPlateService sxscCommodityPlateService;

    /**
     * 查询商品板块列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:plate:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityPlate sxscCommodityPlate)
    {
        List<SxscCommodityPlate> list = sxscCommodityPlateService.selectSxscCommodityPlateList(sxscCommodityPlate);
        return getDataTable(list);
    }

    /**
     * 查询商品板块树状列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:plate:list')")
    @GetMapping("/listTree")
    public TableDataInfo listTree(SxscCommodityPlate sxscCommodityPlate)
    {
        List<SxscCommodityPlate> list = sxscCommodityPlateService.selectSxscCommodityPlateList(sxscCommodityPlate);
        return getDataTable(TreeUtils.getTree(list,0));
    }

    /**
     * 获取商品板块详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:plate:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscCommodityPlateService.selectSxscCommodityPlateById(id));
    }

    /**
     * 新增商品板块
     */
    @PreAuthorize("@ss.hasPermi('commodity:plate:add')")
    @Log(title = "商品板块", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodityPlate sxscCommodityPlate)
    {
        return toAjax(sxscCommodityPlateService.insertSxscCommodityPlate(sxscCommodityPlate));
    }

    /**
     * 修改商品板块
     */
    @PreAuthorize("@ss.hasPermi('commodity:plate:edit')")
    @Log(title = "商品板块", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscCommodityPlate sxscCommodityPlate)
    {
        return toAjax(sxscCommodityPlateService.updateSxscCommodityPlate(sxscCommodityPlate));
    }

    /**
     * 删除商品板块
     */
    @PreAuthorize("@ss.hasPermi('commodity:plate:remove')")
    @Log(title = "商品板块", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return sxscCommodityPlateService.deleteSxscCommodityPlateById(id);
    }
}

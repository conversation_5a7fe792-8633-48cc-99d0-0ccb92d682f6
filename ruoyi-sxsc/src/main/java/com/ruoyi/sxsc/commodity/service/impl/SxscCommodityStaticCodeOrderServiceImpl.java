package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.constant.IntegralBillConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.bill.service.ISxscBillTrustFundService;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityStaticCodeOrder;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityStaticCodeOrderMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityStaticCodeOrderService;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterprise;
import com.ruoyi.sxsc.enterprise.service.ISxscEnterpriseService;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import com.ruoyi.sxsc.payment.service.ISxscAliPayWithdrawalService;
import com.ruoyi.sxsc.person.domain.SxscUserAgent;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscAddressService;
import com.ruoyi.sxsc.person.service.ISxscUserAgentService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 静态收款码订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@Service
public class SxscCommodityStaticCodeOrderServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityStaticCodeOrderMapper,SxscCommodityStaticCodeOrder> implements ISxscCommodityStaticCodeOrderService
{

    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    ISxscAliPayOrderService iSxscAliPayOrderService;

    @Autowired
    ISxscAddressService iSxscAddressService;

    @Autowired
    ISxscUserAgentService iSxscUserAgentService;

    @Autowired
    ISxscUserCommissionService iSxscUserCommissionService;

    @Autowired
    ISxscBillTrustFundService iSxscBillTrustFundService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    @Autowired
    ISxscAliPayWithdrawalService iSxscAliPayWithdrawalService;

    @Autowired
    ISxscEnterpriseService iSxscEnterpriseService;

    /**
     * 查询静态收款码订单
     * 
     * @param id 静态收款码订单主键
     * @return 静态收款码订单
     */
    @Override
    public SxscCommodityStaticCodeOrder selectSxscCommodityStaticCodeOrderById(String id)
    {
        return getById(id);
    }

    /**
     * 查询静态收款码订单列表
     * 
     * @param sxscCommodityStaticCodeOrder 静态收款码订单
     * @return 静态收款码订单
     */
    @Override
    public List<SxscCommodityStaticCodeOrder> selectSxscCommodityStaticCodeOrderList(SxscCommodityStaticCodeOrder sxscCommodityStaticCodeOrder)
    {
        LambdaQueryWrapper<SxscCommodityStaticCodeOrder> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodityStaticCodeOrder.getId()),SxscCommodityStaticCodeOrder::getId,sxscCommodityStaticCodeOrder.getId());

        wrapper.like(StringUtils.isNotNull(sxscCommodityStaticCodeOrder.getSubject()),SxscCommodityStaticCodeOrder::getSubject,sxscCommodityStaticCodeOrder.getSubject());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityStaticCodeOrder.getSellerUserId()),SxscCommodityStaticCodeOrder::getSellerUserId,sxscCommodityStaticCodeOrder.getSellerUserId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityStaticCodeOrder.getBuyerUserId()),SxscCommodityStaticCodeOrder::getBuyerUserId,sxscCommodityStaticCodeOrder.getBuyerUserId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityStaticCodeOrder.getProportion()),SxscCommodityStaticCodeOrder::getProportion,sxscCommodityStaticCodeOrder.getProportion());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityStaticCodeOrder.getTotalAmount()),SxscCommodityStaticCodeOrder::getTotalAmount,sxscCommodityStaticCodeOrder.getTotalAmount());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityStaticCodeOrder.getStatus()),SxscCommodityStaticCodeOrder::getStatus,sxscCommodityStaticCodeOrder.getStatus());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityStaticCodeOrder.getPayType()),SxscCommodityStaticCodeOrder::getPayType,sxscCommodityStaticCodeOrder.getPayType());

        wrapper.orderByDesc(SxscCommodityStaticCodeOrder::getCreateTime);

        List<SxscCommodityStaticCodeOrder> list=list(wrapper);
        for(SxscCommodityStaticCodeOrder staticCodeOrder:list){
            staticCodeOrder.setBuyerUser(iSysUserService.selectUserMainById(staticCodeOrder.getBuyerUserId()));
            staticCodeOrder.setSellerUser(iSysUserService.selectUserMainById(staticCodeOrder.getSellerUserId()));
        }
        return list;
    }

    /**
     * 新增静态收款码订单
     * 
     * @param sxscCommodityStaticCodeOrder 静态收款码订单
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertSxscCommodityStaticCodeOrder(SxscCommodityStaticCodeOrder sxscCommodityStaticCodeOrder)
    {
        String businessPersonnelId=sxscCommodityStaticCodeOrder.getBusinessPersonnelId();
        if(StringUtils.isEmpty(businessPersonnelId)){
            throw new ServiceException("收款码无效，请联系商家");
        }
        if(StringUtils.isNull(sxscCommodityStaticCodeOrder.getTotalAmount())){
            throw new ServiceException("请填写订单金额");
        }else{
            if(sxscCommodityStaticCodeOrder.getTotalAmount().compareTo(new BigDecimal("50000"))>0){
                throw new ServiceException("单笔金额不能大于五万！");
            }
        }
        if(StringUtils.isNull(sxscCommodityStaticCodeOrder.getPayType())){
            throw new ServiceException("请选择支付类型");
        }
        String[] split=businessPersonnelId.split("-");
        if(split.length!=2){
            throw new ServiceException("收款码无效，请联系商家");
        }
        Long sellerUserId=0l;
        try {
            sellerUserId= Long.valueOf(split[0]);
        }catch (Exception e){
            throw new ServiceException("收款码无效，请联系商家");
        }
        SxscUserInfo sxscUserInfo=iSxscUserInfoService.getById(sellerUserId);
        if(StringUtils.isNull(sxscUserInfo)||sxscUserInfo.getDelFlag()==1){
            throw new ServiceException("收款码无效，请联系商家");
        }
        if(!Md5Utils.hash(sxscUserInfo.getIdentityNumber()).equals(split[1])){
            throw new ServiceException("收款码失效，请联系商家");
        }
        SysUserMain sysUser=iSysUserService.selectUserMainById(sellerUserId);
        sxscCommodityStaticCodeOrder.setId(IdUtils.fastSimpleUUID());
        sxscCommodityStaticCodeOrder.setSubject(sysUser.getNickName()+"-固定码收款订单");
        sxscCommodityStaticCodeOrder.setSellerUserId(sellerUserId);
        sxscCommodityStaticCodeOrder.setBuyerUserId(SecurityUtils.getUserId());
        sxscCommodityStaticCodeOrder.setCreateBy(SecurityUtils.getUsername());
        sxscCommodityStaticCodeOrder.setCreateTime(DateUtils.getNowDate());
        sxscCommodityStaticCodeOrder.setStatus(0l);
        //设置默认让利比例
        if(StringUtils.isNull(sxscCommodityStaticCodeOrder.getProportion())){
            sxscCommodityStaticCodeOrder.setProportion(new BigDecimal("0.15"));
        }
        save(sxscCommodityStaticCodeOrder);

        if(sxscCommodityStaticCodeOrder.getPayType()==1){
            return AjaxResult.success("操作成功",iSxscAliPayOrderService.insertSxscAliPayOrder(new SxscAliPayOrder(sxscCommodityStaticCodeOrder)));
        }else{
            return AjaxResult.error("暂未开放，敬请期待");
        }
    }

    /**
     * 修改静态收款码订单
     * 
     * @param sxscCommodityStaticCodeOrder 静态收款码订单
     * @return 结果
     */
    @Override
    public int updateSxscCommodityStaticCodeOrder(SxscCommodityStaticCodeOrder sxscCommodityStaticCodeOrder)
    {
        sxscCommodityStaticCodeOrder.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodityStaticCodeOrder.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscCommodityStaticCodeOrder)?1:0;
    }

    /**
     * 修改静态收款码订单
     *
     * @param id 订单主键
     * @param status 状态
     * @param amount 支付金额
     * @return 结果
     */
    @Override
    @Transactional
    public void updateSxscCommodityStaticCodeOrder(String id,String orderName, Long status, BigDecimal amount){
        SxscCommodityStaticCodeOrder codeOrder=getById(id);
        if(StringUtils.isNull(codeOrder)||codeOrder.getStatus()!=0||StringUtils.isNull(amount)){
            return;
        }
        codeOrder.setStatus(status);
        codeOrder.setReceiptDate(DateUtils.getNowDate());
        codeOrder.setUpdateTime(DateUtils.getNowDate());
        //利润=支付金额*利润比例
        BigDecimal profit=amount.multiply(codeOrder.getProportion());
        //消费者获取佣金=利润*10%
        iSxscUserCommissionService.insertSxscUserCommission(codeOrder.getBuyerUserId(),codeOrder.getId(),profit.multiply(new BigDecimal("0.1")),"线下付款佣金");
        //直推人佣金=利润*10%
        Long buyerParentId=getParentId(codeOrder.getBuyerUserId());
        iSxscUserCommissionService.insertSxscUserCommission(buyerParentId,codeOrder.getId(),profit.multiply(new BigDecimal("0.1")),"直推人佣金");
        //PS : 将[富星本地]中给推码人的10%，给到[商家用户]的推荐人
        Long sellerParentId=getParentId(codeOrder.getSellerUserId());
        iSxscUserCommissionService.insertSxscUserCommission(sellerParentId,codeOrder.getId(),profit.multiply(new BigDecimal("0.1")),"商家直推人佣金");
//        固定码：
//        让利5%的码，为3％贡献值。
//        让利10%的码，为6％贡献值。
//        让利15%的码，为10％个贡献值.
//        让利30%的码，为20％个贡献值。
//        让利40％的码，为25％贡献值
//        让利50％的码，为40％贡献值
//        让利60％的码，为50％贡献值
        BigDecimal gxzProportion=new BigDecimal("0");
        if(codeOrder.getProportion().compareTo(new BigDecimal("0.6"))>=0){
            gxzProportion=new BigDecimal("0.5");
        }else if(codeOrder.getProportion().compareTo(new BigDecimal("0.5"))>=0){
            gxzProportion=new BigDecimal("0.4");
        }else if(codeOrder.getProportion().compareTo(new BigDecimal("0.4"))>=0){
            gxzProportion=new BigDecimal("0.25");
        }else if(codeOrder.getProportion().compareTo(new BigDecimal("0.3"))>=0){
            gxzProportion=new BigDecimal("0.2");
        }else if(codeOrder.getProportion().compareTo(new BigDecimal("0.15"))>=0){
            gxzProportion=new BigDecimal("0.10");
        }else if(codeOrder.getProportion().compareTo(new BigDecimal("0.10"))>=0){
            gxzProportion=new BigDecimal("0.06");
        }else if(codeOrder.getProportion().compareTo(new BigDecimal("0.05"))>=0){
            gxzProportion=new BigDecimal("0.03");
        }
        //贡献值计算->成员*gxzProportion
        iSxscBillIntegralService.insertSxscBillIntegral(codeOrder.getId(), IntegralBillConstants.Shopping,1l,amount.multiply(gxzProportion),codeOrder.getBuyerUserId());

        //信托基金=产品总利润x60%
        iSxscBillTrustFundService.insertSxscBillTrustFund(profit.multiply(new BigDecimal("0.6")),new BigDecimal("0.6"),codeOrder.getId());

        //查询商家注册时填写的地址
        SxscEnterprise sxscEnterprise=iSxscEnterpriseService.selectEnterpriseByUserId(codeOrder.getSellerUserId());

        if(StringUtils.isNotNull(sxscEnterprise)){
            //可分配利润=利润*20%-》变更为10%
            BigDecimal profitAgent=profit.multiply(new BigDecimal("0.1"));
            String orderId=codeOrder.getId();

            List<SxscUserAgent> provinces=iSxscUserAgentService.selectSxscUserAgentProvinceCode(sxscEnterprise.getProvinceCode());
            //佣金=省代理->可分配利润*20%
            BigDecimal provinceProportion=new BigDecimal("0.2");
            //佣金=市代理->可分配利润*30%
            BigDecimal cityProportion=new BigDecimal("0.3");
            //佣金=县代理->可分配利润*50%
            BigDecimal countyProportion=new BigDecimal("0.5");
            //为空利润进入信托基金
            if(StringUtils.isNotNull(provinces)&&provinces.size()>0){
                for(SxscUserAgent province:provinces){
                    iSxscUserCommissionService.insertSxscUserCommission(province.getUserId(), orderId, profitAgent.multiply(provinceProportion.multiply(province.getProportion())),"省级代理佣金");
                }
            }else {
                iSxscBillTrustFundService.insertSxscBillTrustFund(profitAgent.multiply(provinceProportion),provinceProportion,orderId);
            }
            List<SxscUserAgent> citys=iSxscUserAgentService.selectSxscUserAgentCityCode(sxscEnterprise.getCityCode());
            if(StringUtils.isNotNull(citys)&&citys.size()>0){
                for(SxscUserAgent city:citys){
                    iSxscUserCommissionService.insertSxscUserCommission(city.getUserId(), orderId, profitAgent.multiply(cityProportion.multiply(city.getProportion())),"市级代理佣金");
                }
            }else {
                iSxscBillTrustFundService.insertSxscBillTrustFund(profitAgent.multiply(cityProportion),cityProportion,orderId);
            }
            List<SxscUserAgent> countys=iSxscUserAgentService.selectSxscUserAgentCountyCode(sxscEnterprise.getCountyCode());
            if(StringUtils.isNotNull(countys)&&countys.size()>0){
                for(SxscUserAgent county:countys){
                    iSxscUserCommissionService.insertSxscUserCommission(county.getUserId(), orderId, profitAgent.multiply(countyProportion.multiply(county.getProportion())),"县级代理佣金");
                }
            }else {
                //信托基金
                iSxscBillTrustFundService.insertSxscBillTrustFund(profitAgent.multiply(countyProportion),countyProportion,orderId);
            }
        }
        updateById(codeOrder);
        //货款处理
        orderWithdrawal(codeOrder);
    }

    //查询父级账号信息
    private Long getParentId(Long userId){
        SxscUserInfo userInfo=iSxscUserInfoService.getById(userId);
        if(StringUtils.isNull(userInfo)||StringUtils.isNull(userInfo.getParentId())){
            return 0l;
        }
        return userInfo.getParentId();
    }

    //货款逻辑处理
    private void orderWithdrawal(SxscCommodityStaticCodeOrder order){
        //货款=收款金额-收款金额*利润比例
        BigDecimal amount=order.getTotalAmount().subtract(order.getTotalAmount().multiply(order.getProportion()));
        iSxscAliPayWithdrawalService.insertSxscAliPayWithdrawal(amount,order.getSellerUserId(),order.getId(),order.getSubject());
    }
}

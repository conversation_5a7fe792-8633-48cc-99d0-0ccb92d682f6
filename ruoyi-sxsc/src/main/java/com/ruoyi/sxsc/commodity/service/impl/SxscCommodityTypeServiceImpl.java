package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityType;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityTypeMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Service
public class SxscCommodityTypeServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityTypeMapper,SxscCommodityType> implements ISxscCommodityTypeService
{

    @Autowired
    private ISxscCommodityService iSxscCommodityService;

    /**
     * 查询商品分类
     * 
     * @param id 商品分类主键
     * @return 商品分类
     */
    @Override
    public SxscCommodityType selectSxscCommodityTypeById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询商品分类列表
     * 
     * @param sxscCommodityType 商品分类
     * @return 商品分类
     */
    @Override
    public List<SxscCommodityType> selectSxscCommodityTypeList(SxscCommodityType sxscCommodityType)
    {
        LambdaQueryWrapper<SxscCommodityType> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodityType.getParentId()),SxscCommodityType::getParentId,sxscCommodityType.getParentId());

        wrapper.like(StringUtils.isNotNull(sxscCommodityType.getName()),SxscCommodityType::getName,sxscCommodityType.getName());

        wrapper.eq(SxscCommodityType::getDelFlag,0l);

        wrapper.orderByAsc(SxscCommodityType::getSort);

        return list(wrapper);
    }

    /**
     * 新增商品分类
     * 
     * @param sxscCommodityType 商品分类
     * @return 结果
     */
    @Override
    public int insertSxscCommodityType(SxscCommodityType sxscCommodityType)
    {
        sxscCommodityType.setDelFlag(0l);
        sxscCommodityType.setCreateBy(SecurityUtils.getUsername());
        sxscCommodityType.setCreateTime(DateUtils.getNowDate());
        return save(sxscCommodityType)?1:0;
    }

    /**
     * 修改商品分类
     * 
     * @param sxscCommodityType 商品分类
     * @return 结果
     */
    @Override
    public int updateSxscCommodityType(SxscCommodityType sxscCommodityType)
    {
        sxscCommodityType.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodityType.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscCommodityType)?1:0;
    }

    /**
     * 删除商品分类信息
     *
     * @param id 商品分类主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteSxscCommodityTypeById(Long id)
    {
        LambdaQueryWrapper<SxscCommodityType> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscCommodityType::getParentId,id);
        wrapper.eq(SxscCommodityType::getDelFlag,0l);
        if(count(wrapper)>0){
            return AjaxResult.error("商品类别存在子级，无法删除");
        }
        LambdaQueryWrapper<SxscCommodity> commodityLambdaQueryWrapper=new LambdaQueryWrapper();
        commodityLambdaQueryWrapper.eq(SxscCommodity::getType,id);
        commodityLambdaQueryWrapper.ne(SxscCommodity::getStatus,4);
        if(iSxscCommodityService.count(commodityLambdaQueryWrapper)>0){
            return AjaxResult.error("商品类别已关联商品，无法删除");
        }
        LambdaUpdateWrapper<SxscCommodityType> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscCommodityType::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscCommodityType::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.set(SxscCommodityType::getDelFlag,1l);
        updateWrapper.in(SxscCommodityType::getId,id);
        update(updateWrapper);
        return AjaxResult.success();
    }
}

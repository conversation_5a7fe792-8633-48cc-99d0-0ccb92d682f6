package com.ruoyi.sxsc.commodity.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityEvaluate;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityEvaluateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 评价信息
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@RestController
@RequestMapping("/commodity/evaluate")
public class SxscCommodityEvaluateController extends BaseController
{
    @Autowired
    private ISxscCommodityEvaluateService sxscCommodityEvaluateService;

    /**
     * 查询评价信息列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:evaluate:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityEvaluate sxscCommodityEvaluate)
    {
        startPage();
        List<SxscCommodityEvaluate> list = sxscCommodityEvaluateService.selectSxscCommodityEvaluateList(sxscCommodityEvaluate);
        return getDataTable(list);
    }


    /**
     * 获取评价信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:evaluate:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscCommodityEvaluateService.selectSxscCommodityEvaluateById(id));
    }



    /**
     * 新增评价信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:evaluate:add')")
    @Log(title = "评价信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodityEvaluate sxscCommodityEvaluate)
    {
        return sxscCommodityEvaluateService.insertSxscCommodityEvaluate(sxscCommodityEvaluate);
    }

    /**
     * 修改评价信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:evaluate:edit')")
    @Log(title = "评价信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscCommodityEvaluate sxscCommodityEvaluate)
    {
        return toAjax(sxscCommodityEvaluateService.updateSxscCommodityEvaluate(sxscCommodityEvaluate));
    }

    /**
     * 删除评价信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:evaluate:remove')")
    @Log(title = "评价信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscCommodityEvaluateService.deleteSxscCommodityEvaluateByIds(ids));
    }
}

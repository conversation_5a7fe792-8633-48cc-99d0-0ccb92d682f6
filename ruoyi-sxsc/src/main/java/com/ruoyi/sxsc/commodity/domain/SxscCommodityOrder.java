package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.sxsc.person.domain.SxscUserAddress;
import com.ruoyi.sxsc.person.domain.SxscUserInvoice;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品订单信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 卖家主键 */
    @Excel(name = "卖家主键")
    private Long sellerUserId;

    /** 买家主键 */
    @Excel(name = "买家主键")
    private Long buyerUserId;

    /** 地址主键 */
    @Excel(name = "地址主键")
    private Long addressId;

    /** 商品主键 */
    @Excel(name = "商品主键")
    private Long commodityId;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String commodityName;

    /** 规格主键 */
    @Excel(name = "规格主键")
    private Long specificationsId;

    /** 规格名称 */
    @Excel(name = "规格名称")
    private String specificationsName;

    /** 规格图URl,以逗号隔开 */
    @Excel(name = "规格图URl,以逗号隔开")
    private String specificationsImgUrl;

    /** 商品单价 */
    @Excel(name = "商品单价")
    private BigDecimal unitPrice;

    /** 商品数量 */
    @Excel(name = "商品数量")
    private Long number;

    /** 运费 */
    @Excel(name = "运费")
    private BigDecimal freight;

    /** 税费 */
    @Excel(name = "税费")
    private BigDecimal taxesFees;

    /** 商品总价金额 */
    @Excel(name = "商品总价金额")
    private BigDecimal totalAmount;

    /** 商品供货总价 */
    @Excel(name = "商品供货总价")
    private BigDecimal totalSupplyAmount;

    /** 实际支付金额 */
    @Excel(name = "实际支付金额")
    private BigDecimal actualPayment;
    //拼团失败时，会关闭订单
    /** 订单状态1待付款2待发货3待收货4待评价5已完成6关闭7取消 */
    @Excel(name = "订单状态1待付款2待发货3待收货4待评价5已完成6关闭7取消")
    private Long status;

    /** 支付类型 */
    @Excel(name = "支付类型")
    private Long payType;

    /** 发票主键 */
    private Long invoiceId;

    /** 供应商映射订单主键 */
    private String supplierMapperOrderSn;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payDateTime;

    /** 发货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发货时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryDateTime;

    /** 收货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "收货时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receivingDateTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date overDateTime;

    /** 是否可申请售后1可申请0不可申请 */
    private Long afterSales;

    /** 0未申请售后1已申请售后 */
    private Long afterSalesFlag;

    /** 查询需要开票的订单 */
    @TableField(exist = false)
    private Long invoiceFlag;

    /** 商品信息 */
    @TableField(exist = false)
    private SxscCommodity commodity;

    /** 规格信息 */
    @TableField(exist = false)
    private SxscCommoditySpecifications specifications;

    /** 地址信息 */
    @TableField(exist = false)
    private SxscUserAddress address;

    /** 发票信息 */
    @TableField(exist = false)
    private SxscUserInvoice invoice;

    /** 发票信息 */
    @TableField(exist = false)
    private SxscCommodityOrderInvoice orderInvoice;

    /** 评价信息 */
    @TableField(exist = false)
    private SxscCommodityEvaluate evaluate;

}

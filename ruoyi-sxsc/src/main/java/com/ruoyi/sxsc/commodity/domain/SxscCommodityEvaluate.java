package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 评价信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityEvaluate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 商品主键 */
    @Excel(name = "商品主键")
    private Long commodityId;

    /** 商品订单主键 */
    @Excel(name = "商品订单主键")
    private String commodityOrderId;

    /** 企业用户主键 */
    @Excel(name = "企业用户主键")
    private Long enterpriseUserId;

    /** 商品评价星级 */
    @Excel(name = "商品评价星级")
    private BigDecimal commodityStarLevel;

    /** 商品描述评价星级 */
    @Excel(name = "商品描述评价星级")
    private BigDecimal describeStarLevel;

    /** 卖家家服务星级 */
    @Excel(name = "卖家家服务星级")
    private BigDecimal sellerStarLevel;

    /** 物流服务星级 */
    @Excel(name = "物流服务星级")
    private BigDecimal logisticsStarLevel;

    /** 评价信息 */
    @Excel(name = "评价信息")
    private String message;

    /** 评价图片信息 */
    @Excel(name = "评价图片信息")
    private String imgUrl;

    /** 评价用户主键 */
    @Excel(name = "评价用户主键")
    private Long userId;

    /** 评价用户姓名 */
    @Excel(name = "评价用户姓名")
    private String userName;

    /** 0正常1删除 */
    private Long delFlag;

    @TableField(exist = false)
    private SysUserMain sysUser;

    @TableField(exist = false)
    private SxscCommodityOrder commodityOrder;
}

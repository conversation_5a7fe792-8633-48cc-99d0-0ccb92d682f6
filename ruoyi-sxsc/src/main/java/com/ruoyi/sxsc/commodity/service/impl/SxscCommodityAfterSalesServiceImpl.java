package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityAfterSales;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityAfterSalesMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityAfterSalesService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderRefundService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import com.ruoyi.sxsc.person.domain.SxscUserCommissionOrder;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionOrderService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单售后信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
public class SxscCommodityAfterSalesServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityAfterSalesMapper,SxscCommodityAfterSales> implements ISxscCommodityAfterSalesService
{


    @Autowired
    private ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    private ISxscCommodityOrderRefundService iSxscCommodityOrderRefundService;

    @Autowired
    private ISxscUserCommissionOrderService iSxscUserCommissionOrderService;

    @Autowired
    private ISxscUserCommissionService iSxscUserCommissionService;

    /**
     * 查询订单售后信息
     *
     * @param id 订单售后信息主键
     * @return 订单售后信息
     */
    public SxscCommodityAfterSales selectSxscCommodityAfterSalesById(String id){
        return getById(id);
    }

    /**
     * 查询订单售后信息
     *
     * @param commodityOrderId 订单主键
     * @return 订单售后信息
     */
    public SxscCommodityAfterSales selectSxscCommodityAfterSales(String commodityOrderId){
        LambdaQueryWrapper<SxscCommodityAfterSales> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodityAfterSales::getCommodityOrderId,commodityOrderId);
        return getOne(queryWrapper);
    }
    /**
     * 查询订单售后信息
     * 
     * @param commodityOrderId 订单售后信息主键
     * @return 订单售后信息
     */
    @Override
    public SxscCommodityAfterSales selectSxscCommodityAfterSalesByCommodityOrderId(String commodityOrderId,Long status)
    {
        LambdaQueryWrapper<SxscCommodityAfterSales> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodityAfterSales::getCommodityOrderId,commodityOrderId);
        queryWrapper.eq(SxscCommodityAfterSales::getStatus,status);
        return getOne(queryWrapper);
    }

    /**
     * 查询订单售后信息列表
     * 
     * @param sxscCommodityAfterSales 订单售后信息
     * @return 订单售后信息
     */
    @Override
    public List<SxscCommodityAfterSales> selectSxscCommodityAfterSalesList(SxscCommodityAfterSales sxscCommodityAfterSales)
    {
        LambdaQueryWrapper<SxscCommodityAfterSales> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodityAfterSales.getCommodityOrderId()),SxscCommodityAfterSales::getCommodityOrderId,sxscCommodityAfterSales.getCommodityOrderId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityAfterSales.getDeliveryId()),SxscCommodityAfterSales::getDeliveryId,sxscCommodityAfterSales.getDeliveryId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityAfterSales.getRefundReason()),SxscCommodityAfterSales::getRefundReason,sxscCommodityAfterSales.getRefundReason());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityAfterSales.getRefundType()),SxscCommodityAfterSales::getRefundType,sxscCommodityAfterSales.getRefundType());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityAfterSales.getStatus()),SxscCommodityAfterSales::getStatus,sxscCommodityAfterSales.getStatus());

        wrapper.orderByDesc(SxscCommodityAfterSales::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增订单售后信息
     * 
     * @param sxscCommodityAfterSales 订单售后信息
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscCommodityAfterSales(SxscCommodityAfterSales sxscCommodityAfterSales)
    {
        SxscCommodityOrder commodityOrder=iSxscCommodityOrderService.getById(sxscCommodityAfterSales.getCommodityOrderId());
        if(StringUtils.isNull(commodityOrder)){
            return AjaxResult.error("订单不存在，无法申请售后");
        }
        if(commodityOrder.getStatus()==1){
            return AjaxResult.error("订单未付款，无法申请售后");
        }
        if(commodityOrder.getAfterSales()==0){
            return AjaxResult.error("此订单不支持售后申请");
        }
        SxscCommodityAfterSales afterSalesData=selectSxscCommodityAfterSales(sxscCommodityAfterSales.getCommodityOrderId());
        if(StringUtils.isNotNull(afterSalesData)){
            return AjaxResult.error("订单已申请售后信息");
        }
        sxscCommodityAfterSales.setStatus(0l);
        sxscCommodityAfterSales.setCreateBy(SecurityUtils.getUsername());
        sxscCommodityAfterSales.setCreateTime(DateUtils.getNowDate());
        save(sxscCommodityAfterSales);

        LambdaUpdateWrapper<SxscCommodityOrder> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscCommodityOrder::getId,sxscCommodityAfterSales.getCommodityOrderId());
        updateWrapper.set(SxscCommodityOrder::getAfterSalesFlag,1l);
        iSxscCommodityOrderService.update(updateWrapper);
        return AjaxResult.success();
    }

    /**
     * 修改订单售后信息
     * 
     * @param sxscCommodityAfterSales 订单售后信息
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscCommodityAfterSales(SxscCommodityAfterSales sxscCommodityAfterSales)
    {
        SxscCommodityAfterSales afterSales=getById(sxscCommodityAfterSales.getId());
        if(StringUtils.isNull(afterSales)){
            return AjaxResult.warn("审核信息有误，无法审核");
        }
        if(afterSales.getStatus()!=0){
            return AjaxResult.warn("审核信息有误，无法审核");
        }
        if(afterSales.getRefundType()==1){
            SxscCommodityOrder commodityOrder=iSxscCommodityOrderService.getById(afterSales.getCommodityOrderId());
            if(StringUtils.isNull(commodityOrder)){
                return AjaxResult.error("审核信息有误，无法审核");
            }
            if(StringUtils.isNull(sxscCommodityAfterSales.getAddressId())){
                return AjaxResult.error("收货地址未选择,无法审核");
            }
        }
        sxscCommodityAfterSales.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodityAfterSales.setUpdateTime(DateUtils.getNowDate());
        updateById(sxscCommodityAfterSales);

        return AjaxResult.success();
    }


    /**
     * 修改商品订单售后信息
     *
     * @param sxscCommodityAfterSales 商品订单售后信息
     * @return 结果
     */
    @Override
    public AjaxResult updateCommodityOrderAfterSalesAmount(SxscCommodityAfterSales sxscCommodityAfterSales){
        SxscCommodityAfterSales afterSales=getById(sxscCommodityAfterSales.getId());
        if(StringUtils.isNull(afterSales)){
            return AjaxResult.warn("审核信息有误，无法审核");
        }
        if(afterSales.getRefundType()==1&&afterSales.getStatus()!=1){
            return AjaxResult.warn("审核信息有误，无法审核");
        }
        if(afterSales.getStatus()==5){
            return AjaxResult.warn("售后信息已完成，无法审核");
        }
        if(sxscCommodityAfterSales.getStatus()==3){
            SxscCommodityOrder commodityOrder=iSxscCommodityOrderService.getById(afterSales.getCommodityOrderId());
            if(StringUtils.isNull(commodityOrder)||commodityOrder.getStatus()>3){
                return AjaxResult.warn("订单信息有误，无法审核");
            }
            String msg=iSxscCommodityOrderRefundService.insertSxscCommodityOrderRefund(commodityOrder);
            if(msg.equals("fail")){
                sxscCommodityAfterSales.setRefundStatus(2l);
            }else{
                sxscCommodityAfterSales.setRefundStatus(1l);
            }
            sxscCommodityAfterSales.setRefundTime(DateUtils.getNowDate());
            LambdaUpdateWrapper<SxscCommodityOrder> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.set(SxscCommodityOrder::getStatus,6l);
            updateWrapper.set(SxscCommodityOrder::getOverDateTime,DateUtils.getNowDate());
            updateWrapper.set(SxscCommodityOrder::getUpdateTime, DateUtils.getNowDate());
            updateWrapper.set(SxscCommodityOrder::getUpdateBy, SecurityUtils.getUsername());
            updateWrapper.eq(SxscCommodityOrder::getId,commodityOrder.getId());
            iSxscCommodityOrderService.update(updateWrapper);
        }
        sxscCommodityAfterSales.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodityAfterSales.setUpdateTime(DateUtils.getNowDate());
        updateById(sxscCommodityAfterSales);
        return AjaxResult.success();
    }

    /**
     * 修改商品订单售后信息状态
     *
     * @param id 售后主键
     * @param status 状态
     * @return 结果
     */
    @Override
    public AjaxResult updateCommodityOrderAfterSalesStatus(String id,Long status){
        LambdaUpdateWrapper<SxscCommodityAfterSales> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscCommodityAfterSales::getStatus,status);
        updateWrapper.set(SxscCommodityAfterSales::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscCommodityAfterSales::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.eq(SxscCommodityAfterSales::getId,id);
        update(updateWrapper);
        return AjaxResult.success();
    }

    /**
     * 修改商品订单售后信息状态
     *
     * @param orderId 订单主键
     * @param status 状态
     * @return 结果
     */
    @Override
    public void updateCommodityOrderAfterSalesStatusByOrderId(String orderId,Long status){
        LambdaUpdateWrapper<SxscCommodityAfterSales> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscCommodityAfterSales::getStatus,status);
        if(status==6){
            updateWrapper.set(SxscCommodityAfterSales::getRefundStatus,1);
            updateWrapper.set(SxscCommodityAfterSales::getRefundTime,DateUtils.getNowDate());
        }
        updateWrapper.set(SxscCommodityAfterSales::getUpdateBy,"系统");
        updateWrapper.set(SxscCommodityAfterSales::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.eq(SxscCommodityAfterSales::getCommodityOrderId,orderId);
        update(updateWrapper);
    }
}

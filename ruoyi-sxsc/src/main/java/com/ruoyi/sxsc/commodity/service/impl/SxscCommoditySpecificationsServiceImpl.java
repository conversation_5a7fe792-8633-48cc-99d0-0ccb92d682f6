package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommoditySpecifications;
import com.ruoyi.sxsc.commodity.mapper.SxscCommoditySpecificationsMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommoditySpecificationsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品规格Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Service
public class SxscCommoditySpecificationsServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommoditySpecificationsMapper,SxscCommoditySpecifications> implements ISxscCommoditySpecificationsService
{

    /**
     * 查询商品规格
     * 
     * @param id 商品规格主键
     * @return 商品规格
     */
    @Override
    public SxscCommoditySpecifications selectSxscCommoditySpecificationsById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询商品规格
     *
     * @param supplierMapperId 供应商商品规格主键
     * @return 商品规格
     */
    @Override
    public SxscCommoditySpecifications selectSxscCommoditySpecificationsBySupplierMapperId(Long supplierMapperId){
        LambdaQueryWrapper<SxscCommoditySpecifications> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscCommoditySpecifications::getSupplierMapperId,supplierMapperId);
        wrapper.eq(SxscCommoditySpecifications::getDelFlag,0l);
        return getOne(wrapper);
    }
    /**
     * 查询商品规格列表
     * 
     * @param sxscCommoditySpecifications 商品规格
     * @return 商品规格
     */
    @Override
    public List<SxscCommoditySpecifications> selectSxscCommoditySpecificationsList(SxscCommoditySpecifications sxscCommoditySpecifications)
    {
        LambdaQueryWrapper<SxscCommoditySpecifications> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommoditySpecifications.getCommodityId()),SxscCommoditySpecifications::getCommodityId,sxscCommoditySpecifications.getCommodityId());

        wrapper.eq(SxscCommoditySpecifications::getDelFlag,0l);

        wrapper.orderByDesc(SxscCommoditySpecifications::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增商品规格
     * 
     * @param sxscCommoditySpecifications 商品规格
     * @return 结果
     */
    @Override
    public void insertSxscCommoditySpecifications(List<SxscCommoditySpecifications> sxscCommoditySpecifications,Long commodityId)
    {
        deleteSxscCommoditySpecificationsByCommodityId(commodityId);
        for(SxscCommoditySpecifications specifications:sxscCommoditySpecifications){
            specifications.setCreateBy(SecurityUtils.getUsername());
            specifications.setCreateTime(DateUtils.getNowDate());
            specifications.setCommodityId(commodityId);
            specifications.setDelFlag(0l);
        }
        saveBatch(sxscCommoditySpecifications);
    }

    /**
     * 修改商品规格库存
     *
     * @param id 主键
     * @param stock 数量
     * @return 结果
     */
    @Override
    public void updateSxscCommoditySpecifications(Long id,Long stock)
    {
        SxscCommoditySpecifications specifications=getById(id);
        if(StringUtils.isNotNull(specifications)){
            specifications.setStock(specifications.getStock()+stock);
            updateById(specifications);
        }
    }

    /**
     * 修改商品规格库存
     *
     * @param supplierMapperId 供应链商品主键
     * @param stock 数量
     * @param moq 起订数量
     * @return 结果
     */
    @Override
    public void updateSxscCommoditySpecificationsBySupplierMapperId(Integer supplierMapperId,Integer stock,Integer moq){
        LambdaUpdateWrapper<SxscCommoditySpecifications> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscCommoditySpecifications::getSupplierMapperId,supplierMapperId);
        updateWrapper.set(SxscCommoditySpecifications::getStock,stock);
        updateWrapper.set(SxscCommoditySpecifications::getMoq,moq);
        updateWrapper.set(SxscCommoditySpecifications::getUpdateTime,DateUtils.getNowDate());
        update(updateWrapper);
    }

    /**
     * 删除商品规格信息
     * 
     * @param commodityId 商品主键
     * @return 结果
     */
    @Override
    public void deleteSxscCommoditySpecificationsByCommodityId(Long commodityId)
    {
        LambdaUpdateWrapper<SxscCommoditySpecifications> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscCommoditySpecifications::getCommodityId,commodityId);
        updateWrapper.eq(SxscCommoditySpecifications::getDelFlag,0);
        updateWrapper.set(SxscCommoditySpecifications::getDelFlag,1l);
        updateWrapper.set(SxscCommoditySpecifications::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.set(SxscCommoditySpecifications::getUpdateBy,SecurityUtils.getUsername());
        update(updateWrapper);
    }
}

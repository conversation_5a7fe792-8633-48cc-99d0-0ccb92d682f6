package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品分类对象
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 父级主键 */
    @Excel(name = "父级主键")
    private Long parentId;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 图片地址(一个类型对应一张图片) */
    @Excel(name = "图片地址(一个类型对应一张图片)")
    private String imgUrl;

    /** 是否删除1删除0未删除 */
    private Long delFlag;

    /** 子节点 */
    @TableField(exist = false)
    private List<SxscCommodityType> children = new ArrayList<>();


}

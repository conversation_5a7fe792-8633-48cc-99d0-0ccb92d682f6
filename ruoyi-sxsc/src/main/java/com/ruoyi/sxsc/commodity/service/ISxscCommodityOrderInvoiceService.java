package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrderInvoice;

import java.util.List;

/**
 * 商品订单发票信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface ISxscCommodityOrderInvoiceService extends IService<SxscCommodityOrderInvoice>
{
    /**
     * 查询商品订单发票信息
     * 
     * @param id 商品订单发票信息主键
     * @return 商品订单发票信息
     */
    SxscCommodityOrderInvoice selectSxscCommodityOrderInvoiceById(Long id);

    /**
     * 查询商品订单发票信息
     *
     * @param commodityOrderId 商品订单主键
     * @return 商品订单发票信息
     */
    SxscCommodityOrderInvoice selectSxscCommodityOrderInvoice(String commodityOrderId);

    /**
     * 查询商品订单发票信息列表
     * 
     * @param sxscCommodityOrderInvoice 商品订单发票信息
     * @return 商品订单发票信息集合
     */
    List<SxscCommodityOrderInvoice> selectSxscCommodityOrderInvoiceList(SxscCommodityOrderInvoice sxscCommodityOrderInvoice);

    /**
     * 新增商品订单发票信息
     * 
     * @param sxscCommodityOrderInvoice 商品订单发票信息
     * @return 结果
     */
    int insertSxscCommodityOrderInvoice(SxscCommodityOrderInvoice sxscCommodityOrderInvoice);

    /**
     * 修改商品订单发票信息
     * 
     * @param sxscCommodityOrderInvoice 商品订单发票信息
     * @return 结果
     */
    int updateSxscCommodityOrderInvoice(SxscCommodityOrderInvoice sxscCommodityOrderInvoice);

    /**
     * 批量删除商品订单发票信息
     * 
     * @param ids 需要删除的商品订单发票信息主键集合
     * @return 结果
     */
    int deleteSxscCommodityOrderInvoiceByIds(Long[] ids);

    /**
     * 删除商品订单发票信息信息
     * 
     * @param id 商品订单发票信息主键
     * @return 结果
     */
    int deleteSxscCommodityOrderInvoiceById(Long id);
}

package com.ruoyi.sxsc.commodity.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.constant.IntegralBillConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.bill.service.ISxscBillTrustFundService;
import com.ruoyi.sxsc.commodity.domain.*;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityOrderMapper;
import com.ruoyi.sxsc.commodity.model.SxscCommodityOrderModel;
import com.ruoyi.sxsc.commodity.service.*;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityOrderMapper;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityOrderMapperService;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import com.ruoyi.sxsc.payment.service.ISxscAliPayWithdrawalService;
import com.ruoyi.sxsc.person.domain.SxscUserAddress;
import com.ruoyi.sxsc.person.domain.SxscUserInvoice;
import com.ruoyi.sxsc.person.domain.SxscUserAgent;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.mapper.SxscUserInfoMapper;
import com.ruoyi.sxsc.person.service.*;
import com.ruoyi.sxsc.dingdong.util.DingDongCommodityUtil;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品订单信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Slf4j
@Service
public class SxscCommodityOrderServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityOrderMapper,SxscCommodityOrder> implements ISxscCommodityOrderService
{

    @Autowired
    private ISxscCommodityService iSxscCommodityService;

    @Autowired
    private ISxscCommoditySpecificationsService iSxscCommoditySpecificationsService;

    @Autowired
    private ISxscAddressService iSxscAddressService;

    @Autowired
    private ISxscInvoiceService iSxscInvoiceService;

    @Autowired
    private ISxscCommodityEvaluateService iSxscCommodityEvaluateService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISxscAliPayOrderService iSxscAliPayOrderService;

    @Autowired
    private ISxscUserConsumeBuyService iSxscUserConsumeBuyService;

    @Autowired
    private ISxscUserCommissionService iSxscUserCommissionService;

    @Autowired
    private ISxscBillIntegralService iSxscBillIntegralService;

    @Autowired
    private ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    private SxscCommodityOrderMapper commodityOrderMapper;

    @Autowired
    private ISxscCommodityOrderInvoiceService iSxscCommodityOrderInvoiceService;

    @Autowired
    private DingDongCommodityUtil dingDongCommodityUtil;

    @Autowired
    private ISxscDingdongCommodityOrderMapperService iSxscDingdongCommodityOrderMapperService;
    @Autowired
    private SxscUserInfoMapper sxscUserInfoMapper;
    @Autowired
    private ISxscUserConsumeService iSxscUserConsumeService;
    @Autowired
    private ISysConfigService iSysConfigService;


    /**
     * 查询商品订单信息
     * 
     * @param id 商品订单信息主键
     * @return 商品订单信息
     */
    @Override
    public SxscCommodityOrder selectSxscCommodityOrderById(String id)
    {
        SxscCommodityOrder commodityOrder=getById(id);
        if(StringUtils.isNull(commodityOrder)){
            return null;
        }
        commodityOrder.setAddress(iSxscAddressService.getById(commodityOrder.getAddressId()));
        commodityOrder.setInvoice(iSxscInvoiceService.getById(commodityOrder.getInvoiceId()));
        commodityOrder.setOrderInvoice(iSxscCommodityOrderInvoiceService.selectSxscCommodityOrderInvoice(commodityOrder.getId()));
        commodityOrder.setCommodity(iSxscCommodityService.getById(commodityOrder.getCommodityId()));
        commodityOrder.setSpecifications(iSxscCommoditySpecificationsService.getById(commodityOrder.getSpecificationsId()));
        commodityOrder.setEvaluate(iSxscCommodityEvaluateService.selectSxscCommodityEvaluateByCommodityOrderId(commodityOrder.getId()));
        return commodityOrder;
    }
    /**
     * 查询商品订单信息
     *
     * @param DingdongSn 供应商商品主键
     * @return 商品订单信息
     */
    @Override
    public SxscCommodityOrder selectSxscCommodityOrderByDingdongSn(String DingdongSn){
        LambdaQueryWrapper<SxscCommodityOrder> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodityOrder::getSupplierMapperOrderSn,DingdongSn);
        return getOne(queryWrapper);
    }
    /**
     * 查询商品订单信息列表
     * 
     * @param sxscCommodityOrder 商品订单信息
     * @return 商品订单信息
     */
    @Override
    public List<SxscCommodityOrder> selectSxscCommodityOrderList(SxscCommodityOrder sxscCommodityOrder)
    {
        LambdaQueryWrapper<SxscCommodityOrder> wrapper=new LambdaQueryWrapper();
        if(StringUtils.isNotNull(sxscCommodityOrder.getSellerUserId())){
            wrapper.eq(SxscCommodityOrder::getSellerUserId,sxscCommodityOrder.getSellerUserId());
        }
        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrder.getId()),SxscCommodityOrder::getId,sxscCommodityOrder.getId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrder.getBuyerUserId()),SxscCommodityOrder::getBuyerUserId,sxscCommodityOrder.getBuyerUserId());

        wrapper.like(StringUtils.isNotNull(sxscCommodityOrder.getCommodityName()),SxscCommodityOrder::getCommodityName,sxscCommodityOrder.getCommodityName());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrder.getAfterSalesFlag()),SxscCommodityOrder::getAfterSalesFlag,sxscCommodityOrder.getAfterSalesFlag());

        wrapper.like(StringUtils.isNotNull(sxscCommodityOrder.getSpecificationsName()),SxscCommodityOrder::getSpecificationsName,sxscCommodityOrder.getSpecificationsName());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrder.getStatus()),SxscCommodityOrder::getStatus,sxscCommodityOrder.getStatus());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrder.getPayType()),SxscCommodityOrder::getPayType,sxscCommodityOrder.getPayType());

        if(StringUtils.isNotNull(sxscCommodityOrder.getParams().get("startPayDateTime"))){
            wrapper.between(SxscCommodityOrder::getPayDateTime,
                    sxscCommodityOrder.getParams().get("startPayDateTime").toString(),
                    sxscCommodityOrder.getParams().get("endPayDateTime").toString());
        }
        wrapper.like(StringUtils.isNotNull(sxscCommodityOrder.getDeliveryDateTime()),SxscCommodityOrder::getDeliveryDateTime,sxscCommodityOrder.getDeliveryDateTime());

        wrapper.like(StringUtils.isNotNull(sxscCommodityOrder.getReceivingDateTime()),SxscCommodityOrder::getReceivingDateTime,sxscCommodityOrder.getReceivingDateTime());

        wrapper.like(StringUtils.isNotNull(sxscCommodityOrder.getOverDateTime()),SxscCommodityOrder::getOverDateTime,sxscCommodityOrder.getOverDateTime());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrder.getCreateBy()),SxscCommodityOrder::getCreateBy,sxscCommodityOrder.getCreateBy());

        wrapper.isNotNull(StringUtils.isNotNull(sxscCommodityOrder.getInvoiceFlag()),SxscCommodityOrder::getInvoiceId);

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOrder.getSupplierMapperOrderSn()),SxscCommodityOrder::getSupplierMapperOrderSn,sxscCommodityOrder.getSupplierMapperOrderSn());

        if(StringUtils.isNotNull(sxscCommodityOrder.getParams().get("plateId"))){
            wrapper.apply("commodity_id in (select id from sxsc_commodity where plate_id="+sxscCommodityOrder.getParams().get("plateId")+")");
        }
        if(StringUtils.isNotNull(sxscCommodityOrder.getParams().get("sellPhone"))){
            wrapper.apply("seller_user_id in (select user_id from sys_user where user_name like '%"+sxscCommodityOrder.getParams().get("sellPhone")+"%')");
        }
        wrapper.orderByDesc(SxscCommodityOrder::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增商品订单信息
     * 
     * @param model 商品订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public String insertSxscCommodityOrder(SxscCommodityOrderModel model)
    {
        SxscUserInfo sxscUserInfo=iSxscUserInfoService.getById(SecurityUtils.getUserId());
        if(StringUtils.isNotNull(sxscUserInfo)&&sxscUserInfo.getNodeAcc()==1){
            throw new ServiceException("无法创建订单，请联系管理员");
        }
        SxscCommodity sxscCommodity=iSxscCommodityService.getById(model.getCommodityId());
        if(StringUtils.isNull(sxscCommodity)||sxscCommodity.getStatus()!=2){
            throw new ServiceException("商品不存在，无法创建订单");
        }

        SxscCommoditySpecifications specifications=iSxscCommoditySpecificationsService.getById(model.getSpecificationsId());
        if(StringUtils.isNull(specifications)||specifications.getCommodityId().intValue()!=sxscCommodity.getId().intValue()){
            throw new ServiceException("商品规格有误，无法创建订单");
        }
        if(specifications.getStock().longValue()<model.getNumber()){
            throw new ServiceException("库存不足，无法创建订单");
        }
        if(specifications.getMoq().intValue()>model.getNumber().intValue()){
            throw new ServiceException("最少<"+specifications.getMoq()+">件起购");
        }
        SxscUserAddress sxscUserAddress =iSxscAddressService.getById(model.getAddressId());
        if(StringUtils.isNull(sxscUserAddress)){
            throw new ServiceException("地址信息有误，无法创建订单");
        }
        SxscCommodityOrder commodityOrder=new SxscCommodityOrder();
        if(StringUtils.isNotNull(model.getInvoiceId())){
            SxscUserInvoice sxscUserInvoice =iSxscInvoiceService.getById(model.getInvoiceId());
            if(StringUtils.isNull(sxscUserInvoice)|| sxscUserInvoice.getUserId().intValue()!=SecurityUtils.getUserId().intValue()){
                throw new ServiceException("开票信息有误，无法创建订单");
            }
        }
        //不支持售后订单的商品板块
        String afterSalesPlateId=configService.selectConfigByKey("sxsc.commodity.order.afterSales.plateId");

        if(afterSalesPlateId.contains(String.valueOf(sxscCommodity.getPlateId()))){
            commodityOrder.setAfterSales(0l);
        }else{
            commodityOrder.setAfterSales(1l);
        }
        commodityOrder.setId(IdUtils.fastSimpleUUID());
        commodityOrder.setCommodityId(sxscCommodity.getId());
        commodityOrder.setCommodityName(sxscCommodity.getName());
        commodityOrder.setBuyerUserId(SecurityUtils.getUserId());
        commodityOrder.setSellerUserId(sxscCommodity.getUserId());
        commodityOrder.setAddressId(sxscUserAddress.getId());
        commodityOrder.setSpecificationsId(specifications.getId());
        commodityOrder.setSpecificationsName(specifications.getName());
        commodityOrder.setSpecificationsImgUrl(specifications.getImgUrl());
        commodityOrder.setUnitPrice(specifications.getPrice());
        commodityOrder.setNumber(model.getNumber());
        BigDecimal totalSupplyAmount=specifications.getSupplyAmount().multiply(BigDecimal.valueOf(model.getNumber()));
        commodityOrder.setTotalSupplyAmount(totalSupplyAmount.add(totalSupplyAmount.multiply(specifications.getFloatingRatio())));
        commodityOrder.setInvoiceId(model.getInvoiceId());
        if(StringUtils.isNotNull(commodityOrder.getInvoiceId())){
            String proportion = configService.selectConfigByKey("sxsc.commodity.taxesFees.proportion");
            commodityOrder.setTaxesFees(commodityOrder.getUnitPrice().multiply(BigDecimal.valueOf(model.getNumber())).multiply(new BigDecimal(proportion)));
        }else{
            commodityOrder.setTaxesFees(new BigDecimal("0"));
        }
        if(sxscCommodity.getFreightType()==1){
            commodityOrder.setFreight(new BigDecimal("0"));
        }else if(sxscCommodity.getFreightType()==3){
            String provinceCode =configService.selectConfigByKey("sxsc.commodity.freight.remote.areas");
            if(provinceCode.contains(sxscUserAddress.getProvinceCode())){
                commodityOrder.setFreight(sxscCommodity.getFreight());
            }else{
                commodityOrder.setFreight(new BigDecimal("0"));
            }
        }else{
            commodityOrder.setFreight(sxscCommodity.getFreight());
        }
        commodityOrder.setTotalAmount(commodityOrder.getUnitPrice().multiply(BigDecimal.valueOf(model.getNumber())).add(commodityOrder.getFreight()).add(commodityOrder.getTaxesFees()));
        commodityOrder.setCreateBy(SecurityUtils.getUsername());
        commodityOrder.setCreateTime(DateUtils.getNowDate());
        commodityOrder.setStatus(1L);
        if(sxscCommodity.getSourceType()==2){
            //无需处理运费
            JSONObject content=dingDongCommodityUtil.orderCreate(commodityOrder,sxscUserAddress,specifications);
            String orderSn= content.getString("orderSn");
            commodityOrder.setSupplierMapperOrderSn(orderSn);
        }
        save(commodityOrder);
        iSxscCommoditySpecificationsService.updateSxscCommoditySpecifications(commodityOrder.getSpecificationsId(),model.getNumber()*-1);
        return commodityOrder.getId();
    }

    /**
     * 修改商品订单信息
     * 
     * @param sxscCommodityOrder 商品订单信息
     * @return 结果
     */
    @Override
    public int updateSxscCommodityOrder(SxscCommodityOrder sxscCommodityOrder)
    {
        sxscCommodityOrder.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodityOrder.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscCommodityOrder)?1:0;
    }

    /**
     * 修改商品订单信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    @Override
    public AjaxResult dingdongRestartPayment(String orderId){
        SxscCommodityOrder sxscCommodityOrder=getById(orderId);
        if(StringUtils.isNull(sxscCommodityOrder)||sxscCommodityOrder.getStatus()==0||sxscCommodityOrder.getStatus()==6){
            return AjaxResult.error("订单信息有误");
        }
        SxscUserAddress sxscUserAddress =iSxscAddressService.selectSxscAddressById(sxscCommodityOrder.getAddressId());
        if(StringUtils.isNull(sxscUserAddress)){
            return AjaxResult.error("订单地址信息有误");
        }
        SxscCommoditySpecifications specifications=iSxscCommoditySpecificationsService.selectSxscCommoditySpecificationsById(sxscCommodityOrder.getSpecificationsId());
        if(StringUtils.isNull(specifications)){
            return AjaxResult.error("订单规格信息有误");
        }
        JSONObject content=dingDongCommodityUtil.orderCreate(sxscCommodityOrder,sxscUserAddress,specifications);
        String orderSn= content.getString("orderSn");
        sxscCommodityOrder.setSupplierMapperOrderSn(orderSn);
        updateSxscCommodityOrder(sxscCommodityOrder);
        return AjaxResult.success();
    }

    /**
     * 修改商品订单信息
     *
     * @param id 商品订单主键
     * @param type 支付类型
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult updateSxscCommodityOrderPayment(String id,Long type){
        SxscCommodityOrder sxscCommodityOrder=getById(id);
        if(StringUtils.isNull(sxscCommodityOrder)){
            return AjaxResult.error("订单不存在");
        }
        SxscCommodity sxscCommodity=iSxscCommodityService.getById(sxscCommodityOrder.getCommodityId());
        if(StringUtils.isNull(sxscCommodity)){
            throw new ServiceException("商品信息异常，请联系系统管理员");
        }
        LambdaUpdateWrapper<SxscCommodityOrder> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(SxscCommodityOrder::getId,id);
        wrapper.set(SxscCommodityOrder::getPayType,type);
        update(wrapper);
        //校验优惠券抵押
        iSxscUserConsumeBuyService.checkSxscUserConsumeBuy(sxscCommodityOrder,sxscCommodity);

        switch (type.intValue()){
            case 1:
                return AjaxResult.success("操作成功",iSxscAliPayOrderService.insertSxscAliPayOrder(new SxscAliPayOrder(sxscCommodityOrder)));
            case 2:
                return AjaxResult.error("系统暂不支持，敬请期待");
            case 3:
                return AjaxResult.error("系统暂不支持，敬请期待");
            case 4:
                return iSxscUserCommissionService.settlement(sxscCommodityOrder.getTotalAmount(),sxscCommodityOrder.getId());
            default:
                return AjaxResult.error("系统暂不支持，敬请期待");
        }
    }

    @Autowired
    ISxscBillTrustFundService iSxscBillTrustFundService;
    /**
     * 修改商品订单信息
     *
     * @param id 订单主键
     * @param status 状态1待付款2待发货3待收货4待评价5已完成7取消订单
     * @return 结果
     */
    @Override
    @Transactional
    public synchronized AjaxResult updateSxscCommodityOrderStatus(String id,Long status){
        SxscCommodityOrder commodityOrder=getById(id);

        if(StringUtils.isNull(commodityOrder)){
            throw new ServiceException("订单无效，请联系管理员");
        }
        //完成、关闭、取消状态下，不可操作订单
        if(commodityOrder.getStatus()>=5){
            throw new ServiceException("订单不支持操作");
        }
        if(status==4&&commodityOrder.getStatus()==4){
            throw new ServiceException("订单已确认收货");
        }
        if(status==5&&commodityOrder.getStatus()!=4){
            throw new ServiceException("订单未收货，无法完成");
        }
        if(status==7&&commodityOrder.getStatus()!=1){
            throw new ServiceException("订单不处于待付款状态，无法取消");
        }
        if(status==4){
            commodityOrder.setReceivingDateTime(DateUtils.getNowDate());
            confirmReceipt(commodityOrder);
        }
        if(status==3){
            commodityOrder.setDeliveryDateTime(DateUtils.getNowDate());
        }
        if(status==5){
            commodityOrder.setOverDateTime(DateUtils.getNowDate());
        }
        if(status==7){
            commodityOrder.setOverDateTime(DateUtils.getNowDate());
            //修改商品规格
            iSxscCommoditySpecificationsService.updateSxscCommoditySpecifications(commodityOrder.getSpecificationsId(),commodityOrder.getNumber()*1);
        }
        commodityOrder.setStatus(status);
        updateById(commodityOrder);
        return AjaxResult.success();
    }

    /**
     * 支付成功修改商品订单信息
     *
     * @param id 订单批次主键
     * @param actualAmount 支付金额
     * @return 结果
     */
    @Override
    @Transactional
    public void updateXgfmCommodityOrderPayment(String id,  BigDecimal actualAmount){
        LambdaUpdateWrapper<SxscCommodityOrder> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(SxscCommodityOrder::getId,id);
        wrapper.setSql(" actual_payment = total_amount ");
        wrapper.set(SxscCommodityOrder::getStatus,2l);
        wrapper.set(SxscCommodityOrder::getPayDateTime,DateUtils.getNowDate());
        update(wrapper);

        LambdaQueryWrapper<SxscCommodityOrder> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodityOrder::getId,id);
        log.info("*******订单支付状态修改完毕******");
        //优惠券抵押
        List<SxscCommodityOrder> commodityOrderList=list(queryWrapper);
        for(SxscCommodityOrder sxscCommodityOrder:commodityOrderList){
            iSxscUserConsumeBuyService.insertSxscUserConsumeBuy(sxscCommodityOrder);
        }

        //供应链支付
        for(SxscCommodityOrder sxscCommodityOrder:commodityOrderList){
            if(StringUtils.isNotEmpty(sxscCommodityOrder.getSupplierMapperOrderSn())){
                String orderPay=dingDongCommodityUtil.orderPay(sxscCommodityOrder.getSupplierMapperOrderSn());
                SxscDingdongCommodityOrderMapper dingdongCommodityOrderMapper=iSxscDingdongCommodityOrderMapperService.selectSxscDingdongCommodityOrderMapperByDingdongOrderId(sxscCommodityOrder.getSupplierMapperOrderSn());
                dingdongCommodityOrderMapper.setPayJson(orderPay);
                iSxscDingdongCommodityOrderMapperService.updateSxscDingdongCommodityOrderMapper(dingdongCommodityOrderMapper);
            }
        }
    }

    /**
     * 查询商品销售总额
     *
     * @param commodityId 商品主键
     * @return 结果
     */
    @Override
    public BigDecimal getCommodityOrderTotalSales(Long commodityId){

        return commodityOrderMapper.totalSales(5l,commodityId);
    }
    /**
     * 查询商品销售总数
     *
     * @param commodityId 商品主键
     * @return 结果
     */
    @Override
    public long getCommodityOrderAmountSales(Long commodityId){
        LambdaQueryWrapper<SxscCommodityOrder> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(SxscCommodityOrder::getCommodityId,commodityId);
        wrapper.eq(SxscCommodityOrder::getStatus,5);
        return count(wrapper);
    }

    /**
     * 查询当日销量最高的商品
     *
     * @return 结果
     */
    @Override
    public Long explosiveProducts(){
        return commodityOrderMapper.explosiveProducts();
    }

    @Autowired
    ISxscCommodityAfterSalesService iSxscCommodityAfterSalesService;

    /**
     * 确认收货业务逻辑处理
     * @param  commodityOrder
     * @return 结果
     */
    @Override
    @Transactional
    public void confirmReceipt(SxscCommodityOrder commodityOrder){
        SxscCommodity commodity=iSxscCommodityService.getById(commodityOrder.getCommodityId());
        if(StringUtils.isNull(commodity)){
            return;
        }
        if(commodity.getPlateId()==0){//普通商品
            ordinary(commodityOrder);
            orderWithdrawal(commodityOrder); //货款处理
        }else if(commodity.getPlateId()==2){ //富星品牌购
            int i=copartnerCommission(commodityOrder);//合伙人处理
            enterprise(commodityOrder,new BigDecimal("0.4"),IntegralBillConstants.jiazhi,i);//贡献值=成员购买个数x(单品零售价x40%)
            orderWithdrawal(commodityOrder); //货款处理
        }else if(commodity.getPlateId()==3){//富星拼团购
            int i=copartnerCommission(commodityOrder);//合伙人处理
            enterprise(commodityOrder,new BigDecimal("0.3"),IntegralBillConstants.Teamwork,i);
            orderWithdrawal(commodityOrder); //货款处理
        }else if(commodity.getPlateId()==4){ //优选购
            int i=copartnerCommission(commodityOrder);//合伙人处理
            enterprise(commodityOrder,new BigDecimal("0.5"),IntegralBillConstants.Preferred,i);
            orderWithdrawal(commodityOrder); //货款处理
        }else if(commodity.getPlateId()==9){ //共识购
            int i=copartnerCommission(commodityOrder);//合伙人处理
            consensus(commodityOrder,new BigDecimal("0.8"),IntegralBillConstants.consensus,i);
            orderWithdrawal(commodityOrder); //货款处理
        }else if(commodity.getPlateId()==10){ //新人购
            //按照订单比例赠送优惠券
            String consumeAmount=iSysConfigService.selectConfigByKey("sxsc.commodity.order.plate.xinrengou");
            //计算赠送张书
            int num=commodityOrder.getTotalAmount().intValue()/Integer.parseInt(consumeAmount)/6;
            for(int i=0;i<num;i++){
                iSxscUserConsumeService.insertSxscUserConsumeSystem(commodityOrder.getBuyerUserId(),new BigDecimal(consumeAmount));
            }
            orderWithdrawal(commodityOrder); //货款处理
        }else if(commodity.getPlateId()>4){//超值购专区
            int i=copartnerCommission(commodityOrder);//合伙人处理
            tegou(commodityOrder,i);
            orderWithdrawal(commodityOrder); //货款处理
        }
        //售后处理
        SxscCommodityAfterSales sxscCommodityAfterSales= iSxscCommodityAfterSalesService.selectSxscCommodityAfterSales(commodityOrder.getId());
        if(StringUtils.isNotNull(sxscCommodityAfterSales)){
            LambdaUpdateWrapper<SxscCommodityAfterSales> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.set(SxscCommodityAfterSales::getStatus,5l);
            updateWrapper.set(SxscCommodityAfterSales::getUpdateTime,DateUtils.getNowDate());
            updateWrapper.eq(SxscCommodityAfterSales::getId,sxscCommodityAfterSales.getId());
            iSxscCommodityAfterSalesService.update(updateWrapper);
        }
    }
    /**
     * 普通商品确认收货逻辑处理
     *
     * @param order 订单详细信息
     * @return 结果
     */
    private void ordinary(SxscCommodityOrder order){
        //商品总价=订单总价-税费-运费
        BigDecimal total=order.getTotalAmount().subtract(order.getFreight()).subtract(order.getTaxesFees());
        //利润=商品总价-总供货价
        BigDecimal profit=total.subtract(order.getTotalSupplyAmount());

        //直推人佣金=购买数量x(单品利润x5%)
        Long parentId=getParentId(order.getBuyerUserId());
        iSxscUserCommissionService.insertSxscUserCommission(parentId,order.getId(),profit.multiply(new BigDecimal("0.05")),"直推人佣金");

        //信托基金=购买数量x(单品利润x95%)
        BigDecimal proportion=new BigDecimal("0.95");
        iSxscBillTrustFundService.insertSxscBillTrustFund(profit.multiply(proportion),proportion,order.getId());

    }
    @Autowired
    private ISxscCommodityPlateService iSxscCommodityPlateService;

    @Autowired
    private ISxscUserAgentService iSxscUserAgentService;

    //超值购专区 佣金=产品利润x10%
    private void tegou(SxscCommodityOrder order,int i){
        SxscCommodity sxscCommodity=iSxscCommodityService.getById(order.getCommodityId());
        if(StringUtils.isNull(sxscCommodity)||StringUtils.isNull(sxscCommodity.getPlateId())){
           return;
        }
        SxscCommodityPlate commodityPlate=iSxscCommodityPlateService.selectSxscCommodityPlateById(sxscCommodity.getPlateId());
        if(StringUtils.isNull(commodityPlate)||StringUtils.isNull(commodityPlate.getParentId())||commodityPlate.getParentId()==0){
            return;
        }
        //商品总价=订单总价-税费-运费
        BigDecimal total=order.getTotalAmount().subtract(order.getFreight()).subtract(order.getTaxesFees());
        //利润=商品总价-总供货价
        BigDecimal profit=total.subtract(order.getTotalSupplyAmount());

        //佣金=直推人获得订单利润x10%
        Long parentId=getParentId(order.getBuyerUserId());
        iSxscUserCommissionService.insertSxscUserCommission(parentId, order.getId(), profit.multiply(new BigDecimal("0.1")),"直推人佣金");
        // 总利润*10%=代理分配佣金
        BigDecimal profitAgent=profit.multiply(new BigDecimal("0.1"));
        //代理佣金计算
        profitAgent(order.getId(),order.getCommodityName(),order.getAddressId(),profitAgent);
        //信托基金=产品总利润x75%
        BigDecimal proportion=new BigDecimal("0.75").subtract(new BigDecimal("0.05").multiply(new BigDecimal(i)));
        iSxscBillTrustFundService.insertSxscBillTrustFund(profit.multiply(proportion),proportion,order.getId());
        //全国代理
        getNational(order.getId(),profit);
    }

    //品牌购或者优选购或者拼团购计算逻辑
    //所有奖励(佣金、贡献值)以默认收货地址分配。如果推荐人是各级代理任意一个，那么可以同时多重获得身份奖励。
    private void enterprise(SxscCommodityOrder order,BigDecimal integralGxzProportion,String billName,int i){

        //商品总价=订单总价-税费-运费
        BigDecimal total=order.getTotalAmount().subtract(order.getFreight()).subtract(order.getTaxesFees());
        //利润=商品总价-总供货价
        BigDecimal profit=total.subtract(order.getTotalSupplyAmount());

        //贡献值计算
        iSxscBillIntegralService.insertSxscBillIntegral(order.getId(), billName,1l,total.multiply(integralGxzProportion),order.getBuyerUserId());

        //佣金=直推人->订单总利润x10%
        Long parentId=getParentId(order.getBuyerUserId());
        iSxscUserCommissionService.insertSxscUserCommission(parentId, order.getId(), profit.multiply(new BigDecimal("0.1")),"直推人佣金");

        //订单总利润*20%=代理分配佣金
        BigDecimal profitAgent=profit.multiply(new BigDecimal("0.1"));

        //代理获取佣金计算
        profitAgent(order.getId(),order.getCommodityName(),order.getAddressId(),profitAgent);

        //信托基金=分红利润70%
        BigDecimal proportion=new BigDecimal("0.75").subtract(new BigDecimal("0.05").multiply(new BigDecimal(i)));
        iSxscBillTrustFundService.insertSxscBillTrustFund(profit.multiply(proportion),proportion,order.getId());
        //全国代理
        getNational(order.getId(),profit);
    }


    //共识购
    //所有奖励(佣金、贡献值)以默认收货地址分配。如果推荐人是各级代理任意一个，那么可以同时多重获得身份奖励。
    private void consensus(SxscCommodityOrder order,BigDecimal integralGxzProportion,String billName,int i){

        //商品总价=订单总价-税费-运费
        BigDecimal total=order.getTotalAmount().subtract(order.getFreight()).subtract(order.getTaxesFees());
        //利润=商品总价-总供货价
        BigDecimal profit=total.subtract(order.getTotalSupplyAmount());

        //贡献值计算=利润的百分之的八十
        iSxscBillIntegralService.insertSxscBillIntegral(order.getId(), billName,1l,profit.multiply(integralGxzProportion),order.getBuyerUserId());

        //佣金=直推人->订单总利润x10%
        Long parentId=getParentId(order.getBuyerUserId());
        iSxscUserCommissionService.insertSxscUserCommission(parentId, order.getId(), profit.multiply(new BigDecimal("0.1")),"直推人佣金");

        //订单总利润*20%=代理分配佣金
        BigDecimal profitAgent=profit.multiply(new BigDecimal("0.1"));

        //代理获取佣金计算
        profitAgent(order.getId(),order.getCommodityName(),order.getAddressId(),profitAgent);

        //信托基金=分红利润70%
        BigDecimal proportion=new BigDecimal("0.70").subtract(new BigDecimal("0.05").multiply(new BigDecimal(i)));
        iSxscBillTrustFundService.insertSxscBillTrustFund(profit.multiply(proportion),proportion,order.getId());
        //全国代理
        getNational(order.getId(),profit);
        //团队长获得5%
        //待处理
    }

    @Autowired
    private ISxscAliPayWithdrawalService iSxscAliPayWithdrawalService;

    //货款逻辑处理
    private void orderWithdrawal(SxscCommodityOrder order){
        BigDecimal amount=order.getTotalSupplyAmount().add(order.getFreight()).add(order.getTaxesFees());
        if(amount.compareTo(BigDecimal.ZERO)>0){
            iSxscAliPayWithdrawalService.insertSxscAliPayWithdrawal(amount,order.getSellerUserId(),order.getId(),order.getCommodityName());
        }
    }

    //查询父级账号信息
    private Long getParentId(Long userId){
        SxscUserInfo userInfo=iSxscUserInfoService.getById(userId);
        if(StringUtils.isNull(userInfo)||StringUtils.isNull(userInfo.getParentId())){
            return 0l;
        }
        return userInfo.getParentId();
    }

    //查询全国代理
    private void getNational(String orderId,BigDecimal profitAgent){
        List<SxscUserAgent> nationals=iSxscUserAgentService.getNational();
        //佣金=全国代理->利润*5%
        BigDecimal provinceProportion=new BigDecimal("0.05");
        //为空利润进入信托基金
        if(StringUtils.isNotNull(nationals)&&nationals.size()>0){
            for(SxscUserAgent province:nationals){
                iSxscUserCommissionService.insertSxscUserCommission(province.getUserId(), orderId, profitAgent.multiply(provinceProportion.multiply(province.getProportion())),"全国代理佣金");
            }
        }else {
            iSxscBillTrustFundService.insertSxscBillTrustFund(profitAgent.multiply(provinceProportion),provinceProportion,orderId);
        }
    }

    /**
     * 代理获取佣金计算
     *
     * @param orderId 订单主键
     * @param orderName 订单名称
     * @param addressId 地址主键
     * @param profitAgent 代理可分配利润
     * @return 结果
     */
    private void profitAgent(String orderId,String orderName,Long addressId,BigDecimal profitAgent){
        SxscUserAddress sxscUserAddress =iSxscAddressService.getById(addressId);
        if(StringUtils.isNotNull(sxscUserAddress)){
            List<SxscUserAgent> provinces=iSxscUserAgentService.selectSxscUserAgentProvinceCode(sxscUserAddress.getProvinceCode());
            //佣金=省代理->可分配利润*20%
            BigDecimal provinceProportion=new BigDecimal("0.2");
            //佣金=市代理->可分配利润*30%
            BigDecimal cityProportion=new BigDecimal("0.3");
            //佣金=县代理->可分配利润*50%
            BigDecimal countyProportion=new BigDecimal("0.5");
            //为空利润进入信托基金
            if(StringUtils.isNotNull(provinces)&&provinces.size()>0){
                for(SxscUserAgent province:provinces){
                    iSxscUserCommissionService.insertSxscUserCommission(province.getUserId(), orderId, profitAgent.multiply(provinceProportion.multiply(province.getProportion())),"省级代理佣金");
                }
            }else {
                iSxscBillTrustFundService.insertSxscBillTrustFund(profitAgent.multiply(provinceProportion),provinceProportion,orderId);
            }
            List<SxscUserAgent> citys=iSxscUserAgentService.selectSxscUserAgentCityCode(sxscUserAddress.getCityCode());
            if(StringUtils.isNotNull(citys)&&citys.size()>0){
                for(SxscUserAgent city:citys){
                    iSxscUserCommissionService.insertSxscUserCommission(city.getUserId(), orderId, profitAgent.multiply(cityProportion.multiply(city.getProportion())),"市级代理佣金");
                }
            }else {
                iSxscBillTrustFundService.insertSxscBillTrustFund(profitAgent.multiply(cityProportion),cityProportion,orderId);
            }
            List<SxscUserAgent> countys=iSxscUserAgentService.selectSxscUserAgentCountyCode(sxscUserAddress.getCountyCode());
            if(StringUtils.isNotNull(countys)&&countys.size()>0){
                for(SxscUserAgent county:countys){
                    iSxscUserCommissionService.insertSxscUserCommission(county.getUserId(), orderId, profitAgent.multiply(countyProportion.multiply(county.getProportion())),"县级代理佣金");
                }
            }else {
                //信托基金
                iSxscBillTrustFundService.insertSxscBillTrustFund(profitAgent.multiply(countyProportion),countyProportion,orderId);
            }
        }
    }

    /**
     * 合伙人获取佣金计算
     *
     * @param order 订单
     * @return 结果
     */
    private int copartnerCommission(SxscCommodityOrder order){
        //商品总价=订单总价-税费-运费
        BigDecimal total=order.getTotalAmount().subtract(order.getFreight()).subtract(order.getTaxesFees());
        //利润=商品总价-总供货价
        BigDecimal profit=total.subtract(order.getTotalSupplyAmount());
        String orderId=order.getId();
        BigDecimal commission=profit.multiply(new BigDecimal("0.05"));
        Long userId=order.getBuyerUserId();
        List<SxscUserInfo> list=sxscUserInfoMapper.userIdByNodeAcc(userId);
        int i=0;
        for(SxscUserInfo sxscUserInfo:list){
            if(sxscUserInfo.getCopartner()==1){
                iSxscUserCommissionService.insertSxscUserCommission(sxscUserInfo.getUserId(), orderId, commission,"初级合伙人佣金");
                i++;
            }else if(sxscUserInfo.getCopartner()==2){
                iSxscUserCommissionService.insertSxscUserCommission(sxscUserInfo.getUserId(), orderId, commission,"中级合伙人佣金");
                i++;
            }else if(sxscUserInfo.getCopartner()==3){
                iSxscUserCommissionService.insertSxscUserCommission(sxscUserInfo.getUserId(), orderId, commission,"高级合伙人佣金");
                i++;
            }else if(sxscUserInfo.getCopartner()==4){
                iSxscUserCommissionService.insertSxscUserCommission(sxscUserInfo.getUserId(), orderId, commission,"战略合伙人佣金");
                i++;
            }
        }
        return i;
    }
    /**
     * 商家查询订单数量合集
     * @return 结果
     */
    @Override
    public AjaxResult sellerUserCount(Long userId){
        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            userId=SecurityUtils.getUserId();
        }
        Map<String,Object> map=new HashMap<>();
        LambdaQueryWrapper<SxscCommodityOrder>  queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodityOrder::getSellerUserId,userId);
        queryWrapper.eq(SxscCommodityOrder::getStatus,2);
        queryWrapper.eq(SxscCommodityOrder::getAfterSalesFlag,0);
        map.put("pendingShipment",count(queryWrapper));
        LambdaQueryWrapper<SxscCommodityAfterSales>  salesQueryWrapper=new LambdaQueryWrapper<>();
        salesQueryWrapper.apply("commodity_order_id in (select id from sxsc_commodity_order where status!=6 and seller_user_id="+userId+")");
        salesQueryWrapper.and(wrapper->wrapper.eq(SxscCommodityAfterSales::getStatus,0).or().eq(SxscCommodityAfterSales::getStatus,1));
        map.put("afterSales",iSxscCommodityAfterSalesService.count(salesQueryWrapper));
        LambdaQueryWrapper<SxscCommodityOrder>  shippedQueryWrapper=new LambdaQueryWrapper<>();
        shippedQueryWrapper.eq(SxscCommodityOrder::getSellerUserId,userId);
        shippedQueryWrapper.eq(SxscCommodityOrder::getStatus,3);
        shippedQueryWrapper.eq(SxscCommodityOrder::getAfterSalesFlag,0);
        map.put("shipped",count(shippedQueryWrapper));
        return AjaxResult.success(map);
    }

    /**
     * 修改商品订单信息
     *
     * @param userId 用户主键
     * @return 结果
     */
    @Override
    public  AjaxResult optimalSelection(Long userId){

        SxscCommodityOrder sxscCommodityOrder=getById("e16dd02d8f6c4acd880a5d95b4d34675");
        if(StringUtils.isNotNull(sxscCommodityOrder)){
            sxscCommodityOrder.setId(IdUtils.fastSimpleUUID());
            sxscCommodityOrder.setBuyerUserId(userId);
            save(sxscCommodityOrder);
            return AjaxResult.success();
        }
        return AjaxResult.error("系统异常，请联系管理员");
    }

}

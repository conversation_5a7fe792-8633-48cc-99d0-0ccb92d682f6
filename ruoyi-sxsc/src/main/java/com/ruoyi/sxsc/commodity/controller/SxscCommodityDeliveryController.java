package com.ruoyi.sxsc.commodity.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityDelivery;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 订单物流信息
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@RestController
@RequestMapping("/commodity/delivery")
public class SxscCommodityDeliveryController extends BaseController
{
    @Autowired
    private ISxscCommodityDeliveryService sxscCommodityDeliveryService;

    /**
     * 查询订单物流信息列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:delivery:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityDelivery sxscCommodityDelivery)
    {
        startPage();
        List<SxscCommodityDelivery> list = sxscCommodityDeliveryService.selectSxscCommodityDeliveryList(sxscCommodityDelivery);
        return getDataTable(list);
    }


    @Log(title = "订单物流信息", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('commodity:delivery:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SxscCommodityDelivery> util = new ExcelUtil<>(SxscCommodityDelivery.class);
        List<SxscCommodityDelivery> deliveries = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = sxscCommodityDeliveryService.importDelivery(deliveries, updateSupport, operName);
        return success(message);
    }

    /**
     * 获取订单物流信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:delivery:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscCommodityDeliveryService.selectSxscCommodityDeliveryById(id));
    }

    /**
     * 新增订单物流信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:delivery:add')")
    @Log(title = "订单物流信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodityDelivery sxscCommodityDelivery)
    {
        return sxscCommodityDeliveryService.insertSxscCommodityDelivery(sxscCommodityDelivery);
    }

    /**
     * 修改订单物流信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:delivery:edit')")
    @Log(title = "订单物流信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscCommodityDelivery sxscCommodityDelivery)
    {
        return toAjax(sxscCommodityDeliveryService.updateSxscCommodityDelivery(sxscCommodityDelivery));
    }


}

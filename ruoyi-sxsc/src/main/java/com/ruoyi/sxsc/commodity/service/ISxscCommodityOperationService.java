package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOperation;

import java.util.List;

/**
 * 商品操作记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public interface ISxscCommodityOperationService extends IService<SxscCommodityOperation>
{
    /**
     * 查询商品操作记录
     * 
     * @param id 商品操作记录主键
     * @return 商品操作记录
     */
    SxscCommodityOperation selectSxscCommodityOperationById(Long id);

    /**
     * 查询商品操作记录列表
     * 
     * @param sxscCommodityOperation 商品操作记录
     * @return 商品操作记录集合
     */
    List<SxscCommodityOperation> selectSxscCommodityOperationList(SxscCommodityOperation sxscCommodityOperation);

    /**
     * 新增商品操作记录
     * 
     * @param sxscCommodityOperation 商品操作记录
     * @return 结果
     */
    AjaxResult insertSxscCommodityOperation(SxscCommodityOperation sxscCommodityOperation);

    /**
     * 新增商品操作记录
     *
     * @param commodityId 商品主键
     * @param operationType 操作类型
     * @return 结果
     */
    void insertSxscCommodityOperation(Long commodityId,Long operationType);
}

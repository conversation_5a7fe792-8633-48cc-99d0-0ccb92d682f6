package com.ruoyi.sxsc.commodity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityStaticCodeOrder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 静态收款码订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface SxscCommodityStaticCodeOrderMapper extends BaseMapper<SxscCommodityStaticCodeOrder>
{


    /**
     * 查询当日流水总额
     * @param  payDateTime 支付时间
     * @return  利润总额
     */
    @Select("select IFNULL(sum(total_amount), 0) from sxsc_commodity_static_code_order " +
            "where DATE_FORMAT(receipt_date,'%Y-%m-%d') =#{payDateTime} and status=1")
    BigDecimal transactionAmount(@Param("payDateTime")String payDateTime);

}

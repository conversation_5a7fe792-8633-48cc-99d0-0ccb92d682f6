package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 订单售后信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityAfterSales extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 订单主键 */
    private String commodityOrderId;

    /** 退款物流主键 */
    @Excel(name = "退款物流主键")
    private Long deliveryId;

    /** 退款图片 */
    @Excel(name = "退款图片")
    private String refundImg;

    /** 退款原因 */
    @Excel(name = "退款原因")
    private String refundReason;

    /** 退款类型1退货退款2申请仅退款 */
    @Excel(name = "退款类型1退货退款2申请仅退款")
    private Long refundType;

    /** 退货地址信息 */
    @Excel(name = "退货地址信息")
    private Long addressId;

    /** 拒绝退货原因 */
    @Excel(name = "拒绝退货原因")
    private String refuseGoods;

    /** 拒绝退款原因 */
    @Excel(name = "拒绝退款原因")
    private String refuseAmount;

    /** 售后状态0已提交1同意退货2拒绝退货3卖家同意退款4卖家拒绝退款5已完成6已关闭 */
    @Excel(name = "售后状态0已提交1同意退货2拒绝退货3卖家同意退款4卖家拒绝退款5已完成6已关闭")
    private Long status;

    /** 退款状态 1退款成功0未处理2退款失败 */
    @Excel(name = "退款状态1退款成功0未处理2退款失败")
    private Long refundStatus;

    /** 退款到账时间 */
    @Excel(name = "退款到账时间")
    private Date refundTime;

}

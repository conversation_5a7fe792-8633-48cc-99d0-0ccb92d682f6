package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityEvaluate;

import java.math.BigDecimal;
import java.util.List;

/**
 * 评价信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
public interface ISxscCommodityEvaluateService extends IService<SxscCommodityEvaluate>
{

    /**
     * 查询评价信息
     *
     * @param orderId 订单主键
     * @return 评价信息
     */
    SxscCommodityEvaluate selectSxscCommodityEvaluateByCommodityOrderId(String orderId);

    /**
     * 查询评价信息
     * 
     * @param id 评价信息主键
     * @return 评价信息
     */
    SxscCommodityEvaluate selectSxscCommodityEvaluateById(Long id);


    /**
     * 获取商品评价信息好评率
     *
     * @param commodityId 商品主键
     * @return 评价信息
     */
    BigDecimal getRate(Long commodityId);

    /**
     * 获取商家商品描述评分平均值
     *
     * @param userId 用户主键
     * @return 评价信息
     */
    BigDecimal describeStarLevel(Long userId);

    /**
     * 获取卖家服务评分平均值
     *
     * @param userId 用户主键
     * @return 评价信息
     */
    BigDecimal sellerStarLevel(Long userId);

    /**
     * 获取物流服务评分平均值
     *
     * @param userId 用户主键
     * @return 评价信息
     */
    BigDecimal logisticsStarLevel(Long userId);

    /**
     * 查询评价信息列表
     * 
     * @param sxscCommodityEvaluate 评价信息
     * @return 评价信息集合
     */
    List<SxscCommodityEvaluate> selectSxscCommodityEvaluateList(SxscCommodityEvaluate sxscCommodityEvaluate);

    /**
     * 新增评价信息
     * 
     * @param sxscCommodityEvaluate 评价信息
     * @return 结果
     */
    AjaxResult insertSxscCommodityEvaluate(SxscCommodityEvaluate sxscCommodityEvaluate);

    /**
     * 新增评价信息
     *
     * @param sxscCommodityEvaluate 评价信息
     * @return 结果
     */
    void insertSxscCommodityEvaluateTask(SxscCommodityEvaluate sxscCommodityEvaluate);

    /**
     * 修改评价信息
     * 
     * @param sxscCommodityEvaluate 评价信息
     * @return 结果
     */
    int updateSxscCommodityEvaluate(SxscCommodityEvaluate sxscCommodityEvaluate);

    /**
     * 批量删除评价信息
     * 
     * @param ids 需要删除的评价信息主键集合
     * @return 结果
     */
    int deleteSxscCommodityEvaluateByIds(Long[] ids);

}

package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.model.SxscCommodityOrderModel;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品订单信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface ISxscCommodityOrderService extends IService<SxscCommodityOrder>
{
    /**
     * 查询商品订单信息
     * 
     * @param id 商品订单信息主键
     * @return 商品订单信息
     */
    SxscCommodityOrder selectSxscCommodityOrderById(String id);
    /**
     * 查询商品订单信息
     *
     * @param DingdongSn 供应商商品主键
     * @return 商品订单信息
     */
    SxscCommodityOrder selectSxscCommodityOrderByDingdongSn(String DingdongSn);

    /**
     * 查询商品订单信息列表
     * 
     * @param sxscCommodityOrder 商品订单信息
     * @return 商品订单信息集合
     */
    List<SxscCommodityOrder> selectSxscCommodityOrderList(SxscCommodityOrder sxscCommodityOrder);

    /**
     * 新增商品订单信息
     * 
     * @param sxscCommodityOrder 商品订单信息
     * @return 结果
     */
    String insertSxscCommodityOrder(SxscCommodityOrderModel sxscCommodityOrder);

    /**
     * 修改商品订单信息
     * 
     * @param sxscCommodityOrder 商品订单信息
     * @return 结果
     */
    int updateSxscCommodityOrder(SxscCommodityOrder sxscCommodityOrder);


    /**
     * 修改商品订单信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    AjaxResult dingdongRestartPayment(String orderId);
    /**
     * 修改商品订单信息
     *
     * @param id 商品订单主键
     * @param type 支付类型
     * @return 结果
     */
    AjaxResult updateSxscCommodityOrderPayment(String id, Long type);


    /**
     * 修改商品订单状态
     *
     * @param orderId 商品订单主键
     * @param status 订单状态1待付款2待发货3待收货4待评价5已完成7取消
     * @return 结果
     */
    AjaxResult updateSxscCommodityOrderStatus(String orderId,Long status);



    /**
     * 支付成功修改商品订单信息
     *
     * @param id 订单批次主键
     * @param actualAmount 支付金额
     * @return 结果
     */
    void updateXgfmCommodityOrderPayment(String id, BigDecimal actualAmount);


    /**
     * 查询商品销售总额
     *
     * @param commodityId 商品主键
     * @return 结果
     */
    BigDecimal getCommodityOrderTotalSales(Long commodityId);


    /**
     * 查询商品销售总数
     *
     * @param commodityId 商品主键
     * @return 结果
     */
    long getCommodityOrderAmountSales(Long commodityId);

    /**
     * 查询当日销量最高的商品
     *
     * @return 结果
     */
    Long explosiveProducts();


    /**
     * 确认收货业务逻辑处理
     * @param  commodityOrder
     * @return 结果
     */
    void confirmReceipt(SxscCommodityOrder commodityOrder);

    /**
     * 商家查询订单数量合集
     * @return 结果
     */
    AjaxResult sellerUserCount(Long userId);


    /**
     * 修改商品订单信息
     *
     * @param userId 用户主键
     * @return 结果
     */
    AjaxResult optimalSelection(Long userId);
}

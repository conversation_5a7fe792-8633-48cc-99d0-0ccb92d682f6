package com.ruoyi.sxsc.commodity.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityStaticCodeOrder;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityStaticCodeOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 静态收款码订单
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@RestController
@RequestMapping("/commodity/staticOrder")
public class SxscCommodityStaticCodeOrderController extends BaseController
{
    @Autowired
    private ISxscCommodityStaticCodeOrderService sxscCommodityStaticCodeOrderService;

    /**
     * 查询静态收款码订单列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:staticOrder:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityStaticCodeOrder sxscCommodityStaticCodeOrder)
    {
        startPage();
        List<SxscCommodityStaticCodeOrder> list = sxscCommodityStaticCodeOrderService.selectSxscCommodityStaticCodeOrderList(sxscCommodityStaticCodeOrder);
        return getDataTable(list);
    }

    /**
     * 获取静态收款码订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:staticOrder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscCommodityStaticCodeOrderService.selectSxscCommodityStaticCodeOrderById(id));
    }

    /**
     * 新增静态收款码订单
     */
    @Log(title = "静态收款码订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodityStaticCodeOrder sxscCommodityStaticCodeOrder)
    {
        return sxscCommodityStaticCodeOrderService.insertSxscCommodityStaticCodeOrder(sxscCommodityStaticCodeOrder);
    }


}

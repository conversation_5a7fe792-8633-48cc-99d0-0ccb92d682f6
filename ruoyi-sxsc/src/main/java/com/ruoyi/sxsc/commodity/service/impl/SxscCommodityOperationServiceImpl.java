package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOperation;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityOperationMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOperationService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderRefundService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品操作记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Service
public class SxscCommodityOperationServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityOperationMapper,SxscCommodityOperation> implements ISxscCommodityOperationService
{

    @Autowired
    private ISxscCommodityService iSxscCommodityService;

    @Autowired
    private ISxscCommodityOrderRefundService iSxscCommodityOrderRefundService;

    /**
     * 查询商品操作记录
     * 
     * @param id 商品操作记录主键
     * @return 商品操作记录
     */
    @Override
    public SxscCommodityOperation selectSxscCommodityOperationById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询商品操作记录列表
     * 
     * @param sxscCommodityOperation 商品操作记录
     * @return 商品操作记录
     */
    @Override
    public List<SxscCommodityOperation> selectSxscCommodityOperationList(SxscCommodityOperation sxscCommodityOperation)
    {
        LambdaQueryWrapper<SxscCommodityOperation> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOperation.getCommodityId()),SxscCommodityOperation::getCommodityId,sxscCommodityOperation.getCommodityId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodityOperation.getOperationType()),SxscCommodityOperation::getOperationType,sxscCommodityOperation.getOperationType());

        wrapper.orderByDesc(SxscCommodityOperation::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增商品操作记录
     * 
     * @param sxscCommodityOperation 商品操作记录
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscCommodityOperation(SxscCommodityOperation sxscCommodityOperation)
    {
        SxscCommodity sxscCommodity=iSxscCommodityService.getById(sxscCommodityOperation.getCommodityId());
        if(StringUtils.isNull(sxscCommodity)){
            AjaxResult.error("商品不存在");
        }
        if(sxscCommodityOperation.getOperationType()==1){
            LambdaQueryWrapper<SxscCommodity> wrapper=new LambdaQueryWrapper<>();
            wrapper.eq(SxscCommodity::getStatus,2);
            wrapper.orderByDesc(SxscCommodity::getSortNumber);
            wrapper.last(" limit 1");
            SxscCommodity sxscCommodityData=iSxscCommodityService.getOne(wrapper);
            iSxscCommodityService.updateSxscCommoditySortNumber(sxscCommodity.getId(),sxscCommodityData.getSortNumber()+1);
        }
        //删除或者不通过，给予退款
        if(sxscCommodityOperation.getOperationType()==4||sxscCommodityOperation.getOperationType()==5){
            if(StringUtils.isNotEmpty(sxscCommodity.getPayOrderId())){
                iSxscCommodityOrderRefundService.insertSxscCommodityOrderRefund(sxscCommodity);
            }
        }
        iSxscCommodityService.updateCommodityStatus(sxscCommodityOperation.getCommodityId(),sxscCommodityOperation.getOperationType());
        sxscCommodityOperation.setCreateBy(SecurityUtils.getUsername());
        sxscCommodityOperation.setCreateTime(DateUtils.getNowDate());
        save(sxscCommodityOperation);
        return AjaxResult.success();
    }

    /**
     * 新增商品操作记录
     *
     * @param commodityId 商品主键
     * @param operationType 操作类型
     * @return 结果
     */
    public void insertSxscCommodityOperation(Long commodityId,Long operationType){
        SxscCommodityOperation sxscCommodityOperation=new SxscCommodityOperation();
        sxscCommodityOperation.setCreateBy(SecurityUtils.getUsername());
        sxscCommodityOperation.setOperationType(operationType);
        sxscCommodityOperation.setCommodityId(commodityId);
        sxscCommodityOperation.setCreateTime(DateUtils.getNowDate());
        save(sxscCommodityOperation);
    }
}

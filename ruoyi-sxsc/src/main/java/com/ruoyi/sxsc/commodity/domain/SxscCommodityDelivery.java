package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 订单物流信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityDelivery extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户主键 */
    private Long userId;

    /** 订单主键 */
    @Excel(name = "订单编号")
    private String commodityOrderId;

    /** 快递公司 */
    @Excel(name = "快递公司")
    private String deliveryName;

    /** 收货人手机号 */
    @Excel(name = "收货人手机号")
    private String deliveryPhone;

    /** 快递单号 */
    @Excel(name = "快递单号")
    private String deliveryNumber;

    /** 类型(1发货2退货） */
    private Long type;

    /** 轨迹信息 */
    @TableField(exist = false)
    private Object track;

}

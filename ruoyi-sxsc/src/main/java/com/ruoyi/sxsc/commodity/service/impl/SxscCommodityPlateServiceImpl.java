package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityPlate;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityPlateMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityPlateService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品板块Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class SxscCommodityPlateServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscCommodityPlateMapper,SxscCommodityPlate> implements ISxscCommodityPlateService
{


    @Autowired
    private ISxscCommodityService iSxscCommodityService;

    /**
     * 查询商品板块
     * 
     * @param id 商品板块主键
     * @return 商品板块
     */
    @Override
    public SxscCommodityPlate selectSxscCommodityPlateById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询商品板块列表
     * 
     * @param sxscCommodityPlate 商品板块
     * @return 商品板块
     */
    @Override
    public List<SxscCommodityPlate> selectSxscCommodityPlateList(SxscCommodityPlate sxscCommodityPlate)
    {
        LambdaQueryWrapper<SxscCommodityPlate> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodityPlate.getParentId()),SxscCommodityPlate::getParentId,sxscCommodityPlate.getParentId());

        wrapper.like(StringUtils.isNotNull(sxscCommodityPlate.getName()),SxscCommodityPlate::getName,sxscCommodityPlate.getName());

        wrapper.orderByAsc(SxscCommodityPlate::getSort);

        wrapper.eq(SxscCommodityPlate::getDelFlag,0l);

        return list(wrapper);
    }

    /**
     * 新增商品板块
     * 
     * @param sxscCommodityPlate 商品板块
     * @return 结果
     */
    @Override
    public int insertSxscCommodityPlate(SxscCommodityPlate sxscCommodityPlate)
    {
        sxscCommodityPlate.setDelFlag(0l);
        sxscCommodityPlate.setCreateBy(SecurityUtils.getUsername());
        sxscCommodityPlate.setCreateTime(DateUtils.getNowDate());
        return save(sxscCommodityPlate)?1:0;
    }

    /**
     * 修改商品板块
     * 
     * @param sxscCommodityPlate 商品板块
     * @return 结果
     */
    @Override
    public int updateSxscCommodityPlate(SxscCommodityPlate sxscCommodityPlate)
    {
        sxscCommodityPlate.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodityPlate.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscCommodityPlate)?1:0;
    }

    /**
     * 删除商品板块信息
     * 
     * @param id 商品板块主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteSxscCommodityPlateById(Long id)
    {
        LambdaQueryWrapper<SxscCommodityPlate> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscCommodityPlate::getParentId,id);
        wrapper.eq(SxscCommodityPlate::getDelFlag,0l);
        if(count(wrapper)>0){
            return AjaxResult.error("商品板块存在子级，无法删除");
        }
        LambdaQueryWrapper<SxscCommodity> commodityLambdaQueryWrapper=new LambdaQueryWrapper();
        commodityLambdaQueryWrapper.eq(SxscCommodity::getType,id);
        commodityLambdaQueryWrapper.ne(SxscCommodity::getStatus,4);
        if(iSxscCommodityService.count(commodityLambdaQueryWrapper)>0){
            return AjaxResult.error("商品类别已关联商品，无法删除");
        }
        LambdaUpdateWrapper<SxscCommodityPlate> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscCommodityPlate::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscCommodityPlate::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.set(SxscCommodityPlate::getDelFlag,1l);
        updateWrapper.in(SxscCommodityPlate::getId,id);
        update(updateWrapper);
        return AjaxResult.success();
    }
}

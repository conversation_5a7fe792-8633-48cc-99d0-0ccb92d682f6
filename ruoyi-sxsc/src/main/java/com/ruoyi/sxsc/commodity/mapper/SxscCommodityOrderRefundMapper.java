package com.ruoyi.sxsc.commodity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrderRefund;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 订单退款Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface SxscCommodityOrderRefundMapper extends BaseMapper<SxscCommodityOrderRefund>
{

    @Select("<script>"+
            "select IFNULL(sum(amount), 0) from sxsc_commodity_order_refund " +
            "where status=1" +
            "<if test=' month!=null and month!=\"\" '>  and DATE_FORMAT(create_time,'%Y-%m') =#{month}   </if>"+
            "</script>")
    BigDecimal aliPayRefundSum( @Param("month")String month);


}

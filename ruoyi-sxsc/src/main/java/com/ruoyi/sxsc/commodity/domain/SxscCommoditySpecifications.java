package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 商品规格对象
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommoditySpecifications extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 图片地址 */
    @Excel(name = "图片地址")
    private String imgUrl;

    /** 商品主键 */
    @Excel(name = "商品主键")
    private Long commodityId;

    /** 规格名称 */
    @Excel(name = "规格名称")
    private String name;

    /** 库存数量 */
    @Excel(name = "库存数量")
    private Long stock;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal price;

    /** 供货价格 */
    @Excel(name = "供货价格")
    private BigDecimal supplyAmount;

    /** 供应商映射主键 */
    @Excel(name = "供应商映射主键")
    private Long supplierMapperId;

    /** 起订量 */
    @Excel(name = "起订量")
    private Long moq;

    /** 供货价格浮动比例 */
    @Excel(name = "供货价格浮动比例")
    private BigDecimal floatingRatio;

    /** 供应商零售价格 */
    @Excel(name = "供应商零售价格")
    private BigDecimal supplierPrice;

    /** 是否删除0未删除 */
    @Excel(name = "是否删除0未删除")
    private Long delFlag;

    public SxscCommoditySpecifications(Long commodityId){
        this.commodityId=commodityId;
    }


}

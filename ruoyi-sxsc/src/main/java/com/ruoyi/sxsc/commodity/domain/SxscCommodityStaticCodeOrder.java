package com.ruoyi.sxsc.commodity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 静态收款码订单对象
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscCommodityStaticCodeOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 商家标识 */
    @TableField(exist = false)
    private String businessPersonnelId;

    /** 订单名称 */
    @Excel(name = "订单名称")
    private String subject;

    /** 卖家用户主键 */
    @Excel(name = "卖家用户主键")
    private Long sellerUserId;

    /** 买家用户主键 */
    @Excel(name = "买家用户主键")
    private Long buyerUserId;

    /** 利润比例 */
    @Excel(name = "利润比例")
    private BigDecimal proportion;

    /** 订单金额 */
    @Excel(name = "订单金额")
    private BigDecimal totalAmount;

    /** 状态0待付款1已付款 */
    @Excel(name = "状态0待付款1已付款")
    private Long status;

    /** 支付成功时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付成功时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiptDate;

    /** 支付类型1支付宝2微信 */
    @Excel(name = "支付类型1支付宝2微信")
    private Long payType;

    /** 卖家用户信息 */
    @TableField(exist = false)
    private SysUserMain sellerUser;

    /** 买家用户信息 */
    @TableField(exist = false)
    private SysUserMain buyerUser;


}

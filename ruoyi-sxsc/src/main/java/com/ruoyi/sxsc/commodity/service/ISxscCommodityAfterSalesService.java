package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityAfterSales;

import java.util.List;

/**
 * 订单售后信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface ISxscCommodityAfterSalesService extends IService<SxscCommodityAfterSales>
{


    /**
     * 查询订单售后信息
     *
     * @param id 订单售后信息主键
     * @return 订单售后信息
     */
    SxscCommodityAfterSales selectSxscCommodityAfterSalesById(String id);

    /**
     * 查询订单售后信息
     *
     * @param commodityOrderId 订单主键
     * @return 订单售后信息
     */
    SxscCommodityAfterSales selectSxscCommodityAfterSales(String commodityOrderId);
    /**
     * 查询订单售后信息
     * 
     * @param commodityOrderId 订单售后信息主键
     * @return 订单售后信息
     */
    SxscCommodityAfterSales selectSxscCommodityAfterSalesByCommodityOrderId(String commodityOrderId,Long status);

    /**
     * 查询订单售后信息列表
     * 
     * @param sxscCommodityAfterSales 订单售后信息
     * @return 订单售后信息集合
     */
    List<SxscCommodityAfterSales> selectSxscCommodityAfterSalesList(SxscCommodityAfterSales sxscCommodityAfterSales);

    /**
     * 新增订单售后信息
     * 
     * @param sxscCommodityAfterSales 订单售后信息
     * @return 结果
     */
    AjaxResult insertSxscCommodityAfterSales(SxscCommodityAfterSales sxscCommodityAfterSales);

    /**
     * 修改订单售后信息
     * 
     * @param sxscCommodityAfterSales 订单售后信息
     * @return 结果
     */
    AjaxResult updateSxscCommodityAfterSales(SxscCommodityAfterSales sxscCommodityAfterSales);


    /**
     * 修改商品订单售后信息
     *
     * @param sxscCommodityAfterSales 商品订单售后信息
     * @return 结果
     */
    AjaxResult updateCommodityOrderAfterSalesAmount(SxscCommodityAfterSales sxscCommodityAfterSales);

    /**
     * 修改商品订单售后信息状态
     *
     * @param id 售后主键
     * @param status 状态
     * @return 结果
     */
    AjaxResult updateCommodityOrderAfterSalesStatus(String id,Long status);

    /**
     * 修改商品订单售后信息状态
     *
     * @param orderId 订单主键
     * @param status 状态
     * @return 结果
     */
    void updateCommodityOrderAfterSalesStatusByOrderId(String orderId,Long status);
}

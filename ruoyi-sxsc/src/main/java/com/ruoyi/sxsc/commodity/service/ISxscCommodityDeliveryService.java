package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityDelivery;

import java.util.List;

/**
 * 订单物流信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface ISxscCommodityDeliveryService extends IService<SxscCommodityDelivery>
{
    /**
     * 查询订单物流信息
     * 
     * @param id 订单物流信息主键
     * @return 订单物流信息
     */
    SxscCommodityDelivery selectSxscCommodityDeliveryById(Long id);


    /**
     * 查询订单物流信息
     *
     * @param orderId 订单主键
     * @param type 类型1发货2退货
     * @return 订单物流信息
     */
    SxscCommodityDelivery selectSxscCommodityDeliveryByOrderId(String orderId,Long type);

    /**
     * 查询订单物流信息列表
     * 
     * @param sxscCommodityDelivery 订单物流信息
     * @return 订单物流信息集合
     */
    List<SxscCommodityDelivery> selectSxscCommodityDeliveryList(SxscCommodityDelivery sxscCommodityDelivery);

    /**
     * 新增订单物流信息
     * 
     * @param sxscCommodityDelivery 订单物流信息
     * @return 结果
     */
    AjaxResult insertSxscCommodityDelivery(SxscCommodityDelivery sxscCommodityDelivery);

    /**
     * 修改订单物流信息
     * 
     * @param sxscCommodityDelivery 订单物流信息
     * @return 结果
     */
    int updateSxscCommodityDelivery(SxscCommodityDelivery sxscCommodityDelivery);

    /**
     * 导入用户数据
     *
     * @param deliveries 数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importDelivery(List<SxscCommodityDelivery> deliveries, Boolean isUpdateSupport, String operName);
}

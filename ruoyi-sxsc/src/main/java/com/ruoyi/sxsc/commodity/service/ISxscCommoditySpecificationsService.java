package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.commodity.domain.SxscCommoditySpecifications;

import java.util.List;

/**
 * 商品规格Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public interface ISxscCommoditySpecificationsService extends IService<SxscCommoditySpecifications>
{
    /**
     * 查询商品规格
     * 
     * @param id 商品规格主键
     * @return 商品规格
     */
    SxscCommoditySpecifications selectSxscCommoditySpecificationsById(Long id);


    /**
     * 查询商品规格
     *
     * @param supplierMapperId 供应商商品规格主键
     * @return 商品规格
     */
    SxscCommoditySpecifications selectSxscCommoditySpecificationsBySupplierMapperId(Long supplierMapperId);
    /**
     * 查询商品规格列表
     * 
     * @param sxscCommoditySpecifications 商品规格
     * @return 商品规格集合
     */
    List<SxscCommoditySpecifications> selectSxscCommoditySpecificationsList(SxscCommoditySpecifications sxscCommoditySpecifications);


    /**
     * 新增商品规格
     * 
     * @param sxscCommoditySpecifications 商品规格
     * @return 结果
     */
    void insertSxscCommoditySpecifications(List<SxscCommoditySpecifications> sxscCommoditySpecifications,Long commodityId);

    /**
     * 修改商品规格库存
     *
     * @param id 主键
     * @param stock 数量
     * @return 结果
     */
    void updateSxscCommoditySpecifications(Long id,Long stock);

    /**
     * 修改商品规格库存
     *
     * @param supplierMapperId 供应链商品主键
     * @param stock 数量
     * @param moq 起订数量
     * @return 结果
     */
    void updateSxscCommoditySpecificationsBySupplierMapperId(Integer supplierMapperId,Integer stock,Integer moq);


    /**
     * 删除商品规格信息
     * 
     * @param commodityId 商品主键
     * @return 结果
     */
    void deleteSxscCommoditySpecificationsByCommodityId(Long commodityId);
}

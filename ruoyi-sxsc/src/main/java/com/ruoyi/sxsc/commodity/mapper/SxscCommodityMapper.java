package com.ruoyi.sxsc.commodity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 商品管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public interface SxscCommodityMapper extends BaseMapper<SxscCommodity>
{


    @Update("update sxsc_commodity set sort_number = sort_number+1 where sort_number between #{newIndex} and #{oldIndex}")
    Boolean updateForward(@Param("oldIndex") Long oldIndex, @Param("newIndex") Long newIndexLong );

    @Update("update sxsc_commodity set sort_number = sort_number-1 where sort_number between #{oldIndex} and #{newIndex}")
    Boolean updateBackwards(@Param("oldIndex")Long oldIndex, @Param("newIndex")Long newIndex);

    @Update(" update sxsc_commodity set sort_number = #{newIndex} where id = #{id}")
    Boolean updateConfirm(@Param("id")Long id,@Param("newIndex")Long newIndex);

}

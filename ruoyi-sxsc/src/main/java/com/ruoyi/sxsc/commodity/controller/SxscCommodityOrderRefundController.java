package com.ruoyi.sxsc.commodity.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.bill.service.ISxscBillTrustFundService;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrderRefund;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityAfterSalesService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderRefundService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单退款
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/commodity/refund")
public class SxscCommodityOrderRefundController extends BaseController
{
    @Autowired
    private ISxscCommodityOrderRefundService sxscCommodityOrderRefundService;

    @Autowired
    private ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    private ISxscUserCommissionService iSxscUserCommissionService;

    @Autowired
    private ISxscBillIntegralService iSxscBillIntegralService;

    @Autowired
    private ISxscBillTrustFundService iSxscBillTrustFundService;

    @Autowired
    private ISxscCommodityAfterSalesService iSxscCommodityAfterSalesService;


    /**
     * 查询订单退款列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:refund:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityOrderRefund sxscCommodityOrderRefund)
    {
        startPage();
        List<SxscCommodityOrderRefund> list = sxscCommodityOrderRefundService.selectSxscCommodityOrderRefundList(sxscCommodityOrderRefund);
        return getDataTable(list);
    }

    /**
     * 获取订单退款详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:refund:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscCommodityOrderRefundService.selectSxscCommodityOrderRefundById(id));
    }

    /**
     * 管理后台发起退款（仅退款）
     */
    @PreAuthorize("@ss.hasPermi('commodity:refund:manageRefund')")
    @Log(title = "管理后台订单退款-仅退款", businessType = BusinessType.UPDATE)
    @PutMapping("/manage/{orderId}")
    @Transactional
    public synchronized AjaxResult manageRefund(@PathVariable String orderId)
    {
        SxscCommodityOrder order=iSxscCommodityOrderService.getById(orderId);
        if(StringUtils.isNull(order)&&(order.getStatus()==1||order.getStatus()==7||order.getStatus()==6)){
            return AjaxResult.error("订单有误，无法退款");
        }
        //执行退款
        String msg=sxscCommodityOrderRefundService.insertSxscCommodityOrderRefund(order);
        if(msg.equals("fail")){
            return AjaxResult.error("退款失败，请联系管理员");
        }
        LambdaUpdateWrapper<SxscCommodityOrder> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(SxscCommodityOrder::getId,order.getId());
        wrapper.set(SxscCommodityOrder::getStatus,6l);
        wrapper.set(SxscCommodityOrder::getOverDateTime,DateUtils.getNowDate());
        wrapper.set(SxscCommodityOrder::getUpdateTime, DateUtils.getNowDate());
        wrapper.set(SxscCommodityOrder::getUpdateBy, SecurityUtils.getUsername());
        iSxscCommodityOrderService.update(wrapper);
        iSxscCommodityAfterSalesService.updateCommodityOrderAfterSalesStatusByOrderId(order.getId(),6l);
        return AjaxResult.success();
    }
    /**
     * 管理后台发起退款（退款退积分推佣金）
     */
    @PreAuthorize("@ss.hasPermi('commodity:refund:manageRefund')")
    @Log(title = "管理后台订单退款-退款退积分推佣金", businessType = BusinessType.UPDATE)
    @PutMapping("/manage/points/{orderId}")
    @Transactional
    public synchronized AjaxResult managePointsRefund(@PathVariable String orderId)
    {
        SxscCommodityOrder order=iSxscCommodityOrderService.getById(orderId);
        if(StringUtils.isNull(order)&&(order.getStatus()==1||order.getStatus()==7||order.getStatus()==6)){
            return AjaxResult.error("订单有误，无法退款");
        }
        //执行退款
        String msg=sxscCommodityOrderRefundService.insertSxscCommodityOrderRefund(order);
        if(msg.equals("fail")){
            return AjaxResult.error("退款失败，请联系管理员");
        }
        //执行佣金退款
        iSxscUserCommissionService.refundSxscUserCommission(orderId);
        //执行账单退款
        iSxscBillIntegralService.refundSxscBillIntegral(orderId);
        //执行利润退款
        iSxscBillTrustFundService.refundSxscBillTrustFund(orderId);

        LambdaUpdateWrapper<SxscCommodityOrder> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(SxscCommodityOrder::getId,order.getId());
        wrapper.set(SxscCommodityOrder::getStatus,6l);
        wrapper.set(SxscCommodityOrder::getOverDateTime,DateUtils.getNowDate());
        wrapper.set(SxscCommodityOrder::getUpdateTime, DateUtils.getNowDate());
        wrapper.set(SxscCommodityOrder::getUpdateBy, SecurityUtils.getUsername());
        iSxscCommodityOrderService.update(wrapper);
        iSxscCommodityAfterSalesService.updateCommodityOrderAfterSalesStatusByOrderId(order.getId(),6l);
        return AjaxResult.success();
    }


    /**
     * 重新发起退款
     */
    @PreAuthorize("@ss.hasPermi('commodity:refund:reissueRefund')")
    @Log(title = "订单退款", businessType = BusinessType.UPDATE)
    @PutMapping("/{orderId}")
    public AjaxResult edit(@PathVariable String orderId)
    {
        return sxscCommodityOrderRefundService.reissueRefund(orderId);
    }




}

package com.ruoyi.sxsc.commodity.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityDelivery;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityPlate;
import com.ruoyi.sxsc.commodity.model.SxscCommodityOrderExcelModel;
import com.ruoyi.sxsc.commodity.model.SxscCommodityOrderModel;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityDeliveryService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityPlateService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * 商品订单信息
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@RestController
@RequestMapping("/commodity/order")
public class SxscCommodityOrderController extends BaseController
{
    @Autowired
    private ISxscCommodityOrderService sxscCommodityOrderService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private ISxscCommodityService iSxscCommodityService;

    @Autowired
    private ISxscCommodityDeliveryService iSxscCommodityDeliveryService;

    @Autowired
    private ISxscCommodityPlateService iSxscCommodityPlateService;


    /**
     * 查询商品订单信息列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodityOrder sxscCommodityOrder)
    {
        startPage();
        List<SxscCommodityOrder> list = sxscCommodityOrderService.selectSxscCommodityOrderList(sxscCommodityOrder);
        return getDataTable(list);
    }



    /**
     * 导出商品订单信息列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:order:export')")
    @Log(title = "商品订单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscCommodityOrder sxscCommodityOrder)
    {

        List<SxscCommodityOrder> list = sxscCommodityOrderService.selectSxscCommodityOrderList(sxscCommodityOrder);
        List<SxscCommodityOrderExcelModel> excelModels=new ArrayList<>();
        for(SxscCommodityOrder commodityOrder:list){
            SxscCommodityDelivery sxscCommodityDelivery=iSxscCommodityDeliveryService.selectSxscCommodityDeliveryByOrderId(commodityOrder.getId(),1l);
            excelModels.add(new SxscCommodityOrderExcelModel(sxscCommodityOrderService.selectSxscCommodityOrderById(commodityOrder.getId()),sxscCommodityDelivery));
        }
        ExcelUtil<SxscCommodityOrderExcelModel> util = new ExcelUtil<>(SxscCommodityOrderExcelModel.class);
        util.exportExcel(response, excelModels, "商品订单信息数据");
    }


    /**
     * 获取商品订单信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscCommodityOrderService.selectSxscCommodityOrderById(id));
    }

    /**
     * 新增商品订单信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:order:add')")
    @Log(title = "商品订单信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodityOrderModel sxscCommodityOrder)
    {
        String orderId=sxscCommodityOrderService.insertSxscCommodityOrder(sxscCommodityOrder);
        return AjaxResult.success("操作成功",orderId);
    }

    /**
     * 单个商品订单支付
     */
    @Log(title = "商品订单信息", businessType = BusinessType.UPDATE)
    @PutMapping("/payment/{type}/{id}")
    public AjaxResult payment(@PathVariable("type") Long type,@PathVariable("id") String id)
    {
        return sxscCommodityOrderService.updateSxscCommodityOrderPayment(id,type);
    }

    /**
     * 商品订单支付类型
     */
    @GetMapping("/paymentType")
    public AjaxResult paymentType(@RequestParam("commodityOrderId") String commodityOrderId)
    {
        Map<String,Object> map=new HashMap<>();

        map.put("message","");
        map.put("payType",dictTypeService.selectDictDataByType("commodity_pay_type"));

        SxscCommodityOrder sxscCommodityOrder=sxscCommodityOrderService.getById(commodityOrderId);
        if(StringUtils.isNull(sxscCommodityOrder)){
            return AjaxResult.success(map);
        }
        SxscCommodity commodity=iSxscCommodityService.getById(sxscCommodityOrder.getCommodityId());
        if(StringUtils.isNull(commodity)){
            return AjaxResult.success(map);
        }
        SxscCommodityPlate commodityPlate=iSxscCommodityPlateService.getById(commodity.getPlateId());

        if(StringUtils.isNull(commodityPlate)||commodityPlate.getParentId()!=1){
            return AjaxResult.success(map);
        }
        map.put("message","注：此商品需要质押优惠券");
        return AjaxResult.success(map);
    }

    /**
     * 订单确认收货
     */
    @PreAuthorize("@ss.hasPermi('commodity:order:edit')")
    @Log(title = "商品订单信息确认收货", businessType = BusinessType.UPDATE)
    @PutMapping("/receiving/{id}")
    public AjaxResult receiving(@PathVariable("id") String id)
    {
        return sxscCommodityOrderService.updateSxscCommodityOrderStatus(id,4L);
    }
    /**
     * 订单确认完成
     */
    @PreAuthorize("@ss.hasPermi('commodity:order:edit')")
    @Log(title = "商品订单信息确认完成", businessType = BusinessType.UPDATE)
    @PutMapping("/complete/{id}")
    public AjaxResult complete(@PathVariable("id") String id)
    {
        return sxscCommodityOrderService.updateSxscCommodityOrderStatus(id,5L);
    }
    /**
     * 订单确认取消
     */
    @PreAuthorize("@ss.hasPermi('commodity:order:edit')")
    @Log(title = "商品订单信息确认取消", businessType = BusinessType.UPDATE)
    @PutMapping("/cancellation/{id}")
    public AjaxResult cancellation(@PathVariable("id") String id)
    {
        return sxscCommodityOrderService.updateSxscCommodityOrderStatus(id,7L);
    }

    /**
     * 商家查询订单数量合集
     */
    @GetMapping("/orderCount/sellerUser")
    public AjaxResult sellerUserCount(@RequestParam(value = "userId",required = false) Long userId)
    {
        return sxscCommodityOrderService.sellerUserCount(userId);
    }

    /**
     * 供应链商品订单信息重新发起支付
     */
    @PreAuthorize("@ss.hasPermi('commodity:order:dingdongRestartPayment')")
    @Log(title = "商品订单信息", businessType = BusinessType.INSERT)
    @PostMapping("/dingdongRestartPayment/{orderId}")
    public AjaxResult dingdongRestartPayment(@PathVariable("orderId") String orderId)
    {
        return sxscCommodityOrderService.dingdongRestartPayment(orderId);
    }

    /**
     * 开通优选购
     */
    @PreAuthorize("@ss.hasPermi('commodity:order:optimalSelection')")
    @Log(title = "商品订单信息", businessType = BusinessType.INSERT)
    @PostMapping("/optimalSelection/{userId}")
    public AjaxResult dingdongRestartPayment(@PathVariable("userId") Long userId)
    {
        return sxscCommodityOrderService.optimalSelection(userId);
    }
}

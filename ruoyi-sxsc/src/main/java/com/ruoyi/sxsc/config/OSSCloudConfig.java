package com.ruoyi.sxsc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "ali.oss")
public class OSSCloudConfig {

    public  String endpoint_inter ; //ECS 的经典网络访问（内网）

    public  String endpoint ; //外网访问

    public  String accessKeyId ;

    public  String accessKeySecret ;

    public  String bucketName ;

    public String objectPrefixName ; //基础文件夹



}

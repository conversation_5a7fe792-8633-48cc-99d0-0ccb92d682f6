package com.ruoyi.sxsc.person.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 佣金提现对象
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserCommissionOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 提现金额 */
    @Excel(name = "提现金额")
    private BigDecimal amount;

    /** 审核人主键 */
    @Excel(name = "审核人主键")
    private Long examineUserId;

    /** 审核状态1通过0拒绝 */
    @Excel(name = "审核状态1通过0拒绝")
    private Long examineStatus;

    /** 状态0待处理1到账成功2到账失败3购物4发布预购单5拒绝*/
    @Excel(name = "状态0待处理1到账成功2到账失败3购物4发布预购单5拒绝")
    private Long status;

    /** 到账时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到账时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentTime;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private Long paymentType;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Long delFlag;

    /** PT地址 */
    @Excel(name = "PT地址")
    private String ptAddress;

    /** 查询日期 */
    @TableField(exist = false)
    private String createDate;

    @TableField(exist = false)
    private SysUserMain sysUser;

}

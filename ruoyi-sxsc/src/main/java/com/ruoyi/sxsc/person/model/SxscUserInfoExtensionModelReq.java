package com.ruoyi.sxsc.person.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 人员基本信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserInfoExtensionModelReq
{

    /** 手机号 */
    private String phonenumber;

    /** 团队(注册时间)排序 */
    private Long createTimeReorder;

    /** 金额排序 */
    private Long amountReorder;

    /** 订单总数排序 */
    private Long orderReorder;

    /** 开始时间 */
    private String startDate;

    /** 结束时间 */
    private String endDate;

    /** 用户主键 */
    private Long userId;

    /** 商品名称 */
    private String commodityName;

}

package com.ruoyi.sxsc.person.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.person.domain.SxscUserBank;
import com.ruoyi.sxsc.person.service.ISxscUserBankService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 银行卡信息
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/person/bank")
public class SxscUserBankController extends BaseController
{
    @Autowired
    private ISxscUserBankService sxscUserBankService;

    /**
     * 查询银行卡信息列表
     */
    @PreAuthorize("@ss.hasPermi('person:bank:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserBank sxscUserBank)
    {
        startPage();
        List<SxscUserBank> list = sxscUserBankService.selectSxscUserBankList(sxscUserBank);
        return getDataTable(list);
    }


    /**
     * 获取银行卡信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:bank:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserBankService.selectSxscUserBankById(id));
    }

    /**
     * 新增银行卡信息
     */
    @PreAuthorize("@ss.hasPermi('person:bank:add')")
    @Log(title = "银行卡信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserBank sxscUserBank)
    {
        return sxscUserBankService.insertSxscUserBank(sxscUserBank);
    }

    /**
     * 修改银行卡信息
     */
    @PreAuthorize("@ss.hasPermi('person:bank:edit')")
    @Log(title = "银行卡信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscUserBank sxscUserBank)
    {
        return sxscUserBankService.updateSxscUserBank(sxscUserBank);
    }

    /**
     * 删除银行卡信息
     */
    @PreAuthorize("@ss.hasPermi('person:bank:remove')")
    @Log(title = "银行卡信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscUserBankService.deleteSxscUserBankByIds(ids));
    }
}

package com.ruoyi.sxsc.person.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.person.domain.SxscUserCommissionOrder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 佣金提现Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface SxscUserCommissionOrderMapper extends BaseMapper<SxscUserCommissionOrder>
{


    /**
     * 查询提现佣金总额（包含已经提现到账和申请中和购物）
     * @param userId 用户主键
     * @param createMonth 提现月份
     * @return  佣金总额
     */
    @Select("<script>"+
            "select IFNULL(sum(amount), 0)   from sxsc_user_commission_order " +
            "where   (status=1 or status=0 or status=3 or status=4) " +
            "<if test='userId != null '> and  user_id=#{userId} </if>"+
            "<if test='createMonth != null '> and  DATE_FORMAT(create_time, '%Y-%m')=#{createMonth} </if>"+
            "</script>")
    BigDecimal commissionOrderSum(@Param("userId")Long userId,@Param("createMonth") String createMonth);


    /**
     * 查询提现佣金总额（包含已经提现到账和申请中）
     * @param userId 用户主键
     * @param createMonth 提现月份
     * @return  佣金总额
     */
    @Select("<script>"+
            "select IFNULL(sum(amount), 0)   from sxsc_user_commission_order " +
            "where   (status=1 or status=0 ) " +
            "<if test='userId != null '> and  user_id=#{userId} </if>"+
            "<if test='createMonth != null '> and  DATE_FORMAT(create_time, '%Y-%m')=#{createMonth} </if>"+
            "</script>")
    BigDecimal commissionOrderSumAmount(@Param("userId")Long userId,@Param("createMonth") String createMonth);



}

package com.ruoyi.sxsc.person.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.model.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 人员基本信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface ISxscUserInfoService extends IService<SxscUserInfo>
{
    /**
     * 查询人员基本信息
     * 
     * @param userId 人员基本信息主键
     * @return 人员基本信息
     */
    SxscUserInfo selectSxscUserInfoByUserId(Long userId);

    /**
     * 查询人员基本信息列表
     * 
     * @param sxscUserInfo 人员基本信息
     * @return 人员基本信息集合
     */
    List<SxscUserInfo> selectSxscUserInfoList(SxscUserInfoModel sxscUserInfo);

    /**
     * 新增人员基本信息
     * 
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    int insertSxscUserInfo(SxscUserInfo sxscUserInfo);

    /**
     * 修改人员基本信息
     * 
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    AjaxResult updateSxscUserInfo(SxscUserInfo sxscUserInfo);


    /**
     * 修改人员基本信息
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    AjaxResult updateSxscUserInfo(SxscUserInfoModel sxscUserInfo);

    /**
     * 修改人员节点信息
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    AjaxResult updateSxscUserInfoNodeId(SxscUserInfoModel sxscUserInfo);
    /**
     * 修改人员基本信息
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    AjaxResult updateSxscUserInfoTicketAcc(SxscUserInfoModel sxscUserInfo);

    /**
     * 修改人员基本信息
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    AjaxResult updateSxscUserInfoPtAddress(SxscUserInfoModel sxscUserInfo);

    /**
     * 修改人员积分
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    void updateSxscUserInfoIntegral(SxscUserInfo sxscUserInfo);

    /**
     * 换绑当前账号
     *
     * @param changeBindingModel
     * @return 结果
     */
    AjaxResult changeBinding(SxscUserInfoChangeBindingModel changeBindingModel);

    /**
     * 批量删除人员基本信息
     *
     * @param userIds 需要删除的人员基本信息主键集合
     * @return 结果
     */
    AjaxResult deleteSxscUserInfoByUserIds(Long[] userIds);


    /**
     * 查询当前登陆人推广人订单
     *
     * @param sxscUserInfo 筛选条件
     * @return 结果
     */
    List<SxscUserInfoExtensionOrderModelRes> extensionOrderList(SxscUserInfoExtensionModelReq sxscUserInfo);


    /**
     * 导入用户数据
     *
     * @param infoModelExcels 数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importInfo(List<SxscUserInfoModelExcel> infoModelExcels, Boolean isUpdateSupport, String operName);

}

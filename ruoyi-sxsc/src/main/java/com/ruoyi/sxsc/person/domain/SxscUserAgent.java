package com.ruoyi.sxsc.person.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 代理信息对象
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserAgent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 人员id */
    @Excel(name = "人员id")
    private Long userId;

    /** 全国代理1是 */
    @Excel(name = "全国代理")
    private Long national;

    /** 省 */
    @Excel(name = "省")
    private String province;

    /** 省编码 */
    @Excel(name = "省编码")
    private String provinceCode;

    /** 市 */
    @Excel(name = "市")
    private String city;

    /** 市编码 */
    @Excel(name = "市编码")
    private String cityCode;

    /** 县 */
    @Excel(name = "县")
    private String county;

    /** 县编码 */
    @Excel(name = "县编码")
    private String countyCode;

    /** 等级 */
    @Excel(name = "等级")
    private Long grade;

    /** 状态1有效0无效 */
    @Excel(name = "状态1有效0无效")
    private Long status;

    /** 分配比例 */
    @Excel(name = "分配比例")
    private BigDecimal proportion;

    @TableField(exist = false)
    private SysUserMain sysUser;



}

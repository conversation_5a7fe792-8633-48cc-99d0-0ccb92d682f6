package com.ruoyi.sxsc.person.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.person.domain.SxscUserAddress;
import com.ruoyi.sxsc.person.mapper.SxscAddressMapper;
import com.ruoyi.sxsc.person.service.ISxscAddressService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 地址信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Service
public class SxscAddressServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscAddressMapper, SxscUserAddress> implements ISxscAddressService
{

    /**
     * 查询地址信息
     * 
     * @param id 地址信息主键
     * @return 地址信息
     */
    @Override
    public SxscUserAddress selectSxscAddressById(Long id)
    {
        return getById(id);
    }


    /**
     * 查询地址信息
     *
     * @param userId 用户主键
     * @param status 地址信息状态
     * @return 地址信息
     */
    @Override
    public SxscUserAddress selectSxscAddressByUserId(Long userId, Long status){
        LambdaQueryWrapper<SxscUserAddress> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscUserAddress::getUserId,userId);
        queryWrapper.eq(SxscUserAddress::getStatus,status);
        return getOne(queryWrapper);
    }

    /**
     * 查询地址信息列表
     * 
     * @param sxscUserAddress 地址信息
     * @return 地址信息
     */
    @Override
    public List<SxscUserAddress> selectSxscAddressList(SxscUserAddress sxscUserAddress)
    {
        LambdaQueryWrapper<SxscUserAddress> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserAddress::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.eq(StringUtils.isNotNull(sxscUserAddress.getUserId()), SxscUserAddress::getUserId, sxscUserAddress.getUserId());
        }

        wrapper.like(StringUtils.isNotNull(sxscUserAddress.getName()), SxscUserAddress::getName, sxscUserAddress.getName());

        wrapper.eq(StringUtils.isNotNull(sxscUserAddress.getSex()), SxscUserAddress::getSex, sxscUserAddress.getSex());

        wrapper.like(StringUtils.isNotNull(sxscUserAddress.getPhone()), SxscUserAddress::getPhone, sxscUserAddress.getPhone());

        wrapper.eq(StringUtils.isNotNull(sxscUserAddress.getStatus()), SxscUserAddress::getStatus, sxscUserAddress.getStatus());

        wrapper.like(StringUtils.isNotNull(sxscUserAddress.getProvince()), SxscUserAddress::getProvince, sxscUserAddress.getProvince());

        wrapper.like(StringUtils.isNotNull(sxscUserAddress.getCity()), SxscUserAddress::getCity, sxscUserAddress.getCity());

        wrapper.like(StringUtils.isNotNull(sxscUserAddress.getCounty()), SxscUserAddress::getCounty, sxscUserAddress.getCounty());

        wrapper.eq(SxscUserAddress::getDelFlag,0l);

        wrapper.orderByDesc(SxscUserAddress::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增地址信息
     * 
     * @param sxscUserAddress 地址信息
     * @return 结果
     */
    @Override
    public int insertSxscAddress(SxscUserAddress sxscUserAddress)
    {
        if(sxscUserAddress.getStatus()!=2){
            updateStatus(SecurityUtils.getUserId(), sxscUserAddress.getStatus(),2l);
        }
        sxscUserAddress.setUserId(SecurityUtils.getUserId());
        sxscUserAddress.setDelFlag(0l);
        sxscUserAddress.setCreateBy(SecurityUtils.getUsername());
        sxscUserAddress.setCreateTime(DateUtils.getNowDate());
        return save(sxscUserAddress)?1:0;
    }

    /**
     * 修改地址信息
     * 
     * @param sxscUserAddress 地址信息
     * @return 结果
     */
    @Override
    public int updateSxscAddress(SxscUserAddress sxscUserAddress)
    {
        if(sxscUserAddress.getStatus()!=2){
            updateStatus(SecurityUtils.getUserId(), sxscUserAddress.getStatus(),2l);
        }
        //原来的地址删除，保留快照
        deleteSxscAddressById(sxscUserAddress.getId());

        sxscUserAddress.setId(null);
        sxscUserAddress.setUserId(SecurityUtils.getUserId());
        sxscUserAddress.setDelFlag(0l);
        sxscUserAddress.setCreateBy(SecurityUtils.getUsername());
        sxscUserAddress.setCreateTime(DateUtils.getNowDate());
        return save(sxscUserAddress)?1:0;
    }


    /**
     * 删除地址信息信息
     * 
     * @param id 地址信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscAddressById(Long id)
    {
        LambdaUpdateWrapper<SxscUserAddress> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserAddress::getDelFlag,1);
        updateWrapper.set(SxscUserAddress::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscUserAddress::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.eq(SxscUserAddress::getId,id);
        return update(updateWrapper)?1:0;
    }

    private void updateStatus(Long userId,Long status,Long newStatus){
        LambdaUpdateWrapper<SxscUserAddress> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserAddress::getStatus,newStatus);
        updateWrapper.eq(SxscUserAddress::getStatus,status);
        updateWrapper.eq(SxscUserAddress::getUserId,userId);
        update(updateWrapper);
    }
}

package com.ruoyi.sxsc.person.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.person.domain.SxscUserCommissionOrder;

import java.math.BigDecimal;
import java.util.List;

/**
 * 佣金提现Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ISxscUserCommissionOrderService extends IService<SxscUserCommissionOrder>
{
    /**
     * 查询佣金提现
     * 
     * @param id 佣金提现主键
     * @return 佣金提现
     */
    SxscUserCommissionOrder selectSxscUserCommissionOrderById(String id);

    /**
     * 查询佣金提现总额（包含已经提现到账和申请中和购物）
     *
     * @param userId 用户主键
     * @return 佣金提现
     */
    BigDecimal commissionOrderSum(Long userId);

    /**
     * 查询佣金提现列表
     * 
     * @param sxscUserCommissionOrder 佣金提现
     * @return 佣金提现集合
     */
    List<SxscUserCommissionOrder> selectSxscUserCommissionOrderList(SxscUserCommissionOrder sxscUserCommissionOrder);

    /**
     * 新增佣金提现
     * 
     * @param sxscUserCommissionOrder 佣金提现
     * @return 结果
     */
    AjaxResult insertSxscUserCommissionOrder(SxscUserCommissionOrder sxscUserCommissionOrder);

    /**
     * 新增佣金发布预购单
     *
     * @param totalAmount 佣金总额
     * @param orderId 主键
     * @return 结果
     */
    void insertSxscUserCommissionOrder(BigDecimal totalAmount, String orderId);

    /**
     * 重新发起佣金提现到账更改为成功状态
     *
     * @param orderId 主键
     * @return 结果
     */
    void updateSxscUserCommissionOrder(String orderId);

    /**
     * 修改佣金提现
     *
     * @param sxscUserCommissionOrder 佣金提现
     * @return 结果
     */
    AjaxResult updateSxscUserCommissionOrder(SxscUserCommissionOrder sxscUserCommissionOrder);

}

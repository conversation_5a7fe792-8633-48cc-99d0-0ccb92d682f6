package com.ruoyi.sxsc.person.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.person.domain.SxscUserBank;

import java.util.List;

/**
 * 银行卡信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface ISxscUserBankService extends IService<SxscUserBank>
{
    /**
     * 查询银行卡信息
     * 
     * @param id 银行卡信息主键
     * @return 银行卡信息
     */
    SxscUserBank selectSxscUserBankById(Long id);

    /**
     * 查询银行卡信息列表
     * 
     * @param sxscUserBank 银行卡信息
     * @return 银行卡信息集合
     */
    List<SxscUserBank> selectSxscUserBankList(SxscUserBank sxscUserBank);

    /**
     * 新增银行卡信息
     * 
     * @param sxscUserBank 银行卡信息
     * @return 结果
     */
    AjaxResult insertSxscUserBank(SxscUserBank sxscUserBank);

    /**
     * 修改银行卡信息
     * 
     * @param sxscUserBank 银行卡信息
     * @return 结果
     */
    AjaxResult updateSxscUserBank(SxscUserBank sxscUserBank);

    /**
     * 批量删除银行卡信息
     * 
     * @param ids 需要删除的银行卡信息主键集合
     * @return 结果
     */
    int deleteSxscUserBankByIds(Long[] ids);

    /**
     * 删除银行卡信息信息
     * 
     * @param id 银行卡信息主键
     * @return 结果
     */
    int deleteSxscUserBankById(Long id);
}

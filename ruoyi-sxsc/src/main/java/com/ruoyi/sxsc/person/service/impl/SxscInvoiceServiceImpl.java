package com.ruoyi.sxsc.person.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.person.domain.SxscUserInvoice;
import com.ruoyi.sxsc.person.mapper.SxscInvoiceMapper;
import com.ruoyi.sxsc.person.service.ISxscInvoiceService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 发票信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
public class SxscInvoiceServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscInvoiceMapper, SxscUserInvoice> implements ISxscInvoiceService
{

    @Autowired
    ISysUserService iSysUserService;

    /**
     * 查询发票信息
     * 
     * @param id 发票信息主键
     * @return 发票信息
     */
    @Override
    public SxscUserInvoice selectSxscInvoiceById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询发票信息列表
     * 
     * @param sxscUserInvoice 发票信息
     * @return 发票信息
     */
    @Override
    public List<SxscUserInvoice> selectSxscInvoiceList(SxscUserInvoice sxscUserInvoice)
    {
        LambdaQueryWrapper<SxscUserInvoice> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserInvoice::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.eq(StringUtils.isNotNull(sxscUserInvoice.getUserId()), SxscUserInvoice::getUserId, sxscUserInvoice.getUserId());
        }
        wrapper.like(StringUtils.isNotNull(sxscUserInvoice.getInvoiceHeader()), SxscUserInvoice::getInvoiceHeader, sxscUserInvoice.getInvoiceHeader());

        wrapper.like(StringUtils.isNotNull(sxscUserInvoice.getTaxId()), SxscUserInvoice::getTaxId, sxscUserInvoice.getTaxId());

        wrapper.like(StringUtils.isNotNull(sxscUserInvoice.getRegisterAddress()), SxscUserInvoice::getRegisterAddress, sxscUserInvoice.getRegisterAddress());

        wrapper.like(StringUtils.isNotNull(sxscUserInvoice.getRegisterPhone()), SxscUserInvoice::getRegisterPhone, sxscUserInvoice.getRegisterPhone());

        wrapper.like(StringUtils.isNotNull(sxscUserInvoice.getOpeningBank()), SxscUserInvoice::getOpeningBank, sxscUserInvoice.getOpeningBank());

        wrapper.like(StringUtils.isNotNull(sxscUserInvoice.getBankNumber()), SxscUserInvoice::getBankNumber, sxscUserInvoice.getBankNumber());

        wrapper.eq(StringUtils.isNotNull(sxscUserInvoice.getType()), SxscUserInvoice::getType, sxscUserInvoice.getType());

        wrapper.eq(SxscUserInvoice::getDelFlag,0l);

        wrapper.orderByDesc(SxscUserInvoice::getCreateTime);

        List<SxscUserInvoice> list=list(wrapper);

        for(SxscUserInvoice invoice:list){
            invoice.setSysUser(iSysUserService.selectUserMainById(invoice.getUserId()));
        }

        return list;
    }

    /**
     * 新增发票信息
     * 
     * @param sxscUserInvoice 发票信息
     * @return 结果
     */
    @Override
    public int insertSxscInvoice(SxscUserInvoice sxscUserInvoice)
    {
        sxscUserInvoice.setDelFlag(0l);
        sxscUserInvoice.setUserId(SecurityUtils.getUserId());
        sxscUserInvoice.setCreateBy(SecurityUtils.getUsername());
        sxscUserInvoice.setCreateTime(DateUtils.getNowDate());
        return save(sxscUserInvoice)?1:0;
    }

    /**
     * 修改发票信息
     * 
     * @param sxscUserInvoice 发票信息
     * @return 结果
     */
    @Override
    public int updateSxscInvoice(SxscUserInvoice sxscUserInvoice)
    {
        sxscUserInvoice.setUserId(SecurityUtils.getUserId());
        sxscUserInvoice.setUpdateBy(SecurityUtils.getUsername());
        sxscUserInvoice.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscUserInvoice)?1:0;
    }

    /**
     * 批量删除发票信息
     * 
     * @param ids 需要删除的发票信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscInvoiceByIds(Long[] ids)
    {
        LambdaUpdateWrapper<SxscUserInvoice> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserInvoice::getDelFlag,1);
        updateWrapper.set(SxscUserInvoice::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscUserInvoice::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.in(SxscUserInvoice::getId,ids);
        return update(updateWrapper)?1:0;
    }


}

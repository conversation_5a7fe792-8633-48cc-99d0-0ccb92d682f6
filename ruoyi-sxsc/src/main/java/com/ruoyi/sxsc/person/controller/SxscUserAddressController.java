package com.ruoyi.sxsc.person.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.person.domain.SxscUserAddress;
import com.ruoyi.sxsc.person.service.ISxscAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地址信息
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@RestController
@RequestMapping("/person/address")
public class SxscUserAddressController extends BaseController
{
    @Autowired
    private ISxscAddressService sxscAddressService;

    /**
     * 查询地址信息列表
     */
    @PreAuthorize("@ss.hasPermi('person:address:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserAddress sxscUserAddress)
    {
        startPage();
        List<SxscUserAddress> list = sxscAddressService.selectSxscAddressList(sxscUserAddress);
        return getDataTable(list);
    }


    /**
     * 获取地址信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:address:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscAddressService.selectSxscAddressById(id));
    }

    /**
     * 新增地址信息
     */
    @PreAuthorize("@ss.hasPermi('person:address:add')")
    @Log(title = "地址信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserAddress sxscUserAddress)
    {
        return toAjax(sxscAddressService.insertSxscAddress(sxscUserAddress));
    }

    /**
     * 修改地址信息
     */
    @PreAuthorize("@ss.hasPermi('person:address:edit')")
    @Log(title = "地址信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscUserAddress sxscUserAddress)
    {
        return toAjax(sxscAddressService.updateSxscAddress(sxscUserAddress));
    }

    /**
     * 删除地址信息
     */
    @PreAuthorize("@ss.hasPermi('person:address:remove')")
    @Log(title = "地址信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(sxscAddressService.deleteSxscAddressById(id));
    }
}

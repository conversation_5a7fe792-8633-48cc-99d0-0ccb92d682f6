package com.ruoyi.sxsc.person.model;

import com.ruoyi.common.core.domain.entity.SysUserMain;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 人员基本信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserInfoExtensionOrderModelRes
{

    /** 是否有效用户 是否返佣 */
    private Long toCommission;

    /** 佣金 */
    private BigDecimal commission;

    /** 用户基本信息 */
    private SysUserMain sysUser;

    /** 订单信息 */
    private SxscCommodityOrder order;



}

package com.ruoyi.sxsc.person.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 地址信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserAddress extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 人员ID */
    @Excel(name = "人员ID")
    private Long userId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 性别0先生1女士 */
    @Excel(name = "性别0先生1女士")
    private Long sex;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 状态(1使用中2未使用3商家默认地址) */
    @Excel(name = "状态(1使用中2未使用3商家默认地址)")
    private Long status;

    /** 标签，多个字符串隔开 */
    @Excel(name = "标签，多个字符串隔开")
    private String label;

    /** 省 */
    @Excel(name = "省")
    private String province;

    /** 省编码 */
    private String provinceCode;

    /** 市 */
    @Excel(name = "市")
    private String city;

    /** 市编码 */
    private String cityCode;

    /** 县 */
    @Excel(name = "县")
    private String county;

    /** 县编码 */
    private String countyCode;

    /** 0正常1删除 */
    private Long delFlag;




}

package com.ruoyi.sxsc.person.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.person.domain.SxscUserAgent;
import com.ruoyi.sxsc.person.service.ISxscUserAgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代理信息
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@RestController
@RequestMapping("/person/agent")
public class SxscUserAgentController extends BaseController
{
    @Autowired
    private ISxscUserAgentService sxscUserAgentService;

    /**
     * 查询代理信息列表
     */
    @PreAuthorize("@ss.hasPermi('person:agent:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserAgent sxscUserAgent)
    {
        startPage();
        List<SxscUserAgent> list = sxscUserAgentService.selectSxscUserAgentList(sxscUserAgent);
        return getDataTable(list);
    }

    /**
     * 获取代理信息统计信息
     */
    @PreAuthorize("@ss.hasPermi('person:agent:query')")
    @GetMapping(value = "/statistics")
    public AjaxResult statistics(SxscUserAgent sxscUserAgent)
    {
        return success(sxscUserAgentService.statistics(sxscUserAgent));
    }

    /**
     * 获取代理信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:agent:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserAgentService.selectSxscUserAgentById(id));
    }

    /**
     * 新增代理信息
     */
    @PreAuthorize("@ss.hasPermi('person:agent:add')")
    @Log(title = "代理信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserAgent sxscUserAgent)
    {
        return sxscUserAgentService.insertSxscUserAgent(sxscUserAgent);
    }

    /**
     * 修改代理信息
     */
    @PreAuthorize("@ss.hasPermi('person:agent:edit')")
    @Log(title = "代理信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscUserAgent sxscUserAgent)
    {
        return toAjax(sxscUserAgentService.updateSxscUserAgent(sxscUserAgent));
    }


    /**
     * 获取省市级数据
     */
    @GetMapping(value="/district")
    public AjaxResult district() {

        return sxscUserAgentService.district();
    }
}

package com.ruoyi.sxsc.person.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.model.SxscUserInfoExtensionModelReq;
import com.ruoyi.sxsc.person.model.SxscUserInfoExtensionModelRes;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 人员基本信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface SxscUserInfoMapper extends BaseMapper<SxscUserInfo>
{

    /**
     * 查询当前积分总数
     * @return
     */
    @Select("select IFNULL(sum(integral_ty), 0) from sxsc_user_info  where del_flag=0")
    BigDecimal integralTySum();

    /**
     * 查询当前贡献值总数
     * @return
     */
    @Select("select IFNULL(sum(integral_gxz), 0) from sxsc_user_info  where del_flag=0")
    BigDecimal integralGxzSum();

    /**
     * 查询当前数字权证总数
     * @return
     */
    @Select("select IFNULL(sum(integral_szqz), 0) from sxsc_user_info  where del_flag=0")
    BigDecimal integralSzqzSum();

    /**
     * 查询当前总集团股
     * @return
     */
    @Select("select IFNULL(sum(share), 0) from sxsc_user_info  where del_flag=0")
    BigDecimal integralShareSum();

    /**
     * 查询当前总人数
     * @return
     */
    @Select("select count(1) from sxsc_user_info  where del_flag=0")
    BigDecimal peopleCount();

    /**
     * 查询代理区域总人数
     * @return
     */
    @Select("<script>"+
            "select count(1) from sxsc_user_info  " +
            "where del_flag=0 " +
            "and  #{createTime}  <![CDATA[ >= ]]>DATE_FORMAT(create_time,'%Y-%m-%d') "+
            "and  user_id in (select user_id from sxsc_user_address where del_flag=0  and status=1 " +
                    "<if test=' province != null and province != \"\" '> and  province=#{province} </if>" +
                    "<if test=' city != null and city != \"\" '> and  city=#{city} </if>" +
                    "<if test=' county != null and county != \"\" '> and  county=#{county} </if>" +
            "     )"+
            "</script>")
    Long agentPeopleCount(@Param("createTime") String createTime,@Param("province") String province,
                                @Param("city") String city,@Param("county") String county);

    @Select("<script>"+
            "select * from (SELECT " +
            " c.nick_name as nickName, " +
            " c.phonenumber, " +
            " c.avatar, " +
            " c.user_id as userId, " +
            " a.effective AS effective, " +
            " a.create_time AS createTime, " +
            " IF( a.identity_time IS NULL, 0, 1 ) AS realName, " +
            " (select IFNULL(sum( b.actual_payment ),0) from sxsc_commodity_order as b where a.user_id = b.buyer_user_id AND b.STATUS =5) AS amountSum, " +
            " (select count( b.actual_payment ) from sxsc_commodity_order as b where a.user_id = b.buyer_user_id AND b.STATUS =5) AS orderCount  " +
            "FROM " +
            " sxsc_user_info AS a,sys_user as c where a.del_flag=0 and a.user_id=c.user_id and a.parent_id=#{userId}"+
            "<if test='startDate != null and startDate != \"\"  '>  " +
            "and #{startDate}   <![CDATA[ <= ]]>DATE_FORMAT(a.create_time,'%Y-%m-%d') </if>"+

            "<if test=' endDate != null and endDate != \"\" '>  " +
            "and DATE_FORMAT(a.create_time,'%Y-%m-%d')<![CDATA[ <= ]]> #{endDate} </if>"+

            "<if test='phonenumber != null and phonenumber != \"\" '>  and c.phonenumber like concat('%', #{phonenumber}, '%') </if>"+
            ") as t  order by 1 "+
            "<if test='createTimeReorder==0 '>   ,t.createTime asc </if>"+
            "<if test='createTimeReorder==1 '> ,t.createTime desc </if>"+
            "<if test='orderReorder==0 '> ,t.orderCount asc </if>"+
            "<if test='orderReorder==1 '>  ,t.orderCount desc </if>"+
            "<if test='amountReorder==0 '>  ,t.amountSum asc </if>"+
            "<if test='amountReorder==1 '>  ,t.amountSum desc </if>"+
            "</script>")
    List<SxscUserInfoExtensionModelRes>  extensionUserList(SxscUserInfoExtensionModelReq sxscUserInfo);



    /**
     * 查询节点账号下的总人数
     * @return
     */
    @Select("<script>"+
            "WITH RECURSIVE user_hierarchy AS (" +
            "    SELECT user_id, node_id,copartner, 1 AS level" +
            "    FROM sxsc_user_info" +
            "    WHERE node_id = #{userId} " +
            "    UNION ALL" +
            "    SELECT u.user_id, u.node_id,u.copartner,  uh.level + 1" +
            "    FROM sxsc_user_info u" +
            "    JOIN user_hierarchy uh ON u.node_id = uh.user_id   WHERE uh.level <![CDATA[ < ]]> 1000  " +
            ") " +
            " SELECT * FROM user_hierarchy where 1=1 " +
            "<if test='copartner != null '> and  copartner=#{copartner} </if>"+
            " ORDER BY level, user_id; "+
            "</script>")
    List<SxscUserInfo> nodeAccByUserId(@Param("userId") Long userId, @Param("copartner") Long copartner);


    /**
     * 反向查询账号的节点账号
     * @return
     */
    @Select("<script>"+
            "WITH RECURSIVE user_hierarchy AS (" +
            "    SELECT user_id, node_id,copartner, 1 AS level" +
            "    FROM sxsc_user_info" +
            "    WHERE user_id = #{userId}" +
            "    UNION ALL" +
            "    SELECT u.user_id, u.node_id,u.copartner, uh.level + 1 " +
            "    FROM sxsc_user_info u" +
            "    JOIN user_hierarchy uh ON u.user_id = uh.node_id   WHERE uh.level <![CDATA[ < ]]> 1000 " +
            ")" +
            "SELECT * FROM user_hierarchy where copartner <![CDATA[ > ]]> 0 and user_id != #{userId} " +
            "ORDER BY level"+
            "</script>")
    List<SxscUserInfo> userIdByNodeAcc(@Param("userId") Long userId);




}

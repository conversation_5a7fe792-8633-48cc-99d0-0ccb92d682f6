package com.ruoyi.sxsc.person.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.person.domain.SxscUserInvoice;
import com.ruoyi.sxsc.person.service.ISxscInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发票信息
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@RestController
@RequestMapping("/person/invoice")
public class SxscUserInvoiceController extends BaseController
{
    @Autowired
    private ISxscInvoiceService sxscInvoiceService;

    /**
     * 查询发票信息列表
     */
    @PreAuthorize("@ss.hasPermi('person:invoice:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserInvoice sxscUserInvoice)
    {
        startPage();
        List<SxscUserInvoice> list = sxscInvoiceService.selectSxscInvoiceList(sxscUserInvoice);
        return getDataTable(list);
    }


    /**
     * 获取发票信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:invoice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscInvoiceService.selectSxscInvoiceById(id));
    }

    /**
     * 新增发票信息
     */
    @PreAuthorize("@ss.hasPermi('person:invoice:add')")
    @Log(title = "发票信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserInvoice sxscUserInvoice)
    {
        return toAjax(sxscInvoiceService.insertSxscInvoice(sxscUserInvoice));
    }

    /**
     * 修改发票信息
     */
    @PreAuthorize("@ss.hasPermi('person:invoice:edit')")
    @Log(title = "发票信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscUserInvoice sxscUserInvoice)
    {
        return toAjax(sxscInvoiceService.updateSxscInvoice(sxscUserInvoice));
    }

    /**
     * 删除发票信息
     */
    @PreAuthorize("@ss.hasPermi('person:invoice:remove')")
    @Log(title = "发票信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscInvoiceService.deleteSxscInvoiceByIds(ids));
    }
}

package com.ruoyi.sxsc.person.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.person.domain.SxscUserInvoice;

import java.util.List;

/**
 * 发票信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface ISxscInvoiceService extends IService<SxscUserInvoice>
{
    /**
     * 查询发票信息
     * 
     * @param id 发票信息主键
     * @return 发票信息
     */
    SxscUserInvoice selectSxscInvoiceById(Long id);

    /**
     * 查询发票信息列表
     * 
     * @param sxscUserInvoice 发票信息
     * @return 发票信息集合
     */
    List<SxscUserInvoice> selectSxscInvoiceList(SxscUserInvoice sxscUserInvoice);

    /**
     * 新增发票信息
     * 
     * @param sxscUserInvoice 发票信息
     * @return 结果
     */
    int insertSxscInvoice(SxscUserInvoice sxscUserInvoice);

    /**
     * 修改发票信息
     * 
     * @param sxscUserInvoice 发票信息
     * @return 结果
     */
    int updateSxscInvoice(SxscUserInvoice sxscUserInvoice);

    /**
     * 批量删除发票信息
     * 
     * @param ids 需要删除的发票信息主键集合
     * @return 结果
     */
    int deleteSxscInvoiceByIds(Long[] ids);


}

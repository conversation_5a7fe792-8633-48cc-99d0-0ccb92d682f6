package com.ruoyi.sxsc.person.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 人员基本信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserInfoModelExcel
{

    /** 身份证姓名 */
    @Excel(name = "身份证姓名")
    private String identityName;

    /** 身份证性别 */
    @Excel(name = "身份证性别")
    private String identitySex;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String identityNumber;

    /** 身份证有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "身份证有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date identityDate;

    /** 认证时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "认证时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date identityTime;

    /** 支付宝姓名 */
    @Excel(name = "支付宝姓名")
    private String aliPayName;

    /** 支付宝账号 */
    @Excel(name = "支付宝账号")
    private String aliPayAcc;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 用户手机号 */
    @Excel(name = "用户手机号")
    private String phonenumber;

    /** 用户性别 */
    @Excel(name = "用户性别")
    private String sex;

    /** 用户类型 */
    @Excel(name = "用户类型")
    private String userType;

    /** 邀请码 */
    @Excel(name = "邀请码")
    private String invitationCode;


}

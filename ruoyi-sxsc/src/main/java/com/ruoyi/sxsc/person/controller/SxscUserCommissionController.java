package com.ruoyi.sxsc.person.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.person.domain.SxscUserCommission;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 佣金获取信息
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@RestController
@RequestMapping("/person/commission")
public class SxscUserCommissionController extends BaseController
{
    @Autowired
    private ISxscUserCommissionService sxscUserCommissionService;

    /**
     * 查询佣金信息列表
     */
    @PreAuthorize("@ss.hasPermi('person:commission:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserCommission sxscUserCommission)
    {
        startPage();
        List<SxscUserCommission> list = sxscUserCommissionService.selectSxscUserCommissionList(sxscUserCommission);
        return getDataTable(list);
    }

    /**
     * 获取佣金信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:commission:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserCommissionService.selectSxscUserCommissionById(id));
    }

    /**
     * 新增佣金信息
     */
    @PreAuthorize("@ss.hasPermi('person:commission:add')")
    @Log(title = "新增佣金信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserCommission sxscUserCommission)
    {
        return success(sxscUserCommissionService.insertSxscUserCommission(sxscUserCommission));
    }
    /**
     * 统计佣金信息
     */
    @PreAuthorize("@ss.hasPermi('person:commission:list')")
    @GetMapping("/statistics")
    public AjaxResult statistics(Long userId)
    {
        if(StringUtils.isNull(userId)){
            userId= SecurityUtils.getUserId();
        }
        return AjaxResult.success(sxscUserCommissionService.statistics(userId));
    }


}

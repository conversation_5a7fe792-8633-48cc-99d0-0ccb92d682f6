package com.ruoyi.sxsc.person.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 佣金信息对象
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserCommission extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 商品订单主键 */
    @Excel(name = "商品订单主键")
    private String commodityOrderId;

    /** 佣金名称 */
    @Excel(name = "佣金名称")
    private String name;

    /** 获取佣金用户主键 */
    @Excel(name = "获取佣金用户主键")
    private Long userId;

    /** 佣金额 */
    @Excel(name = "佣金额")
    private BigDecimal commission;

    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 查询日期 */
    @TableField(exist = false)
    private String createDate;

    /** 用户手机号 */
    @TableField(exist = false)
    private String userName;

}

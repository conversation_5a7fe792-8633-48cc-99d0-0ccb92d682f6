package com.ruoyi.sxsc.person.service;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.person.domain.SxscUserCommissionQuota;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 佣金提现额度Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface ISxscUserCommissionQuotaService extends IService<SxscUserCommissionQuota>
{
    /**
     * 查询佣金提现额度
     * 
     * @param id 佣金提现额度主键
     * @return 佣金提现额度
     */
    public SxscUserCommissionQuota selectSxscUserCommissionQuotaById(Long id);

    /**
     * 查询佣金提现额度列表
     * 
     * @param sxscUserCommissionQuota 佣金提现额度
     * @return 佣金提现额度集合
     */
    public List<SxscUserCommissionQuota> selectSxscUserCommissionQuotaList(SxscUserCommissionQuota sxscUserCommissionQuota);

    /**
     * 新增佣金提现额度
     * 
     * @param sxscUserCommissionQuota 佣金提现额度
     * @return 结果
     */
    public AjaxResult insertSxscUserCommissionQuota(SxscUserCommissionQuota sxscUserCommissionQuota);

    /**
     * 获取佣金提现额度
     * 
     * @param userId 用户主键
     * @return 结果
     */
    public BigDecimal getCommissionQuotaUserId(Long userId);


    /**
     * 删除佣金提现额度信息
     * 
     * @param id 佣金提现额度主键
     * @return 结果
     */
    public int deleteSxscUserCommissionQuotaById(Long id);
}

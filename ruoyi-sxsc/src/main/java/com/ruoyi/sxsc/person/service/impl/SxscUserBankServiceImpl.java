package com.ruoyi.sxsc.person.service.impl;

import com.aliyun.cloudauth20190307.models.BankMetaVerifyResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.person.domain.SxscUserBank;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.mapper.SxscUserBankMapper;
import com.ruoyi.sxsc.person.service.ISxscUserBankService;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.sxsc.utils.IdCardUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 银行卡信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class SxscUserBankServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserBankMapper,SxscUserBank> implements ISxscUserBankService
{

    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    IdCardUtil idCardUtil;

    /**
     * 查询银行卡信息
     * 
     * @param id 银行卡信息主键
     * @return 银行卡信息
     */
    @Override
    public SxscUserBank selectSxscUserBankById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询银行卡信息列表
     * 
     * @param sxscUserBank 银行卡信息
     * @return 银行卡信息
     */
    @Override
    public List<SxscUserBank> selectSxscUserBankList(SxscUserBank sxscUserBank)
    {
        LambdaQueryWrapper<SxscUserBank> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserBank::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.eq(StringUtils.isNotNull(sxscUserBank.getUserId()),SxscUserBank::getUserId,sxscUserBank.getUserId());
        }
        wrapper.like(StringUtils.isNotNull(sxscUserBank.getBankAccName()),SxscUserBank::getBankAccName,sxscUserBank.getBankAccName());

        wrapper.like(StringUtils.isNotNull(sxscUserBank.getBankAccNumber()),SxscUserBank::getBankAccNumber,sxscUserBank.getBankAccNumber());

        wrapper.like(StringUtils.isNotNull(sxscUserBank.getBankName()),SxscUserBank::getBankName,sxscUserBank.getBankName());

        wrapper.like(StringUtils.isNotNull(sxscUserBank.getBankOpenName()),SxscUserBank::getBankOpenName,sxscUserBank.getBankOpenName());

        wrapper.eq(SxscUserBank::getDelFlag,0l);

        wrapper.orderByDesc(SxscUserBank::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增银行卡信息
     * 
     * @param sxscUserBank 银行卡信息
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscUserBank(SxscUserBank sxscUserBank)
    {

        SxscUserInfo userInfo=iSxscUserInfoService.getById(SecurityUtils.getUserId());

        if(StringUtils.isNull(userInfo.getIdentityTime())){
            return AjaxResult.error("未通过实名认证，无法添加银行卡");
        }
        BankMetaVerifyResponseBody responseBody=idCardUtil.bankCardVerification(userInfo.getIdentityName(),userInfo.getIdentityNumber(),sxscUserBank.getBankAccNumber());
        if(responseBody.getCode().equals("200")){
            if(!responseBody.getResultObject().getBizCode().equals("1")){
                return  AjaxResult.error("校验不⼀致或未查到记录！");
            }
            if(!responseBody.getResultObject().getSubCode().equals("101")){
                return  AjaxResult.error(responseBody.getMessage());
            }
        }else{
            return  AjaxResult.error("校验异常，请联系管理员");
        }
        sxscUserBank.setUserId(SecurityUtils.getUserId());
        sxscUserBank.setDelFlag(0l);
        sxscUserBank.setCreateBy(SecurityUtils.getUsername());
        sxscUserBank.setCreateTime(DateUtils.getNowDate());
        save(sxscUserBank);
        return AjaxResult.success();
    }

    /**
     * 修改银行卡信息
     * 
     * @param sxscUserBank 银行卡信息
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscUserBank(SxscUserBank sxscUserBank)
    {
        SxscUserInfo userInfo=iSxscUserInfoService.getById(SecurityUtils.getUserId());

        if(StringUtils.isNull(userInfo.getIdentityTime())){
            return AjaxResult.error("未通过实名认证，无法添加银行卡");
        }
        BankMetaVerifyResponseBody responseBody=idCardUtil.bankCardVerification(userInfo.getIdentityName(),userInfo.getIdentityNumber(),sxscUserBank.getBankAccNumber());
        if(!responseBody.getCode().equals("200")){
            if(!responseBody.getResultObject().getBizCode().equals("1")){
                return  AjaxResult.error("校验不⼀致或未查到记录！");
            }
            if(!responseBody.getResultObject().getSubCode().equals("101")){
                return  AjaxResult.error(responseBody.getMessage());
            }
        }else{
            return  AjaxResult.error("校验异常，请联系管理员");
        }
        sxscUserBank.setDelFlag(0l);
        sxscUserBank.setUpdateBy(SecurityUtils.getUsername());
        sxscUserBank.setUpdateTime(DateUtils.getNowDate());
        updateById(sxscUserBank);
        return AjaxResult.success();
    }

    /**
     * 批量删除银行卡信息
     * 
     * @param ids 需要删除的银行卡信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscUserBankByIds(Long[] ids)
    {
        LambdaUpdateWrapper<SxscUserBank> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserBank::getDelFlag,1);
        updateWrapper.set(SxscUserBank::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscUserBank::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.in(SxscUserBank::getId,ids);
        return update(updateWrapper)?1:0;
    }

    /**
     * 删除银行卡信息信息
     * 
     * @param id 银行卡信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscUserBankById(Long id)
    {
        return removeById(id)?1:0;
    }
}

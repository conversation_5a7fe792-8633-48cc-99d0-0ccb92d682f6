package com.ruoyi.sxsc.person.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 发票信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserInvoice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 发票抬头 */
    @Excel(name = "发票抬头")
    private String invoiceHeader;

    /** 单位税号 */
    @Excel(name = "单位税号")
    private String taxId;

    /** 注册地址 */
    @Excel(name = "注册地址")
    private String registerAddress;

    /** 注册电话 */
    @Excel(name = "注册电话")
    private String registerPhone;

    /** 开户银行 */
    @Excel(name = "开户银行")
    private String openingBank;

    /** 银行账号 */
    @Excel(name = "银行账号")
    private String bankNumber;

    /** 类型 1企业2个人*/
    @Excel(name = "类型")
    private Long type;

    /** 是否删除 */
    private Long delFlag;

    @TableField(exist = false)
    private SysUserMain sysUser;


}

package com.ruoyi.sxsc.person.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.person.domain.SxscUserCommissionQuota;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionQuotaService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 佣金提现额度
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/person/quota")
public class SxscUserCommissionQuotaController extends BaseController
{
    @Autowired
    private ISxscUserCommissionQuotaService sxscUserCommissionQuotaService;

    /**
     * 查询佣金提现额度列表
     */
    @PreAuthorize("@ss.hasPermi('person:quota:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserCommissionQuota sxscUserCommissionQuota)
    {
        startPage();
        List<SxscUserCommissionQuota> list = sxscUserCommissionQuotaService.selectSxscUserCommissionQuotaList(sxscUserCommissionQuota);
        return getDataTable(list);
    }

    /**
     * 导出佣金提现额度列表
     */
    @PreAuthorize("@ss.hasPermi('person:quota:export')")
    @Log(title = "佣金提现额度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscUserCommissionQuota sxscUserCommissionQuota)
    {
        List<SxscUserCommissionQuota> list = sxscUserCommissionQuotaService.selectSxscUserCommissionQuotaList(sxscUserCommissionQuota);
        ExcelUtil<SxscUserCommissionQuota> util = new ExcelUtil<SxscUserCommissionQuota>(SxscUserCommissionQuota.class);
        util.exportExcel(response, list, "佣金提现额度数据");
    }

    /**
     * 获取佣金提现额度详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:quota:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserCommissionQuotaService.selectSxscUserCommissionQuotaById(id));
    }

    /**
     * 新增佣金提现额度
     */
    @PreAuthorize("@ss.hasPermi('person:quota:add')")
    @Log(title = "佣金提现额度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserCommissionQuota sxscUserCommissionQuota)
    {
        return sxscUserCommissionQuotaService.insertSxscUserCommissionQuota(sxscUserCommissionQuota);
    }

}

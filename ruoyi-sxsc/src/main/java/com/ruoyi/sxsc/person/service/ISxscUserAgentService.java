package com.ruoyi.sxsc.person.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.person.domain.SxscUserAgent;

import java.util.List;
import java.util.Map;

/**
 * 代理信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface ISxscUserAgentService extends IService<SxscUserAgent>
{
    /**
     * 查询代理信息
     * 
     * @param id 代理信息主键
     * @return 代理信息
     */
    SxscUserAgent selectSxscUserAgentById(Long id);

    /**
     * 查询代理统计信息
     *
     * @param sxscUserAgent 代理信息
     * @return 代理信息
     */
    Map<String,Object> statistics(SxscUserAgent sxscUserAgent);
    /**
     * 查询省级代理信息
     *
     * @param provinceCode 省级编码
     * @return 代理信息
     */
    List<SxscUserAgent> selectSxscUserAgentProvinceCode(String provinceCode);

    /**
     * 查询县级代理信息
     *
     * @param countyCode 县级编码
     * @return 代理信息
     */
    List<SxscUserAgent> selectSxscUserAgentCountyCode(String countyCode);

    /**
     * 查询市级代理信息
     *
     * @param cityCode 市级编码
     * @return 代理信息
     */
    List<SxscUserAgent> selectSxscUserAgentCityCode(String cityCode);

    /**
     * 查询代理信息列表
     * 
     * @param sxscUserAgent 代理信息
     * @return 代理信息集合
     */
    List<SxscUserAgent> selectSxscUserAgentList(SxscUserAgent sxscUserAgent);

    /**
     * 新增代理信息
     * 
     * @param sxscUserAgent 代理信息
     * @return 结果
     */
    AjaxResult insertSxscUserAgent(SxscUserAgent sxscUserAgent);

    /**
     * 修改代理信息
     * 
     * @param sxscUserAgent 代理信息
     * @return 结果
     */
    int updateSxscUserAgent(SxscUserAgent sxscUserAgent);

    /**
     * 获取省市级数据
     * 
     * @return 结果
     */
    AjaxResult district();
    /**
     * 获取全国代理
     *
     * @return 结果
     */
    List<SxscUserAgent> getNational();
}

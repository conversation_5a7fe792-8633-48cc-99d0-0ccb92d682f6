package com.ruoyi.sxsc.person.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.person.domain.SxscUserCommission;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 佣金信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface SxscUserCommissionMapper extends BaseMapper<SxscUserCommission>
{

    /**
     * 查询佣金总额
     * @param userId
     * @param createTime
     * @return  佣金总额
     */
    @Select("<script>"+
            "select IFNULL(sum(commission), 0)   from sxsc_user_commission " +
            "where  1=1 " +
            "<if test='userId != null '> and  user_id=#{userId} </if>"+
            "<if test='commodityOrderId != null and commodityOrderId != \"\" '> and  commodity_order_id=#{commodityOrderId} </if>"+
            "<if test='createTime != null '> and  date_format(create_time,'%Y-%m-%d')=#{createTime} </if>"+
            "</script>")
    BigDecimal commissionSum(@Param("userId")Long userId,@Param("createTime")String createTime,@Param("commodityOrderId")String commodityOrderId);





}

package com.ruoyi.sxsc.person.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 人员基本信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserInfoExtensionModelRes
{

    /** 是否有效用户 1有效0无效 */
    private Long effective;

    /** 昵称 */
    private String nickName;

    /** 手机号 */
    private String phonenumber;

    /** 用户头像 */
    private String avatar;

    /** 加入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /** 是否实名 1实名0未实名 */
    private Long realName;

    /** 订单总数 */
    private Long orderCount;

    /** 订单总金额 */
    private BigDecimal amountSum;

    /** 用户主键 */
    private Long userId;

    /** 惠购值 */
    private BigDecimal preferentialCredit;

    /** 当日是否完成任务 */
    private Long task;

}

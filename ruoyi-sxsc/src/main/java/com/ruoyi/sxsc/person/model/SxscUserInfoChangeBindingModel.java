package com.ruoyi.sxsc.person.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 人员基本信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserInfoChangeBindingModel
{

    /** 原账号手机号 */
    private String oldPhone;

    /** 原账号验证码 */
    private String oldCode;

    /** 换绑后的手机号 */
    private String newPhone;

    /** 换绑后的手机号验证码 */
    private String newCode;

}

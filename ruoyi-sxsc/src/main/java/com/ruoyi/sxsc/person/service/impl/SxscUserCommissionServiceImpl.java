package com.ruoyi.sxsc.person.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import com.ruoyi.sxsc.person.domain.SxscUserCommission;
import com.ruoyi.sxsc.person.mapper.SxscUserCommissionMapper;
import com.ruoyi.sxsc.person.mapper.SxscUserCommissionOrderMapper;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionOrderService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 佣金信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
public class SxscUserCommissionServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserCommissionMapper,SxscUserCommission> implements ISxscUserCommissionService
{

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    SxscUserCommissionMapper sxscUserCommissionMapper;

    @Autowired
    private SxscUserCommissionOrderMapper sxscUserCommissionOrderMapper;

    @Autowired
    private ISxscUserCommissionOrderService iSxscUserCommissionOrderService;

    @Autowired
    private ISxscAliPayOrderService iSxscAliPayOrderService;

    @Autowired
    ISxscCommodityOrderService iSxscCommodityOrderService;

    /**
     * 查询佣金信息
     * 
     * @param id 佣金信息主键
     * @return 佣金信息
     */
    @Override
    public SxscUserCommission selectSxscUserCommissionById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询佣金信息列表
     * 
     * @param sxscUserCommission 佣金信息
     * @return 佣金信息
     */
    @Override
    public List<SxscUserCommission> selectSxscUserCommissionList(SxscUserCommission sxscUserCommission)
    {
        LambdaQueryWrapper<SxscUserCommission> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserCommission::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserCommission.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserCommission.getParams().get("phonenumber")+"', '%'))");
        }

        wrapper.apply( StringUtils.isNotNull(sxscUserCommission.getCreateDate()),"DATE_FORMAT(create_time,'%Y-%m') = '"+ sxscUserCommission.getCreateDate()+"'");

        wrapper.eq(StringUtils.isNotNull(sxscUserCommission.getCommodityOrderId()),SxscUserCommission::getCommodityOrderId,sxscUserCommission.getCommodityOrderId());

        wrapper.eq(StringUtils.isNotNull(sxscUserCommission.getCommission()),SxscUserCommission::getCommission,sxscUserCommission.getCommission());

        wrapper.orderByDesc(SxscUserCommission::getCreateTime);

        List<SxscUserCommission> list=list(wrapper);

        for(SxscUserCommission userCommission:list){
            userCommission.setSysUser(iSysUserService.selectUserMainById(userCommission.getUserId()));
        }

        return list;
    }


    /**
     * 新增佣金信息
     *
     * @param userId 用户主键
     * @param orderId 订单主键
     * @param amount 佣金额
     * @param name 佣金名称
     * @return 结果
     */
    @Override
    public int insertSxscUserCommission(Long userId, String orderId, BigDecimal amount,String name){
        SxscUserCommission sxscUserCommission=new SxscUserCommission();
        sxscUserCommission.setUserId(userId);
        sxscUserCommission.setCommodityOrderId(orderId);
        sxscUserCommission.setName(name);
        sxscUserCommission.setCommission(amount);
        sxscUserCommission.setCreateTime(DateUtils.getNowDate());
        return save(sxscUserCommission)?1:0;
    }
    /**
     * 新增佣金信息
     *
     * @param sxscUserCommission 佣金信息
     * @return 结果
     */
    @Override
    public int insertSxscUserCommission(SxscUserCommission sxscUserCommission){
        SysUser sysUser=iSysUserService.selectUserByUserName(sxscUserCommission.getUserName());
        if(StringUtils.isNull(sysUser)){
            throw new ServiceException("用户不存在");
        }
        sxscUserCommission.setCommodityOrderId(IdUtils.fastSimpleUUID());
        sxscUserCommission.setUserId(sysUser.getUserId());
        sxscUserCommission.setCreateBy(SecurityUtils.getUsername());
        sxscUserCommission.setCreateTime(DateUtils.getNowDate());
        return save(sxscUserCommission)?1:0;
    }
    /**
     * 商品-佣金信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    @Override
    public void refundSxscUserCommission(String orderId){
        LambdaQueryWrapper<SxscUserCommission> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserCommission::getCommodityOrderId,orderId);
        List<SxscUserCommission> list=list(wrapper);
        for(SxscUserCommission sxscUserCommission:list){
            this.insertSxscUserCommission(sxscUserCommission.getUserId(),orderId,sxscUserCommission.getCommission().multiply(new BigDecimal("-1")),"退款-"+sxscUserCommission.getName());
        }
    }

    /**
     * 查询可使用佣金总额
     *
     * @param userId 用户主键
     * @return 结果
     */
    @Override
    public BigDecimal usedUserCommission(Long userId){
        //佣金总额
        BigDecimal total=sxscUserCommissionMapper.commissionSum(userId,null,null);
        //已提现佣金额
        BigDecimal withdrawal=iSxscUserCommissionOrderService.commissionOrderSum(userId);

        return total.subtract(withdrawal).setScale(2);
    }


    /**
     * 统计佣金信息
     *
     * @param userId 用户主键
     * @return 结果
     */
    public Map<String,Object> statistics(Long userId){
        Map<String,Object> map=new HashMap<>();

        //佣金总额
        BigDecimal total=sxscUserCommissionMapper.commissionSum(userId,null,null);
        //已提现佣金额
        BigDecimal withdrawal=sxscUserCommissionOrderMapper.commissionOrderSum(userId,null);
        //当前佣金
        map.put("currentCommission",total.subtract(withdrawal));
        //昨日收益
        map.put("yesterday",sxscUserCommissionMapper.commissionSum(userId,DateUtils.dateTime(DateUtils.addDays(new Date(),-1)),null));
        //累计提现
        map.put("withdrawal",withdrawal);
        //佣金总额
        map.put("total",total);

        return map;
    }

    /**
     * 佣金结算订单
     *
     * @param sxscCommodityOrder 订单信息
     * @return 结果
     */
    /**
     * 佣金结算订单
     *
     * @param orderId 订单编号
     * @param totalAmount 订单总金额
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult settlement(BigDecimal totalAmount,String orderId){
        //佣金总额
        BigDecimal total=sxscUserCommissionMapper.commissionSum(SecurityUtils.getUserId(),null,null);
        //已提现佣金额
        BigDecimal withdrawal=iSxscUserCommissionOrderService.commissionOrderSum(SecurityUtils.getUserId());
        //当前佣金
        BigDecimal currentCommission=total.subtract(withdrawal);

        if(currentCommission.compareTo(totalAmount)<0){
            return AjaxResult.error("佣金余额不足，支付失败");
        }
        iSxscUserCommissionOrderService.insertSxscUserCommissionOrder(totalAmount,orderId);
        iSxscCommodityOrderService.updateXgfmCommodityOrderPayment(orderId,totalAmount);
        return AjaxResult.success();
    }

}

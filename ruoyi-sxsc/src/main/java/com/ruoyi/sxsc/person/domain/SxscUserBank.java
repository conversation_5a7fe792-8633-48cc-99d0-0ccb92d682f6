package com.ruoyi.sxsc.person.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 银行卡信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserBank extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户主键 */
    private Long userId;

    /** 银行卡账户姓名 */
    @Excel(name = "银行卡账户姓名")
    private String bankAccName;

    /** 银行卡账户卡号 */
    @Excel(name = "银行卡账户卡号")
    private String bankAccNumber;

    /** 银行卡名称 */
    @Excel(name = "银行卡名称")
    private String bankName;

    /** 开户行名称 */
    @Excel(name = "开户行名称")
    private String bankOpenName;

    /** 是否删除 */
    private Long delFlag;




}

package com.ruoyi.sxsc.person.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.person.domain.SxscUserAddress;

import java.util.List;

/**
 * 地址信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
public interface ISxscAddressService extends IService<SxscUserAddress>
{
    /**
     * 查询地址信息
     * 
     * @param id 地址信息主键
     * @return 地址信息
     */
    SxscUserAddress selectSxscAddressById(Long id);

    /**
     * 查询地址信息
     *
     * @param userId 用户主键
     * @param status 地址信息状态
     * @return 地址信息
     */
    SxscUserAddress selectSxscAddressByUserId(Long userId, Long status);

    /**
     * 查询地址信息列表
     * 
     * @param sxscUserAddress 地址信息
     * @return 地址信息集合
     */
    List<SxscUserAddress> selectSxscAddressList(SxscUserAddress sxscUserAddress);

    /**
     * 新增地址信息
     * 
     * @param sxscUserAddress 地址信息
     * @return 结果
     */
    int insertSxscAddress(SxscUserAddress sxscUserAddress);

    /**
     * 修改地址信息
     * 
     * @param sxscUserAddress 地址信息
     * @return 结果
     */
    int updateSxscAddress(SxscUserAddress sxscUserAddress);


    /**
     * 删除地址信息信息
     * 
     * @param id 地址信息主键
     * @return 结果
     */
    int deleteSxscAddressById(Long id);
}

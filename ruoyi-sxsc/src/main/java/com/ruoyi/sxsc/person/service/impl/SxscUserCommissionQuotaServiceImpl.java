package com.ruoyi.sxsc.person.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Arrays;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.sxsc.person.domain.SxscUserCommission;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.person.mapper.SxscUserCommissionQuotaMapper;
import com.ruoyi.sxsc.person.domain.SxscUserCommissionQuota;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionQuotaService;

/**
 * 佣金提现额度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Service
public class SxscUserCommissionQuotaServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserCommissionQuotaMapper,SxscUserCommissionQuota> implements ISxscUserCommissionQuotaService
{

    @Autowired
    ISysUserService iSysUserService;

    /**
     * 查询佣金提现额度
     * 
     * @param id 佣金提现额度主键
     * @return 佣金提现额度
     */
    @Override
    public SxscUserCommissionQuota selectSxscUserCommissionQuotaById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询佣金提现额度列表
     * 
     * @param sxscUserCommissionQuota 佣金提现额度
     * @return 佣金提现额度
     */
    @Override
    public List<SxscUserCommissionQuota> selectSxscUserCommissionQuotaList(SxscUserCommissionQuota sxscUserCommissionQuota)
    {
        LambdaQueryWrapper<SxscUserCommissionQuota> wrapper=new LambdaQueryWrapper();

        wrapper.apply(StringUtils.isNotNull(sxscUserCommissionQuota.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserCommissionQuota.getParams().get("phonenumber")+"', '%'))");

        wrapper.eq(StringUtils.isNotNull(sxscUserCommissionQuota.getUserId()),SxscUserCommissionQuota::getUserId,sxscUserCommissionQuota.getUserId());

        wrapper.eq(StringUtils.isNotNull(sxscUserCommissionQuota.getCreateBy()),SxscUserCommissionQuota::getCreateBy,sxscUserCommissionQuota.getCreateBy());

        wrapper.eq(StringUtils.isNotNull(sxscUserCommissionQuota.getDelFlag()),SxscUserCommissionQuota::getDelFlag,sxscUserCommissionQuota.getDelFlag());

        wrapper.orderByDesc(SxscUserCommissionQuota::getCreateTime);

        List<SxscUserCommissionQuota> list=list(wrapper);

        for(SxscUserCommissionQuota userCommission:list){
            userCommission.setSysUser(iSysUserService.selectUserMainById(userCommission.getUserId()));
        }

        return list;
    }

    /**
     * 新增佣金提现额度
     * 
     * @param sxscUserCommissionQuota 佣金提现额度
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscUserCommissionQuota(SxscUserCommissionQuota sxscUserCommissionQuota)
    {
        if(StringUtils.isEmpty(sxscUserCommissionQuota.getUserName())){
            return AjaxResult.error("请输入账号");
        }
        SysUser sysUser=iSysUserService.selectUserByUserName(sxscUserCommissionQuota.getUserName());
        if(StringUtils.isNull(sysUser)){
            return AjaxResult.error("账号不存在");
        }
        sxscUserCommissionQuota.setUserId(sysUser.getUserId());
        deleteSxscUserCommissionQuotaById(sxscUserCommissionQuota.getUserId());
        sxscUserCommissionQuota.setCreateBy(SecurityUtils.getUsername());
        sxscUserCommissionQuota.setCreateTime(DateUtils.getNowDate());
        sxscUserCommissionQuota.setDelFlag(0L);
        save(sxscUserCommissionQuota);
        return AjaxResult.success();
    }

    /**
     * 获取佣金提现额度
     *
     * @param userId 用户主键
     * @return 结果
     */
    @Override
    public BigDecimal getCommissionQuotaUserId(Long userId)
    {
        LambdaQueryWrapper<SxscUserCommissionQuota> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscUserCommissionQuota::getUserId,userId);
        queryWrapper.eq(SxscUserCommissionQuota::getDelFlag,0);
        SxscUserCommissionQuota sxscUserCommissionQuota=getOne(queryWrapper);
        if(StringUtils.isNull(sxscUserCommissionQuota)){
            return new BigDecimal("0");
        }
        return sxscUserCommissionQuota.getQuota();
    }


    /**
     * 删除佣金提现额度信息
     * 
     * @param userId 佣金提现额度用户主键
     * @return 结果
     */
    @Override
    public int deleteSxscUserCommissionQuotaById(Long userId)
    {
        LambdaUpdateWrapper<SxscUserCommissionQuota> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscUserCommissionQuota::getUserId,userId);
        updateWrapper.eq(SxscUserCommissionQuota::getDelFlag,0);
        updateWrapper.set(SxscUserCommissionQuota::getDelFlag,1);
        updateWrapper.set(SxscUserCommissionQuota::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscUserCommissionQuota::getUpdateTime,DateUtils.getNowDate());
        return update(updateWrapper)?1:0;
    }
}

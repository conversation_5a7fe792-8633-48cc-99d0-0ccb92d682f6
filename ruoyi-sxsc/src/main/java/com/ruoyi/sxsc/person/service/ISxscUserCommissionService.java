package com.ruoyi.sxsc.person.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.person.domain.SxscUserCommission;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 佣金信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface ISxscUserCommissionService extends IService<SxscUserCommission>
{
    /**
     * 查询佣金信息
     * 
     * @param id 佣金信息主键
     * @return 佣金信息
     */
    SxscUserCommission selectSxscUserCommissionById(Long id);

    /**
     * 查询佣金信息列表
     * 
     * @param sxscUserCommission 佣金信息
     * @return 佣金信息集合
     */
    List<SxscUserCommission> selectSxscUserCommissionList(SxscUserCommission sxscUserCommission);


    /**
     * 新增佣金信息
     *
     * @param userId 用户主键
     * @param orderId 订单主键
     * @param amount 佣金额
     * @param name 佣金名称
     * @return 结果
     */
    int insertSxscUserCommission(Long userId, String orderId, BigDecimal amount,String name);


    /**
     * 新增佣金信息
     *
     * @param sxscUserCommission 佣金信息
     * @return 结果
     */
    int insertSxscUserCommission(SxscUserCommission sxscUserCommission);

    /**
     * 商品退款-佣金信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    void refundSxscUserCommission(String orderId);

    /**
     * 查询可使用佣金总额
     *
     * @param userId 用户主键
     * @return 结果
     */
    BigDecimal usedUserCommission(Long userId);

    /**
     * 统计佣金信息
     *
     * @param userId 用户主键
     * @return 结果
     */
    Map<String,Object> statistics(Long userId);

    /**
     * 佣金结算订单
     *
     * @param orderId 订单编号
     * @param totalAmount 订单总金额
     * @return 结果
     */
    AjaxResult settlement(BigDecimal totalAmount,String orderId);
}

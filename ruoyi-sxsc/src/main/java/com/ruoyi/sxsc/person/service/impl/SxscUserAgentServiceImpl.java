package com.ruoyi.sxsc.person.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityOrderMapper;
import com.ruoyi.sxsc.person.domain.SxscUserAgent;
import com.ruoyi.sxsc.person.mapper.SxscUserAgentMapper;
import com.ruoyi.sxsc.person.mapper.SxscUserInfoMapper;
import com.ruoyi.sxsc.person.service.ISxscUserAgentService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代理信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class SxscUserAgentServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserAgentMapper,SxscUserAgent> implements ISxscUserAgentService
{

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    SxscUserInfoMapper sxscUserInfoMapper;

    @Autowired
    SxscCommodityOrderMapper sxscCommodityOrderMapper;

    /**
     * 查询代理信息
     * 
     * @param id 代理信息主键
     * @return 代理信息
     */
    @Override
    public SxscUserAgent selectSxscUserAgentById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询代理统计信息
     *
     * @param sxscUserAgent 代理信息
     * @return 代理信息
     */
    @Override
    public Map<String,Object> statistics(SxscUserAgent sxscUserAgent){
        Map<String,Object> map=new HashMap<>();
        Long yesterdayPeople=sxscUserInfoMapper.agentPeopleCount(DateUtils.addDaysToCurrentDate(-1),sxscUserAgent.getProvince(),sxscUserAgent.getCity(),sxscUserAgent.getCounty());
        Long todayPeople=sxscUserInfoMapper.agentPeopleCount(DateUtils.dateTime(DateUtils.getNowDate()),sxscUserAgent.getProvince(),sxscUserAgent.getCity(),sxscUserAgent.getCounty());

        BigDecimal yesterdayTurnover=sxscCommodityOrderMapper.agentOrderTotalAmount(5l,DateUtils.addDaysToCurrentDate(-1),sxscUserAgent.getProvince(),sxscUserAgent.getCity(),sxscUserAgent.getCounty());
        BigDecimal todayTurnover=sxscCommodityOrderMapper.agentOrderTotalAmount(5l,DateUtils.dateTime(DateUtils.getNowDate()),sxscUserAgent.getProvince(),sxscUserAgent.getCity(),sxscUserAgent.getCounty());

        map.put("yesterdayPeople",yesterdayPeople);
        map.put("todayPeople",todayPeople);
        map.put("yesterdayTurnover",yesterdayTurnover);
        map.put("todayTurnover",todayTurnover);
        return map;
    }

    @Override
    public List<SxscUserAgent> selectSxscUserAgentProvinceCode(String provinceCode) {
        if(StringUtils.isEmpty(provinceCode)){
            return null;
        }
        LambdaQueryWrapper<SxscUserAgent> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserAgent::getProvinceCode,provinceCode);
        wrapper.eq(SxscUserAgent::getGrade,1);
        wrapper.eq(SxscUserAgent::getStatus,1);
        return list(wrapper);
    }

    @Override
    public  List<SxscUserAgent> selectSxscUserAgentCountyCode(String countyCode) {
        if(StringUtils.isEmpty(countyCode)){
            return null;
        }
        LambdaQueryWrapper<SxscUserAgent> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserAgent::getCountyCode,countyCode);
        wrapper.eq(SxscUserAgent::getStatus,1);
        wrapper.eq(SxscUserAgent::getGrade,3);
        return list(wrapper);
    }

    @Override
    public  List<SxscUserAgent> selectSxscUserAgentCityCode(String cityCode) {
        if(StringUtils.isEmpty(cityCode)){
            return null;
        }
        LambdaQueryWrapper<SxscUserAgent> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserAgent::getCityCode,cityCode);
        wrapper.eq(SxscUserAgent::getStatus,1);
        wrapper.eq(SxscUserAgent::getGrade,2);
        return list(wrapper);
    }

    /**
     * 查询代理信息列表
     * 
     * @param sxscUserAgent 代理信息
     * @return 代理信息
     */
    @Override
    public List<SxscUserAgent> selectSxscUserAgentList(SxscUserAgent sxscUserAgent)
    {
        LambdaQueryWrapper<SxscUserAgent> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getUserId()),SxscUserAgent::getUserId,sxscUserAgent.getUserId());

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getProvince()),SxscUserAgent::getProvince,sxscUserAgent.getProvince());

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getProvinceCode()),SxscUserAgent::getProvinceCode,sxscUserAgent.getProvinceCode());

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getCity()),SxscUserAgent::getCity,sxscUserAgent.getCity());

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getCityCode()),SxscUserAgent::getCityCode,sxscUserAgent.getCityCode());

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getCounty()),SxscUserAgent::getCounty,sxscUserAgent.getCounty());

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getCountyCode()),SxscUserAgent::getCountyCode,sxscUserAgent.getCountyCode());

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getGrade()),SxscUserAgent::getGrade,sxscUserAgent.getGrade());

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getStatus()),SxscUserAgent::getStatus,sxscUserAgent.getStatus());

        wrapper.eq(StringUtils.isNotNull(sxscUserAgent.getNational()),SxscUserAgent::getNational,sxscUserAgent.getNational());

        wrapper.orderByDesc(SxscUserAgent::getCreateTime);

        List<SxscUserAgent> agents=list(wrapper);

        for(SxscUserAgent agent:agents){
            agent.setSysUser(iSysUserService.selectUserMainById(agent.getUserId()));
        }

        return agents;
    }

    /**
     * 新增代理信息
     * 
     * @param sxscUserAgent 代理信息
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscUserAgent(SxscUserAgent sxscUserAgent)
    {
        if(StringUtils.isNotEmpty(sxscUserAgent.getProvince())){
            sxscUserAgent.setProvinceCode(sxscUserAgent.getProvince().split("\\|")[0]);
            sxscUserAgent.setProvince(sxscUserAgent.getProvince().split("\\|")[1]);
            sxscUserAgent.setGrade(1L);
        }
        if(StringUtils.isNotEmpty(sxscUserAgent.getCity())){
            sxscUserAgent.setCityCode(sxscUserAgent.getCity().split("\\|")[0]);
            sxscUserAgent.setCity(sxscUserAgent.getCity().split("\\|")[1]);
            sxscUserAgent.setGrade(2L);
        }
        if(StringUtils.isNotEmpty(sxscUserAgent.getCounty())){
            sxscUserAgent.setCountyCode(sxscUserAgent.getCounty().split("\\|")[0]);
            sxscUserAgent.setCounty(sxscUserAgent.getCounty().split("\\|")[1]);
            sxscUserAgent.setGrade(3L);
        }

        sxscUserAgent.setStatus(1L);
        sxscUserAgent.setCreateBy(SecurityUtils.getUsername());
        sxscUserAgent.setCreateTime(DateUtils.getNowDate());
        save(sxscUserAgent);
        return AjaxResult.success();
    }

    /**
     * 修改代理信息
     * 
     * @param sxscUserAgent 代理信息
     * @return 结果
     */
    @Override
    public int updateSxscUserAgent(SxscUserAgent sxscUserAgent)
    {
        sxscUserAgent.setUpdateBy(SecurityUtils.getUsername());
        sxscUserAgent.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscUserAgent)?1:0;
    }

    /**
     * 获取省市级数据
     * 
     * @return 结果
     */
    @Override
    public AjaxResult district() {
        return AjaxResult.success(JSONObject.parseObject(doGet("https://restapi.amap.com/v3/config/district?key=3a708a4ef5e3af28694b1c861985a5ce&keyWords=%E4%B8%AD%E5%9B%BD&subdistrict=3").replaceAll("districts","children")));
    }

    /**
     * 获取全国代理
     *
     * @return 结果
     */
    @Override
    public List<SxscUserAgent> getNational(){
        LambdaQueryWrapper<SxscUserAgent> wrapper=new LambdaQueryWrapper();

        wrapper.eq(SxscUserAgent::getStatus,1);

        wrapper.eq(SxscUserAgent::getNational,1);

        wrapper.orderByDesc(SxscUserAgent::getCreateTime);

        return list(wrapper);
    }

    private static String doGet(String httpUrl){
        //链接
        HttpURLConnection connection = null;
        InputStream is = null;
        BufferedReader br = null;
        StringBuffer result = new StringBuffer();
        try {
            //创建连接
            URL url = new URL(httpUrl);
            connection = (HttpURLConnection) url.openConnection();
            //设置请求方式
            connection.setRequestMethod("GET");
            //设置连接超时时间
            connection.setReadTimeout(15000);
            //开始连接
            connection.connect();
            //获取响应数据
            if (connection.getResponseCode() == 200) {
                //获取返回的数据
                is = connection.getInputStream();
                if (null != is) {
                    br = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                    String temp = null;
                    while (null != (temp = br.readLine())) {
                        result.append(temp);
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            //关闭远程连接
            connection.disconnect();
        }
        return result.toString();
    }
}

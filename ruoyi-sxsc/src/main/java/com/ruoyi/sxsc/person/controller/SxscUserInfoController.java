package com.ruoyi.sxsc.person.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.model.*;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人员基本信息
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/person/info")
public class SxscUserInfoController extends BaseController
{
    @Autowired
    private ISxscUserInfoService sxscUserInfoService;

    /**
     * 查询人员基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('person:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserInfoModel sxscUserInfo)
    {
        startPage();
        List<SxscUserInfo> list = sxscUserInfoService.selectSxscUserInfoList(sxscUserInfo);
        return getDataTable(list);
    }

    @Log(title = "人员信息导入", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('person:info:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SxscUserInfoModelExcel> util = new ExcelUtil<>(SxscUserInfoModelExcel.class);
        List<SxscUserInfoModelExcel> infoModelExcels = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = sxscUserInfoService.importInfo(infoModelExcels, updateSupport, operName);
        return success(message);
    }

    /**
     * 获取人员基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:info:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {

        return success(sxscUserInfoService.selectSxscUserInfoByUserId(userId));
    }

    /**
     * 获取当前登陆人详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:info:query')")
    @GetMapping(value = "/login")
    public AjaxResult getInfo()
    {
        return success(sxscUserInfoService.selectSxscUserInfoByUserId(SecurityUtils.getUserId()));
    }

    /**
     * 修改人员基本信息
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping()
    public AjaxResult edit(@RequestBody SxscUserInfoModel sxscUserInfo)
    {
        return sxscUserInfoService.updateSxscUserInfo(sxscUserInfo);
    }

    /**
     * 修改人员推荐节点账号
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/nodeCode")
    public AjaxResult editNode(@RequestBody SxscUserInfoModel sxscUserInfo)
    {
        return sxscUserInfoService.updateSxscUserInfoNodeId(sxscUserInfo);
    }

    /**
     * 修改人员票证账号
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit')")
    @Log(title = "修改人员票证账号", businessType = BusinessType.UPDATE)
    @PutMapping("/editTicketAcc")
    public AjaxResult editTicketAcc(@RequestBody SxscUserInfoModel sxscUserInfo)
    {
        return sxscUserInfoService.updateSxscUserInfoTicketAcc(sxscUserInfo);
    }

    /**
     * 修改人员PT地址
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit')")
    @Log(title = "修改人员票证账号", businessType = BusinessType.UPDATE)
    @PutMapping("/ptAddress")
    public AjaxResult editPtAddress(@RequestBody SxscUserInfoModel sxscUserInfo)
    {
        return sxscUserInfoService.updateSxscUserInfoPtAddress(sxscUserInfo);
    }

    /**
     * 删除人员基本信息
     */
    @PreAuthorize("@ss.hasPermi('person:info:remove')")
    @Log(title = "人员基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return sxscUserInfoService.deleteSxscUserInfoByUserIds(userIds);
    }

    /**
     * 注销当前账号
     */
    @PreAuthorize("@ss.hasPermi('person:info:logOff')")
    @Log(title = "人员基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/logOff")
    public AjaxResult logOff()
    {
        return sxscUserInfoService.deleteSxscUserInfoByUserIds(new Long[]{SecurityUtils.getUserId()});
    }


    /**
     * 换绑当前账号
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit')")
    @Log(title = "人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/changeBinding")
    public AjaxResult changeBinding(@RequestBody SxscUserInfoChangeBindingModel changeBindingModel)
    {
        return sxscUserInfoService.changeBinding(changeBindingModel);
    }

    /**
     * 修改人员富星拼团购
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit:teamLeader')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/teamLeader")
    public AjaxResult editTeamLeader(@RequestBody SxscUserInfo sxscUserInfo)
    {
        return sxscUserInfoService.updateSxscUserInfo(sxscUserInfo);
    }

    /**
     * 修改人员富星超值购
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit:benefitRights')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/benefitRights")
    public AjaxResult editBenefitRights(@RequestBody SxscUserInfo sxscUserInfo)
    {
        return sxscUserInfoService.updateSxscUserInfo(sxscUserInfo);
    }
    /**
     * 修改人员富星品牌购
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit:buyingMerchants')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/buyingMerchants")
    public AjaxResult editBuyingMerchants(@RequestBody SxscUserInfo sxscUserInfo)
    {
        return sxscUserInfoService.updateSxscUserInfo(sxscUserInfo);
    }
    /**
     * 修改人员有效用户
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit:effective')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/effective")
    public AjaxResult effective(@RequestBody SxscUserInfo sxscUserInfo)
    {
        return sxscUserInfoService.updateSxscUserInfo(sxscUserInfo);
    }


    /**
     * 清空推荐人
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit:parentClear')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/parentClear/{userId}")
    public AjaxResult parentClear(@PathVariable Long userId)
    {
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscUserInfo::getUserId,userId);
        updateWrapper.set(SxscUserInfo::getParentId,null);
        sxscUserInfoService.update(updateWrapper);
        return AjaxResult.success();
    }

    /**
     * 清空节点人
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit:parentClear')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/nodeClear/{userId}")
    public AjaxResult nodeClear(@PathVariable Long userId)
    {
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscUserInfo::getUserId,userId);
        updateWrapper.set(SxscUserInfo::getNodeId,null);
        sxscUserInfoService.update(updateWrapper);
        return AjaxResult.success();
    }
    /**
     * 清空实名认证信息
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit:identityClear')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/identityClear/{userId}")
    public AjaxResult identityClear(@PathVariable Long userId)
    {
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscUserInfo::getUserId,userId);
        updateWrapper.set(SxscUserInfo::getIdentityNumber,null);
        updateWrapper.set(SxscUserInfo::getIdentityName,null);
        updateWrapper.set(SxscUserInfo::getIdentityTime,null);
        updateWrapper.set(SxscUserInfo::getIdentityDate,null);
        updateWrapper.set(SxscUserInfo::getIdentitySex,null);
        sxscUserInfoService.update(updateWrapper);
        return AjaxResult.success();
    }

    /**
     * 修改合伙人身份
     */
    @PreAuthorize("@ss.hasPermi('person:info:edit:copartner')")
    @Log(title = "修改人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/copartner/{userId}")
    public AjaxResult identityClear(@PathVariable Long userId,@RequestBody SxscUserInfo sxscUserInfo)
    {
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscUserInfo::getUserId,userId);
        updateWrapper.set(SxscUserInfo::getCopartner,sxscUserInfo.getCopartner());
        updateWrapper.set(SxscUserInfo::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscUserInfo::getUpdateTime, DateUtils.dateTime());
        sxscUserInfoService.update(updateWrapper);
        return AjaxResult.success();
    }

    /**
     * 查询人推广人统计数据
     */
    @PreAuthorize("@ss.hasPermi('person:info:list')")
    @GetMapping("/extension/userSum")
    public AjaxResult extensionUserSum()
    {
        Map<String,Integer> map=new HashMap<>();
        LambdaQueryWrapper<SxscUserInfo> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscUserInfo::getParentId,SecurityUtils.getUserId());
        queryWrapper.eq(SxscUserInfo::getDelFlag,0);
        //推荐用户
        map.put("recommend",sxscUserInfoService.count(queryWrapper));
        queryWrapper.eq(SxscUserInfo::getEffective,1);
        //有效用户
        map.put("effective",sxscUserInfoService.count(queryWrapper));

        return success(map);
    }



    /**
     * 查询人推广人订单
     */
    @GetMapping("/extension/order")
    public TableDataInfo extensionOrderList(SxscUserInfoExtensionModelReq sxscUserInfo)
    {
        startPage();
        List<SxscUserInfoExtensionOrderModelRes> list = sxscUserInfoService.extensionOrderList(sxscUserInfo);
        return getDataTable(list);
    }

}

package com.ruoyi.sxsc.person.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.person.domain.SxscUserCommissionOrder;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionOrderService;
import com.ruoyi.sxsc.seting.service.ISxscSetingParameterService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 佣金提现信息
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@RestController
@RequestMapping("/commission/order")
public class SxscUserCommissionOrderController extends BaseController
{
    @Autowired
    private ISxscUserCommissionOrderService sxscUserCommissionOrderService;

    @Autowired
    private ISysDictTypeService dictTypeService;


    /**
     * 查询佣金提现列表
     */
    @PreAuthorize("@ss.hasPermi('commission:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserCommissionOrder sxscUserCommissionOrder)
    {
        startPage();
        List<SxscUserCommissionOrder> list = sxscUserCommissionOrderService.selectSxscUserCommissionOrderList(sxscUserCommissionOrder);
        return getDataTable(list);
    }


    /**
     * 获取佣金提现详细信息
     */
    @PreAuthorize("@ss.hasPermi('commission:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscUserCommissionOrderService.selectSxscUserCommissionOrderById(id));
    }


    /**
     * 获取佣金提现方式
     */
    @GetMapping("/paymentType")
    public AjaxResult paymentType()
    {
        return AjaxResult.success(dictTypeService.selectDictDataByType("sxsc_commission_payment_type"));
    }

    /**
     * 新增佣金提现
     */
    @PreAuthorize("@ss.hasPermi('commission:order:add')")
    @Log(title = "佣金提现", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserCommissionOrder sxscUserCommissionOrder)
    {
        return sxscUserCommissionOrderService.insertSxscUserCommissionOrder(sxscUserCommissionOrder);
    }

    /**
     * 审核佣金提现
     */
    @PreAuthorize("@ss.hasPermi('commission:order:edit')")
    @Log(title = "佣金提现", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscUserCommissionOrder sxscUserCommissionOrder)
    {
        return sxscUserCommissionOrderService.updateSxscUserCommissionOrder(sxscUserCommissionOrder);
    }

}

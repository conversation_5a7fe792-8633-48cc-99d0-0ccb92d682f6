package com.ruoyi.sxsc.person.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 佣金提现额度对象
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserCommissionQuota extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 提现额度 */
    @Excel(name = "提现额度")
    private BigDecimal quota;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Long delFlag;

    /** 手机号 */
    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private SysUserMain sysUser;
}

package com.ruoyi.sxsc.dingdong.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 供应商地址码对照对象
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscDingdongAddressMapper extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 区域主键 */
    @TableId
    private Long regionId;

    /** 区域名称 */
    @Excel(name = "区域名称")
    private String regionName;

    /** 父级主键 */
    @Excel(name = "父级主键")
    private Long parentId;

    /** 级别 */
    @Excel(name = "级别")
    private Long level;

    /** 城市编码 */
    @Excel(name = "城市编码")
    private String cityCode;




}

package com.ruoyi.sxsc.dingdong.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Arrays;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import com.ruoyi.sxsc.dingdong.util.DingDongCommodityUtil;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.dingdong.mapper.SxscDingdongCommodityMappingMapper;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityMapping;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityMappingService;

/**
 * 叮咚供应链商品对应关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-13
 */
@Service
public class SxscDingdongCommodityMappingServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscDingdongCommodityMappingMapper, SxscDingdongCommodityMapping> implements ISxscDingdongCommodityMappingService
{

    @Autowired
    DingDongCommodityUtil dingDongCommodityUtil;

    @Autowired
    ISxscCommodityService iSxscCommodityService;

    @Autowired
    ISysConfigService iSysConfigService;

    /**
     * 查询叮咚供应链商品对应关系
     * 
     * @param commodityId 叮咚供应链商品对应关系主键
     * @return 叮咚供应链商品对应关系
     */
    @Override
    public SxscDingdongCommodityMapping selectSxscCommodityDingdongMappingByCommodityId(Long commodityId)
    {
        LambdaQueryWrapper<SxscDingdongCommodityMapping> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscDingdongCommodityMapping::getCommodityId,commodityId);
        wrapper.eq(SxscDingdongCommodityMapping::getDelFlag,0l);
        return getOne(wrapper);
    }

    /**
     * 查询叮咚供应链商品对应关系
     *
     * @param dingdongId 供应商主键
     * @return 叮咚供应链商品对应关系
     */
    @Override
    public SxscDingdongCommodityMapping selectSxscCommodityDingdongMappingByDingdongId(Long dingdongId){
        LambdaQueryWrapper<SxscDingdongCommodityMapping> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscDingdongCommodityMapping::getDingdongId,dingdongId);
        wrapper.eq(SxscDingdongCommodityMapping::getDelFlag,0l);
        return getOne(wrapper);
    }

    /**
     * 查询叮咚供应链商品对应关系列表
     * 
     * @param sxscDingdongCommodityMapping 叮咚供应链商品对应关系
     * @return 叮咚供应链商品对应关系
     */
    @Override
    public List<SxscDingdongCommodityMapping> selectSxscCommodityDingdongMappingList(SxscDingdongCommodityMapping sxscDingdongCommodityMapping)
    {
        LambdaQueryWrapper<SxscDingdongCommodityMapping> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscDingdongCommodityMapping.getDingdongId()), SxscDingdongCommodityMapping::getDingdongId, sxscDingdongCommodityMapping.getDingdongId());

        wrapper.eq(StringUtils.isNotNull(sxscDingdongCommodityMapping.getCommodityId()), SxscDingdongCommodityMapping::getCommodityId, sxscDingdongCommodityMapping.getCommodityId());

        wrapper.eq( SxscDingdongCommodityMapping::getDelFlag, 0);

        return list(wrapper);
    }

    /**
     * 新增叮咚供应链商品对应关系
     * 
     * @param sxscDingdongCommodityMapping 叮咚供应链商品对应关系
     * @return 结果
     */
    @Override
    public int insertSxscCommodityDingdongMapping(SxscDingdongCommodityMapping sxscDingdongCommodityMapping)
    {
        sxscDingdongCommodityMapping.setDelFlag(0l);
        sxscDingdongCommodityMapping.setCreateBy(SecurityUtils.getUsername());
        sxscDingdongCommodityMapping.setCreateTime(DateUtils.getNowDate());
        return save(sxscDingdongCommodityMapping)?1:0;
    }

    /**
     * 修改叮咚供应链商品对应关系
     * 
     * @param sxscDingdongCommodityMapping 叮咚供应链商品对应关系
     * @return 结果
     */
    @Override
    public int updateSxscCommodityDingdongMappingByDingdongId(SxscDingdongCommodityMapping sxscDingdongCommodityMapping)
    {
        LambdaUpdateWrapper<SxscDingdongCommodityMapping> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscDingdongCommodityMapping::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscDingdongCommodityMapping::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.set(SxscDingdongCommodityMapping::getInventoryJson,sxscDingdongCommodityMapping.getInventoryJson());
        updateWrapper.eq(SxscDingdongCommodityMapping::getDingdongId,sxscDingdongCommodityMapping.getDingdongId());
        updateWrapper.eq(SxscDingdongCommodityMapping::getDelFlag,0);
        return update(updateWrapper)?1:0;
    }

    /**
     * 供应商通知商品属性更新
     *
     * @param dingdongId
     * @return 结果
     */
    @Override
    public void notifyNotice(Long dingdongId){
        String commodityJson=dingDongCommodityUtil.goodsDetail(dingdongId.intValue());
        String userId=iSysConfigService.selectConfigByKey("sxsc:dingdong:commodity:userid");
        SxscDingdongCommodityMapping commodityMapping=selectSxscCommodityDingdongMappingByDingdongId(dingdongId);
        if(StringUtils.isNotNull(commodityMapping)){
            LambdaUpdateWrapper<SxscDingdongCommodityMapping> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(SxscDingdongCommodityMapping::getDingdongId,dingdongId);
            updateWrapper.eq(SxscDingdongCommodityMapping::getCommodityId,commodityMapping.getCommodityId());
            updateWrapper.set(SxscDingdongCommodityMapping::getDelFlag,1);
            update(updateWrapper);
            SxscCommodity sxscCommodity=iSxscCommodityService.getById(commodityMapping.getCommodityId());
            if(StringUtils.isNotNull(sxscCommodity)){
                dingDongCommodityUtil.setCommodity(commodityJson,userId,sxscCommodity.getRetailAmount(),sxscCommodity.getStatus(),sxscCommodity.getType(),sxscCommodity.getPlateId(),sxscCommodity.getSortNumber(),sxscCommodity.getId());
            }else{
                dingDongCommodityUtil.setCommodity(commodityJson,userId,null,0l,null,null,null,null);
            }
        }else{
            dingDongCommodityUtil.setCommodity(commodityJson,userId,null,0l,null,null,null,null);
        }
    }

    /**
     * 系统主动发起商品属性更新
     *
     * @param commodityId  系统商品主键
     * @return 结果
     */
    @Override
    public AjaxResult proactivelyInitiateNotice(Long commodityId){
        SxscDingdongCommodityMapping commodityMapping=selectSxscCommodityDingdongMappingByCommodityId(commodityId);
        if(StringUtils.isNotNull(commodityMapping)){
            String commodityJson=dingDongCommodityUtil.goodsDetail(commodityMapping.getDingdongId().intValue());
            String userId=iSysConfigService.selectConfigByKey("sxsc:dingdong:commodity:userid");
            LambdaUpdateWrapper<SxscDingdongCommodityMapping> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(SxscDingdongCommodityMapping::getDingdongId,commodityMapping.getDingdongId());
            updateWrapper.eq(SxscDingdongCommodityMapping::getCommodityId,commodityMapping.getCommodityId());
            updateWrapper.set(SxscDingdongCommodityMapping::getDelFlag,1);
            update(updateWrapper);
            SxscCommodity sxscCommodity=iSxscCommodityService.getById(commodityMapping.getCommodityId());
            dingDongCommodityUtil.setCommodity(commodityJson,userId,sxscCommodity.getRetailAmount(),sxscCommodity.getStatus(),sxscCommodity.getType(),sxscCommodity.getPlateId(),sxscCommodity.getSortNumber(),sxscCommodity.getId());
            return AjaxResult.success();
        }
        return AjaxResult.error("商品数据有误，无法同步");
    }
}

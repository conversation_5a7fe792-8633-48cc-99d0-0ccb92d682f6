package com.ruoyi.sxsc.dingdong.service;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityOrderMapper;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 供应商订单信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ISxscDingdongCommodityOrderMapperService extends IService<SxscDingdongCommodityOrderMapper>
{
    /**
     * 查询供应商订单信息
     * 
     * @param dingdongOrderId 供应商订单信息主键
     * @return 供应商订单信息
     */
    public SxscDingdongCommodityOrderMapper selectSxscDingdongCommodityOrderMapperByDingdongOrderId(String dingdongOrderId);

    /**
     * 查询供应商订单信息列表
     * 
     * @param sxscDingdongCommodityOrderMapper 供应商订单信息
     * @return 供应商订单信息集合
     */
    public List<SxscDingdongCommodityOrderMapper> selectSxscDingdongCommodityOrderMapperList(SxscDingdongCommodityOrderMapper sxscDingdongCommodityOrderMapper);

    /**
     * 新增供应商订单信息
     *
     * @param createRes 调用接口返回信息
     * @param createParam 创建参数
     * @param orderId 系统订单主键
     * @return 结果
     */
    public SxscDingdongCommodityOrderMapper insertSxscDingdongCommodityOrderMapper(JSONObject createRes,JSONObject createParam,String orderId);


    /**
     * 修改供应商订单信息
     * 
     * @param sxscDingdongCommodityOrderMapper 供应商订单信息
     * @return 结果
     */
    public int updateSxscDingdongCommodityOrderMapper(SxscDingdongCommodityOrderMapper sxscDingdongCommodityOrderMapper);


}

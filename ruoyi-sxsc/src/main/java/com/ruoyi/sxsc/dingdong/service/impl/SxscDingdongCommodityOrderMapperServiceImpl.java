package com.ruoyi.sxsc.dingdong.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Arrays;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.dingdong.mapper.SxscDingdongCommodityOrderMapperMapper;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityOrderMapper;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityOrderMapperService;

/**
 * 供应商订单信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class SxscDingdongCommodityOrderMapperServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscDingdongCommodityOrderMapperMapper,SxscDingdongCommodityOrderMapper> implements ISxscDingdongCommodityOrderMapperService
{

    /**
     * 查询供应商订单信息
     * 
     * @param dingdongOrderId 供应商订单信息主键
     * @return 供应商订单信息
     */
    @Override
    public SxscDingdongCommodityOrderMapper selectSxscDingdongCommodityOrderMapperByDingdongOrderId(String dingdongOrderId)
    {
        return getById(dingdongOrderId);
    }

    /**
     * 查询供应商订单信息列表
     * 
     * @param sxscDingdongCommodityOrderMapper 供应商订单信息
     * @return 供应商订单信息
     */
    @Override
    public List<SxscDingdongCommodityOrderMapper> selectSxscDingdongCommodityOrderMapperList(SxscDingdongCommodityOrderMapper sxscDingdongCommodityOrderMapper)
    {
        LambdaQueryWrapper<SxscDingdongCommodityOrderMapper> wrapper=new LambdaQueryWrapper();


        wrapper.eq(StringUtils.isNotNull(sxscDingdongCommodityOrderMapper.getDingdongOrderId()),SxscDingdongCommodityOrderMapper::getDingdongOrderId,sxscDingdongCommodityOrderMapper.getDingdongOrderId());


        wrapper.eq(StringUtils.isNotNull(sxscDingdongCommodityOrderMapper.getCommodityOrderId()),SxscDingdongCommodityOrderMapper::getCommodityOrderId,sxscDingdongCommodityOrderMapper.getCommodityOrderId());


       return list(wrapper);
    }

    /**
     * 新增供应商订单信息
     *
     * @param createRes 调用接口返回信息
     * @param createParam 创建参数
     * @param orderId 系统订单主键
     * @return 结果
     */
    @Override
    public SxscDingdongCommodityOrderMapper insertSxscDingdongCommodityOrderMapper(JSONObject createRes,JSONObject createParam,String orderId)
    {
        SxscDingdongCommodityOrderMapper sxscDingdongCommodityOrderMapper=new SxscDingdongCommodityOrderMapper();
        sxscDingdongCommodityOrderMapper.setCreateJson(createRes.toJSONString());
        sxscDingdongCommodityOrderMapper.setCreateParam(createParam.toJSONString());
        sxscDingdongCommodityOrderMapper.setCommodityOrderId(orderId);
        sxscDingdongCommodityOrderMapper.setCreateBy(SecurityUtils.getUsername());
        sxscDingdongCommodityOrderMapper.setCreateTime(DateUtils.getNowDate());
        if(createRes.getInteger("status")==200){
            JSONObject jsonObject=createRes.getJSONObject("content");
            sxscDingdongCommodityOrderMapper.setDingdongOrderId(jsonObject.getString("orderSn"));
            sxscDingdongCommodityOrderMapper.setTotalGoodsPrice(new BigDecimal(jsonObject.getInteger("totalGoodsPrice")));
            sxscDingdongCommodityOrderMapper.setTotalPrice(new BigDecimal(jsonObject.getInteger("totalPrice")));
            sxscDingdongCommodityOrderMapper.setTotalShipPrice(new BigDecimal(jsonObject.getInteger("totalShipPrice")));
            sxscDingdongCommodityOrderMapper.setTotalWlPrice(new BigDecimal(jsonObject.getInteger("totalWlPrice")));
        }else{
            sxscDingdongCommodityOrderMapper.setDingdongOrderId(IdUtils.fastSimpleUUID());
        }
        save(sxscDingdongCommodityOrderMapper);
        return sxscDingdongCommodityOrderMapper;
    }

    /**
     * 修改供应商订单信息
     * 
     * @param sxscDingdongCommodityOrderMapper 供应商订单信息
     * @return 结果
     */
    @Override
    public int updateSxscDingdongCommodityOrderMapper(SxscDingdongCommodityOrderMapper sxscDingdongCommodityOrderMapper)
    {
        sxscDingdongCommodityOrderMapper.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscDingdongCommodityOrderMapper)?1:0;
    }



}

package com.ruoyi.sxsc.dingdong.service.impl;

import java.util.List;
import java.util.Arrays;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityDelivery;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityDeliveryService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.dingdong.util.DingDongCommodityUtil;
import com.ruoyi.sxsc.person.domain.SxscUserAddress;
import com.ruoyi.sxsc.person.service.ISxscAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.dingdong.mapper.SxscDingdongCommodityOrderNotifyMapper;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityOrderNotify;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityOrderNotifyService;

/**
 * 供应商订单回调信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class SxscDingdongCommodityOrderNotifyServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscDingdongCommodityOrderNotifyMapper,SxscDingdongCommodityOrderNotify> implements ISxscDingdongCommodityOrderNotifyService
{


    @Autowired
    ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    DingDongCommodityUtil dingDongCommodityUtil;

    @Autowired
    ISxscCommodityDeliveryService iSxscCommodityDeliveryService;

    @Autowired
    ISxscAddressService iSxscAddressService;
    /**
     * 查询供应商订单回调信息
     * 
     * @param id 供应商订单回调信息主键
     * @return 供应商订单回调信息
     */
    @Override
    public SxscDingdongCommodityOrderNotify selectSxscDingdongCommodityOrderNotifyById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询供应商订单回调信息列表
     * 
     * @param sxscDingdongCommodityOrderNotify 供应商订单回调信息
     * @return 供应商订单回调信息
     */
    @Override
    public List<SxscDingdongCommodityOrderNotify> selectSxscDingdongCommodityOrderNotifyList(SxscDingdongCommodityOrderNotify sxscDingdongCommodityOrderNotify)
    {
        LambdaQueryWrapper<SxscDingdongCommodityOrderNotify> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscDingdongCommodityOrderNotify.getId()),SxscDingdongCommodityOrderNotify::getId,sxscDingdongCommodityOrderNotify.getId());

        wrapper.eq(StringUtils.isNotNull(sxscDingdongCommodityOrderNotify.getOrderSn()),SxscDingdongCommodityOrderNotify::getOrderSn,sxscDingdongCommodityOrderNotify.getOrderSn());

        wrapper.eq(StringUtils.isNotNull(sxscDingdongCommodityOrderNotify.getCommodityOrderId()),SxscDingdongCommodityOrderNotify::getCommodityOrderId,sxscDingdongCommodityOrderNotify.getCommodityOrderId());

        return list(wrapper);
    }


    /**
     * 修改供应商订单回调信息
     * 
     * @param sxscDingdongCommodityOrderNotify 供应商订单回调信息
     * @return 结果
     */
    @Override
    public int updateSxscDingdongCommodityOrderNotify(SxscDingdongCommodityOrderNotify sxscDingdongCommodityOrderNotify)
    {
        sxscDingdongCommodityOrderNotify.setUpdateBy(SecurityUtils.getUsername());
        sxscDingdongCommodityOrderNotify.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscDingdongCommodityOrderNotify)?1:0;
    }

    /**
     * 处理供应商订单回调信息
     *
     * @param jsonObject 供应商订单回调信息
     * @return 结果
     */
    @Override
    public void notifyNotice(JSONObject jsonObject){
        SxscDingdongCommodityOrderNotify orderNotify=new SxscDingdongCommodityOrderNotify();
        orderNotify.setCreateTime(DateUtils.getNowDate());
        orderNotify.setNotifyJson(jsonObject.toJSONString());
        JSONObject content=jsonObject.getJSONObject("content");
        JSONObject data=content.getJSONObject("data");
        orderNotify.setOrderSn(data.getString("order_sn"));
        SxscCommodityOrder sxscCommodityOrder=iSxscCommodityOrderService.selectSxscCommodityOrderByDingdongSn(orderNotify.getOrderSn());
        if(StringUtils.isNotNull(sxscCommodityOrder)){
            orderNotify.setCommodityOrderId(sxscCommodityOrder.getId());
            String orderSnDetail=dingDongCommodityUtil.orderSnDetail(orderNotify.getOrderSn(),orderNotify.getCommodityOrderId());
            orderNotify.setProactivelyJson(orderSnDetail);
            JSONObject orderSnJson=JSONObject.parseObject(orderSnDetail);
            if(orderSnJson.getInteger("status")==200){
                JSONObject orderSnJsonContent=orderSnJson.getJSONArray("content").getJSONObject(0);
                if(StringUtils.isNotNull(orderSnJsonContent)){
                    Integer status=orderSnJsonContent.getInteger("status");
                    //订单状态：0待发货，1已发货，3已配货，4已完成，5已取消，6待支付，9已关闭（已退款）
                    if(status==1&&sxscCommodityOrder.getStatus()==2){
                        sxscCommodityOrder.setStatus(3l);
                        sxscCommodityOrder.setDeliveryDateTime(DateUtils.getNowDate());
                    }

                    if(status==5){
                        sxscCommodityOrder.setStatus(7l);
                        sxscCommodityOrder.setOverDateTime(DateUtils.getNowDate());
                    }
                    if(status==9&&sxscCommodityOrder.getStatus()!=6){//还需处理其他业务
                        sxscCommodityOrder.setStatus(6l);
                        sxscCommodityOrder.setOverDateTime(DateUtils.getNowDate());
                    }
                    //物流信息只要不为空，每次都更新
                    if(StringUtils.isNotEmpty(orderSnJsonContent.getString("shipName"))){
                        SxscCommodityDelivery sxscCommodityDelivery=iSxscCommodityDeliveryService.selectSxscCommodityDeliveryByOrderId(sxscCommodityOrder.getId(),1l);
                        SxscCommodityDelivery delivery=new SxscCommodityDelivery();
                        if(StringUtils.isNotNull(sxscCommodityDelivery)){
                            delivery.setId(sxscCommodityDelivery.getId());
                            delivery.setUpdateTime(DateUtils.getNowDate());
                        }else{
                            delivery.setCreateTime(DateUtils.getNowDate());
                        }
                        delivery.setCommodityOrderId(sxscCommodityOrder.getId());
                        delivery.setUserId(sxscCommodityOrder.getSellerUserId());
                        delivery.setType(1l);
                        SxscUserAddress sxscAddress=iSxscAddressService.selectSxscAddressById(sxscCommodityOrder.getAddressId());
                        delivery.setDeliveryPhone(sxscAddress.getPhone());
                        delivery.setDeliveryName(orderSnJsonContent.getString("shipName"));
                        delivery.setDeliveryNumber(orderSnJsonContent.getString("shipSn"));
                        iSxscCommodityDeliveryService.saveOrUpdate(delivery);
                    }
                    iSxscCommodityOrderService.updateById(sxscCommodityOrder);
                }
            }
        }
        save(orderNotify);
    }
}

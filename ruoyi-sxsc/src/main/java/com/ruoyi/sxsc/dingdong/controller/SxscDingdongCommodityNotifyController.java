package com.ruoyi.sxsc.dingdong.controller;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityMappingService;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityOrderNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 商品分类
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@RestController
@RequestMapping("/dingdong/notify")
public class SxscDingdongCommodityNotifyController extends BaseController
{
    @Autowired
    private ISxscDingdongCommodityMappingService iSxscDingdongCommodityMappingService;

    @Autowired
    private ISxscDingdongCommodityOrderNotifyService iSxscDingdongCommodityOrderNotifyService;

    /**
     * 商品信息回调
     */
    @PostMapping("/goods")
    @Log(title = "供应链商品信息回调", businessType = BusinessType.UPDATE)
    public Map<String,Object> goods(@RequestBody Map<String,Long> map)
    {
        if(StringUtils.isNotNull(map.get("goodsId"))){
            iSxscDingdongCommodityMappingService.notifyNotice(map.get("goodsId"));
        }
        Map<String,Object> resMap=new HashMap<>();
        resMap.put("status",200);
        resMap.put("msg","OK");
        return resMap;
    }

    /**
     * 订单信息回调
     */
    @PostMapping(value = "/order")
    @Log(title = "供应链订单信息回调", businessType = BusinessType.UPDATE)
    public Map<String,Object> order(@RequestBody JSONObject jsonObject)
    {
        iSxscDingdongCommodityOrderNotifyService.notifyNotice(jsonObject);
        Map<String,Object> resMap=new HashMap<>();
        resMap.put("status",200);
        resMap.put("msg","OK");
        Map<String,Object> contentMap=new HashMap<>();
        contentMap.put("msg","更新成功");
        resMap.put("content",contentMap);
        return resMap;
    }


}

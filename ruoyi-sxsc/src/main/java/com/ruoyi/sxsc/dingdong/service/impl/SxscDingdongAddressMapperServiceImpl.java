package com.ruoyi.sxsc.dingdong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.dingdong.mapper.SxscDingdongAddressMapperMapper;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongAddressMapper;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongAddressMapperService;

/**
 * 供应商地址码对照Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class SxscDingdongAddressMapperServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscDingdongAddressMapperMapper, SxscDingdongAddressMapper> implements ISxscDingdongAddressMapperService
{

    /**
     * 查询供应商地址码对照
     * 
     * @param code 城市编码
     * @return 供应商地址码对照
     */
    @Override
    public Long selectSxscAddressDingdongMapperByCode(String code)
    {
        if(code.equals("0")){
            return 0l;
        }
        LambdaQueryWrapper<SxscDingdongAddressMapper> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscDingdongAddressMapper::getCityCode,code);
        SxscDingdongAddressMapper sxscDingdongAddressMapper =getOne(wrapper);
        if(StringUtils.isNull(sxscDingdongAddressMapper)){
            throw new ServiceException("地址信息有误，无法下单");
        }
        return sxscDingdongAddressMapper.getRegionId();
    }


}

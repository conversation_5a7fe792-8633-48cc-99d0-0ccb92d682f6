package com.ruoyi.sxsc.dingdong.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 供应商订单信息对象
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscDingdongCommodityOrderMapper extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 供应商订单主键 */
    @TableId(type = IdType.INPUT)
    private String dingdongOrderId;

    /** 系统订单主键 */
    @Excel(name = "系统订单主键")
    private String commodityOrderId;

    /** 创建订单传入参数 */
    @Excel(name = "创建订单传入参数")
    private String createParam;

    /** 创建订单返回内容 */
    @Excel(name = "创建订单返回内容")
    private String createJson;

    /** 支付订单返回内容 */
    @Excel(name = "支付订单返回内容")
    private String payJson;

    /** 订单总价 */
    @Excel(name = "订单总价")
    private BigDecimal totalPrice;

    /** 商品总金额 */
    @Excel(name = "商品总金额")
    private BigDecimal totalGoodsPrice;

    /** 运费总金额 */
    @Excel(name = "运费总金额")
    private BigDecimal totalShipPrice;

    /** 物流到付总费用 */
    @Excel(name = "物流到付总费用")
    private BigDecimal totalWlPrice;


}

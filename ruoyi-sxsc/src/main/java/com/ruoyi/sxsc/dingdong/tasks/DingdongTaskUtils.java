package com.ruoyi.sxsc.dingdong.tasks;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityDelivery;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.domain.SxscCommoditySpecifications;
import com.ruoyi.sxsc.commodity.model.SxscCommodityOrderExcelModel;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityDeliveryService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import com.ruoyi.sxsc.commodity.service.ISxscCommoditySpecificationsService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeShareService;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityMapping;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityMappingService;
import com.ruoyi.sxsc.dingdong.util.DingDongCommodityUtil;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.sxsc.utils.EmailSender;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 系统任务调度
 */
@Component("dingdongTasks")
public class DingdongTaskUtils {
    @Autowired
    DingDongCommodityUtil dingDongCommodityUtil;
    @Autowired
    RedisCache redisCache;
    @Autowired
    ISxscDingdongCommodityMappingService iSxscDingdongCommodityMappingService;
    @Autowired
    private ISysConfigService iSysConfigService;

    /**
     *  系统获取叮咚所有在售商品
     */
    public void getAllCommodity(){
        String commodityJson=dingDongCommodityUtil.allGoods();
        JSONObject jsonObject=JSONObject.parseObject(commodityJson);
        if(jsonObject.getInteger("status")==200){
            JSONObject content=jsonObject.getJSONObject("content");
            JSONArray gift=content.getJSONArray("gift");
            JSONArray currency=content.getJSONArray("currency");
            JSONArray all=new JSONArray();
            all.addAll(gift);
            all.addAll(currency);
            redisCache.setCacheObject("dingDong:goodsIds",all.toJSONString(),24, TimeUnit.HOURS);
        }else{
            throw new ServiceException(jsonObject.getString("msg"));
        }
    }

    /**
     *  系统获取叮咚所有在售商品明细
     */
    public void getCommodityDetail(){
        JSONArray jsonArray=JSONObject.parseArray(redisCache.getCacheObject("dingDong:goodsIds"));
        for(int i=0;i<jsonArray.size();i++){
            iSxscDingdongCommodityMappingService.notifyNotice(Long.valueOf(jsonArray.getInteger(i)));
        }

    }
    /**
     *  系统设置供应商回调地址
     *
     */
    public void setNotifyUrl(String type){
        String url=iSysConfigService.selectConfigByKey("sxsc:dingdong:commodity:url:"+type);
        dingDongCommodityUtil.notifyUrl(type,url);
    }


    /**
     *  系统获取叮咚所有在售商品库存
     */
    public void getCommodityDetailInventory(){
        List<SxscDingdongCommodityMapping> commodityMappings=iSxscDingdongCommodityMappingService.selectSxscCommodityDingdongMappingList(new SxscDingdongCommodityMapping());
        for(SxscDingdongCommodityMapping commodityMapping:commodityMappings){
           dingDongCommodityUtil.updateCommodityStock(commodityMapping);
        }
    }


}

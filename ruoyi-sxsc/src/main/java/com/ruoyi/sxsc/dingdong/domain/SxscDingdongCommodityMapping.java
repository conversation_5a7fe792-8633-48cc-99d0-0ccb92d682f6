package com.ruoyi.sxsc.dingdong.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 叮咚供应链商品对应关系对象
 * 
 * <AUTHOR>
 * @date 2025-04-13
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscDingdongCommodityMapping extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 叮咚供应商商品主键 */
    private Long dingdongId;

    /** 富兴商城商品主键 */
    @Excel(name = "富兴商城商品主键")
    private Long commodityId;

    /** 供应商商品详情对象 */
    @Excel(name = "供应商商品详情对象")
    private String dingdongJsonDetail;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Long delFlag;

    /** 供应商商品库存详情 */
    @Excel(name = "供应商商品库存详情")
    private String inventoryJson;
}

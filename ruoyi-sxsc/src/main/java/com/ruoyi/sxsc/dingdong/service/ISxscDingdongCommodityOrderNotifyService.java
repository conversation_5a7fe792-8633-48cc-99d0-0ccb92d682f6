package com.ruoyi.sxsc.dingdong.service;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityOrderNotify;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 供应商订单回调信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ISxscDingdongCommodityOrderNotifyService extends IService<SxscDingdongCommodityOrderNotify>
{
    /**
     * 查询供应商订单回调信息
     * 
     * @param id 供应商订单回调信息主键
     * @return 供应商订单回调信息
     */
    public SxscDingdongCommodityOrderNotify selectSxscDingdongCommodityOrderNotifyById(Long id);

    /**
     * 查询供应商订单回调信息列表
     * 
     * @param sxscDingdongCommodityOrderNotify 供应商订单回调信息
     * @return 供应商订单回调信息集合
     */
    public List<SxscDingdongCommodityOrderNotify> selectSxscDingdongCommodityOrderNotifyList(SxscDingdongCommodityOrderNotify sxscDingdongCommodityOrderNotify);


    /**
     * 修改供应商订单回调信息
     * 
     * @param sxscDingdongCommodityOrderNotify 供应商订单回调信息
     * @return 结果
     */
    public int updateSxscDingdongCommodityOrderNotify(SxscDingdongCommodityOrderNotify sxscDingdongCommodityOrderNotify);

    /**
     * 处理供应商订单回调信息
     *
     * @param jsonObject 供应商订单回调信息
     * @return 结果
     */
    public void notifyNotice(JSONObject jsonObject);
}

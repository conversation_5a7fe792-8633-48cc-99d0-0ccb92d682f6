package com.ruoyi.sxsc.dingdong.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityMapping;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 叮咚供应链商品对应关系Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-13
 */
public interface ISxscDingdongCommodityMappingService extends IService<SxscDingdongCommodityMapping>
{
    /**
     * 查询叮咚供应链商品对应关系
     * 
     * @param commodityId 系统商品主键
     * @return 叮咚供应链商品对应关系
     */
    public SxscDingdongCommodityMapping selectSxscCommodityDingdongMappingByCommodityId(Long commodityId);

    /**
     * 查询叮咚供应链商品对应关系
     *
     * @param dingdongId 供应商主键
     * @return 叮咚供应链商品对应关系
     */
    public SxscDingdongCommodityMapping selectSxscCommodityDingdongMappingByDingdongId(Long dingdongId);

    /**
     * 查询叮咚供应链商品对应关系列表
     * 
     * @param sxscDingdongCommodityMapping 叮咚供应链商品对应关系
     * @return 叮咚供应链商品对应关系集合
     */
    public List<SxscDingdongCommodityMapping> selectSxscCommodityDingdongMappingList(SxscDingdongCommodityMapping sxscDingdongCommodityMapping);

    /**
     * 新增叮咚供应链商品对应关系
     * 
     * @param sxscDingdongCommodityMapping 叮咚供应链商品对应关系
     * @return 结果
     */
    public int insertSxscCommodityDingdongMapping(SxscDingdongCommodityMapping sxscDingdongCommodityMapping);

    /**
     * 修改叮咚供应链商品对应关系
     * 
     * @param sxscDingdongCommodityMapping 叮咚供应链商品对应关系
     * @return 结果
     */
    public int updateSxscCommodityDingdongMappingByDingdongId(SxscDingdongCommodityMapping sxscDingdongCommodityMapping);

    /**
     * 供应商通知商品属性更新
     * 
     * @param dingdongId
     * @return 结果
     */
    public void notifyNotice(Long dingdongId);

    /**
     * 系统主动发起商品属性更新
     *
     * @param commodityId  系统商品主键
     * @return 结果
     */
    public AjaxResult proactivelyInitiateNotice(Long commodityId);


}

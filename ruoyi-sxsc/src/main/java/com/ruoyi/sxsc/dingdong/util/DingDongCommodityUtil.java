package com.ruoyi.sxsc.dingdong.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import com.ruoyi.sxsc.commodity.service.ISxscCommoditySpecificationsService;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityMapping;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.domain.SxscCommoditySpecifications;
import com.ruoyi.sxsc.dingdong.domain.SxscDingdongCommodityOrderMapper;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityMappingService;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongAddressMapperService;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityOrderMapperService;
import com.ruoyi.sxsc.person.domain.SxscUserAddress;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
public class DingDongCommodityUtil {

    private final static String baseUrl="https://openapi.7dingdong.com";
    private final static String appKey="14e07f4e-1e5a-11f0-bc55-080027db4a40";
    private final static String appSecret="3ee08e12069af52c6546d6bed5d4a34f";

    @Autowired
    RedisCache redisCache;

    @Autowired
    ISxscDingdongCommodityMappingService iSxscDingdongCommodityMappingService;

    @Autowired
    ISxscDingdongAddressMapperService iSxscDingdongAddressMapperService;

    @Autowired
    ISxscCommodityService iSxscCommodityService;

    @Autowired
    private ISxscCommoditySpecificationsService iSxscCommoditySpecificationsService;

    @Autowired
    private ISxscDingdongCommodityOrderMapperService iSxscDingdongCommodityOrderMapperService;

    //67EE06A00A912FA7B2D7897E58B73313

    private  String getToken(){
        String token="";
        if(redisCache.hasKey("dingDong:token")){
            return redisCache.getCacheObject("dingDong:token");
        }
        OkHttpClient client = new OkHttpClient().newBuilder().build();
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, "");
        String timestamp= String.valueOf(DateUtils.getNowDate().getTime()/1000);
        String nonce= IdUtils.fastSimpleUUID();
        String md5="appKey"+appKey+"timestamp"+timestamp+"token"+"nonce"+nonce+"appSecret"+appSecret;
        String signature= Md5Utils.hash(md5).toUpperCase();
        Request request = new Request.Builder()
                .url(baseUrl+"/api/v1/user/get_token")
                .method("POST", body)
                .addHeader("appKey", appKey)
                .addHeader("token", token)
                .addHeader("nonce", nonce)
                .addHeader("timestamp", timestamp)
                .addHeader("signature", signature)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if(response.isSuccessful()){
                JSONObject jsonObject=JSONObject.parseObject(response.body().string());
                if(jsonObject.getInteger("status")==200){
                    JSONObject content=jsonObject.getJSONObject("content");
                    redisCache.setCacheObject("dingDong:token",content.getString("token"));
                    return content.getString("token");
                }
            }
            return null;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 设置回调地址
     * @param type 通知类型：1、商品通知，2、订单通知
     * @param url 通知URL
     */
    public String notifyUrl(String type,String url){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        // 使用 Gson 构建 JSON 对象
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type",type);
        jsonObject.put("url", url);
        RequestBody body = RequestBody.create(mediaType, jsonObject.toString());
        Request request = getRequest("/api/v1/user/notify_url",body);
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取全部在售商品
     */
    public String  allGoods(){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = getRequest("/api/v1/goods/all_goods",body);
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取商品详情
     * @param goodsId 商品主键
     */
    public String goodsDetail(Integer goodsId){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\r\n\t\"goodsId\": "+goodsId+"\r\n}");
        Request request = getRequest("/api/v1/goods/goods_detail",body);
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取订单详情
     * @param orderSn 供应商订单主键
     * @param commodityOrderId 系统订单主键
     */
    public String orderSnDetail(String orderSn,String commodityOrderId){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        // 使用 Gson 构建 JSON 对象
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderSn",orderSn);
        jsonObject.put("orderNo", commodityOrderId.substring(0,29));
        RequestBody body = RequestBody.create(mediaType, jsonObject.toString());
        Request request = getRequest("/api/v1/order/detail",body);
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 订单预览
     * @param goodsId 商品id
     * @param specId 商品规格id，不可重复
     * @param num 商品数量
     * @param price 商品价格（需扣除储备金的价格），在商品详情接口获取，主要用于校验，当用户提交的价格与商品售价不符时，会返回错误信息
     * @param receiver 收件人姓名
     * @param phone 收件人电话
     * @param province 省id；直辖市时：直辖市id
     * @param city 市id；直辖市时：区id
     * @param area 区id；直辖市时：0
     * @param detail 详细地址：XX路XX号XX小区XX楼XX单元XX室
     */
    public String orderPreview(Long goodsId, Long specId, Long num, BigDecimal price, String receiver, String phone, String province, String city, String
                               area, String detail){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        // 创建订单对象
        JSONObject order = new JSONObject();
        order.put("checkType", 1);

        // 创建商品数组
        JSONArray goodsArray = new JSONArray();

        // 创建第一个商品对象
        JSONObject goods1 = new JSONObject();
        goods1.put("goodsId", goodsId);
        goods1.put("specId", specId);
        goods1.put("num", num);
        goods1.put("yfMode", 1);
        goods1.put("price", price.multiply(new BigDecimal("100")));
        goodsArray.add(goods1);
        // 将商品数组添加到订单对象中
        order.put("goods", goodsArray);

        // 创建收货人对象
        JSONObject consignee = new JSONObject();
        consignee.put("receiver", receiver);
        consignee.put("phone", phone);

        // 创建地址对象
        JSONObject address = new JSONObject();
        address.put("province", province);
        address.put("city", city);
        address.put("area", area);
        address.put("detail", detail);

        // 将地址对象添加到收货人对象中
        consignee.put("address", address);

        // 将收货人对象添加到订单对象中
        order.put("consignee", consignee);

        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, order.toString());
        Request request = getRequest("/api/v1/order/order_preview",body);
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public JSONObject orderCreate(SxscCommodityOrder commodityOrder, SxscUserAddress sxscAddress, SxscCommoditySpecifications specifications){

        SxscDingdongCommodityMapping dingdongMapping= iSxscDingdongCommodityMappingService.selectSxscCommodityDingdongMappingByCommodityId(commodityOrder.getCommodityId());
        if(StringUtils.isNull(dingdongMapping)){
            throw new ServiceException("商品不存在，无法下单");
        }
        String province=sxscAddress.getProvinceCode();
        String city=sxscAddress.getCityCode();
        String county=sxscAddress.getCountyCode();
        String address=sxscAddress.getAddress();
        String name=sxscAddress.getName();
        String phone=sxscAddress.getPhone();
        Long provinceId=0l;
        Long cityId=0l;
        Long countyId=0l;
        if(sxscAddress.getProvinceCode().equals("110000")||sxscAddress.getProvinceCode().equals("120000")||sxscAddress.getProvinceCode().equals("310000")||sxscAddress.getProvinceCode().equals("500000")){
            province=sxscAddress.getProvinceCode();
            city=sxscAddress.getCountyCode();
            county="0";
        }else if(sxscAddress.getCityCode().equals("419000")){
            province=sxscAddress.getProvinceCode();
            city=sxscAddress.getCountyCode();
            county="0";
        }
        provinceId= iSxscDingdongAddressMapperService.selectSxscAddressDingdongMapperByCode(province);
        cityId= iSxscDingdongAddressMapperService.selectSxscAddressDingdongMapperByCode(city);
        countyId= iSxscDingdongAddressMapperService.selectSxscAddressDingdongMapperByCode(county);
        SxscDingdongCommodityOrderMapper sxscDingdongCommodityOrderMapper= orderCreate(commodityOrder.getId(),dingdongMapping.getDingdongId(),specifications.getSupplierMapperId(),commodityOrder.getNumber(),specifications.getSupplyAmount(),name,phone,provinceId,cityId,countyId,address);
        JSONObject jsonObject=JSONObject.parseObject(sxscDingdongCommodityOrderMapper.getCreateJson());
        if(jsonObject.getInteger("status")==200){
            return jsonObject.getJSONObject("content");
        }else{
            throw new ServiceException(jsonObject.getString("msg"));
        }
    }

    /**
     * 订单创建
     * @param orderNo 业务系统订单号
     * @param goodsId 商品id
     * @param specId 商品规格id，不可重复
     * @param num 商品数量
     * @param price (目前传入的是规格单价)商品价格（需扣除储备金的价格），在商品详情接口获取，主要用于校验，当用户提交的价格与商品售价不符时，会返回错误信息
     * @param receiver 收件人姓名
     * @param phone 收件人电话
     * @param province 省id；直辖市时：直辖市id
     * @param city 市id；直辖市时：区id
     * @param area 区id；直辖市时：0
     * @param detail 详细地址：XX路XX号XX小区XX楼XX单元XX室
     */
    public SxscDingdongCommodityOrderMapper orderCreate(String orderNo, Long goodsId, Long specId,Long num, BigDecimal price, String receiver, String phone, Long province, Long city, Long
            area, String detail){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        // 创建订单对象
        JSONObject order = new JSONObject();
        order.put("checkType", 1);
        order.put("orderNo", orderNo.substring(0,29));

        // 创建商品数组
        JSONArray goodsArray = new JSONArray();

        // 创建第一个商品对象
        JSONObject goods1 = new JSONObject();
        goods1.put("goodsId", goodsId);
        goods1.put("specId", specId);
        goods1.put("num", num);
        goods1.put("yfMode", 1);
        goods1.put("price", price.multiply(new BigDecimal("100")));
        goods1.put("shipPrice", 0);
        goodsArray.add(goods1);
        // 将商品数组添加到订单对象中
        order.put("goods", goodsArray);

        // 创建收货人对象
        JSONObject consignee = new JSONObject();
        consignee.put("receiver", receiver);
        consignee.put("phone", phone);

        // 创建地址对象
        JSONObject address = new JSONObject();
        address.put("province", province);
        address.put("city", city);
        address.put("area", area);
        address.put("detail", detail);

        // 将地址对象添加到收货人对象中
        consignee.put("address", address);

        // 将收货人对象添加到订单对象中
        order.put("consignee", consignee);
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, order.toString());
        Request request = getRequest("/api/v1/order/order_create",body);
        try {
            Response response = client.newCall(request).execute();
            String res=response.body().string();
            JSONObject resJson=JSONObject.parseObject(res);
            return iSxscDingdongCommodityOrderMapperService.insertSxscDingdongCommodityOrderMapper(resJson,order,orderNo);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 订单支付
     * @param orderSn 创建订单 “4.3 订单创建”时，API返回的订单编号
     */
    public String orderPay(String orderSn){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\r\n    \"orderSn\": \""+orderSn+"\"\r\n}");
        Request request = getRequest("/api/v1/order/pay",body);
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 确认收货
     * @param orderSn 创建订单时，API返回的订单编号
     * @param orderId 商品单号，为空时则该订单下所有待收货订单都会改为已收货
     */
    public String orderReceipt(String orderSn,String orderId){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\r\n    \"orderSn\": \""+orderSn+"\",\r\n    \"orderId\": "+orderId+"\r\n}");
        Request request = getRequest("/api/v1/order/receipt",body);
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 确认收货
     * @param type 通知类型：1、商品通知，2、订单通知
     * @param url 通知URL
     */
    public void notityUrl(String type,String url){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        JSONObject json = new JSONObject();
        json.put("type", type);
        json.put("url", url);
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, json.toString());
        Request request = getRequest("/api/v1/user/notify_url",body);
        try {
            Response response = client.newCall(request).execute();
            JSONObject jsonObject=JSONObject.parseObject(response.body().string());
            if(jsonObject.getInteger("status")!=200){
                throw new ServiceException(jsonObject.getString("msg"));
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取商品库存
     * @param goodsId 商品主键
     */
    public String goodsInventory(Long goodsId){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body =  RequestBody.create(mediaType, "{\r\n    \"goodsId\": [\r\n        "+goodsId+"\r\n    ]\r\n}");
        Request request = getRequest("/api/v1/goods/inventory",body);
        try {
            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void setCommodity(String commodityJson,String userId,BigDecimal retailAmount,Long status,Long type,Long plateId,Long sortNumber,Long id){
        JSONObject jsonObject=JSONObject.parseObject(commodityJson);
        if(jsonObject.getInteger("status")==200){
            JSONObject content=jsonObject.getJSONObject("content");
            SxscCommodity sxscCommodity=new SxscCommodity();
            sxscCommodity.setUserId(Long.valueOf(userId));
            sxscCommodity.setName(content.getString("goodsName"));
            if(StringUtils.isNull(retailAmount)){
                sxscCommodity.setRetailAmount(new BigDecimal(content.getInteger("marketPrice")).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));
            }else{
                sxscCommodity.setRetailAmount(retailAmount);
            }
            sxscCommodity.setDetails(content.getString("description"));
            sxscCommodity.setTitleImgUrl(content.getString("defaultImage"));
            JSONArray images=content.getJSONArray("images");
            // 使用StringBuilder来构建最终的字符串
            StringBuilder urls = new StringBuilder();
            for (int j = 0; j < images.size(); j++) {
                // 获取每个JSONObject
                JSONObject img = images.getJSONObject(j);
                // 获取url属性
                String url = img.getString("imageUrl");
                // 拼接url，用逗号分隔
                if (urls.length() > 0) {
                    urls.append(",");
                }
                urls.append(url);
            }
            sxscCommodity.setDetailsImgUrl(urls.toString());
            sxscCommodity.setSourceType(2l);
            sxscCommodity.setFreightType(1l);
            sxscCommodity.setFreight(new BigDecimal("0"));
            sxscCommodity.setProductNature(1l);
            if(content.getInteger("onOffStatus")==1||content.getInteger("closeStatus")==0){
                sxscCommodity.setStatus(status);
            }else{
                sxscCommodity.setStatus(3l);
            }
            sxscCommodity.setType(type);
            sxscCommodity.setPlateId(plateId);
            sxscCommodity.setSortNumber(sortNumber);
            if(StringUtils.isNull(id)){
                sxscCommodity.setCreateBy("系统");
                sxscCommodity.setCreateTime(DateUtils.getNowDate());
                iSxscCommodityService.save(sxscCommodity);
            }else{
                sxscCommodity.setId(id);
                sxscCommodity.setUpdateBy("系统");
                sxscCommodity.setUpdateTime(DateUtils.getNowDate());
                iSxscCommodityService.updateById(sxscCommodity);
            }
            List<SxscCommoditySpecifications> specificationsList=new ArrayList<>();
            JSONArray specs=content.getJSONArray("specs");
            for(int k=0;k<specs.size();k++){
                JSONObject spec=specs.getJSONObject(k);
                SxscCommoditySpecifications specifications=new SxscCommoditySpecifications();
                specifications.setName(spec.getString("specValue1")+spec.getString("specValue2")+spec.getString("specValue3")+spec.getString("specValue4"));
                specifications.setStock(0l);
                specifications.setSupplierMapperId(Long.valueOf(spec.getInteger("specId")));
                specifications.setImgUrl(spec.getString("image"));
                if(StringUtils.isEmpty(specifications.getImgUrl())){
                    specifications.setImgUrl(sxscCommodity.getTitleImgUrl());
                }
                specifications.setSupplierPrice(new BigDecimal(spec.getInteger("marketPrice")).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));
                specifications.setSupplyAmount(new BigDecimal(spec.getInteger("price")).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));
                //specifications.setPackageNum(Long.valueOf(spec.getInteger("packageNum")));
                SxscCommoditySpecifications specificationsData=iSxscCommoditySpecificationsService.selectSxscCommoditySpecificationsBySupplierMapperId(specifications.getSupplierMapperId());
                if(StringUtils.isNotNull(specificationsData)){
                    specifications.setFloatingRatio(specificationsData.getFloatingRatio());
                    specifications.setPrice(specificationsData.getPrice());
                }else{
                    specifications.setPrice(specifications.getSupplierPrice());
                }
                specificationsList.add(specifications);
            }
            sxscCommodity.setSpecifications(specificationsList);
            iSxscCommoditySpecificationsService.insertSxscCommoditySpecifications(sxscCommodity.getSpecifications(),sxscCommodity.getId());

            SxscDingdongCommodityMapping dingdongMapping=new SxscDingdongCommodityMapping();
            dingdongMapping.setCommodityId(sxscCommodity.getId());
            dingdongMapping.setDingdongId(Long.valueOf(content.getInteger("goodsId")));
            dingdongMapping.setDingdongJsonDetail(content.toJSONString());
            iSxscDingdongCommodityMappingService.insertSxscCommodityDingdongMapping(dingdongMapping);
            updateCommodityStock(dingdongMapping);//更新库存
        }else{
            throw new ServiceException(jsonObject.getString("msg"));
        }
    }

    public void updateCommodityStock(SxscDingdongCommodityMapping commodityMapping){
        String json=goodsInventory(commodityMapping.getDingdongId());
        JSONObject jsonObject=JSONObject.parseObject(json);
        if(jsonObject.getInteger("status")==200){
            JSONArray contents=jsonObject.getJSONArray("content");
            for(int i=0;i<contents.size();i++){
                JSONObject content=contents.getJSONObject(i);
                JSONArray details=content.getJSONArray("detail");
                for(int j=0;j<details.size();j++){
                    JSONObject detail=details.getJSONObject(j);
                    Integer specId=detail.getInteger("specId");
                    Integer stock=detail.getInteger("stock");
                    Integer moq=detail.getInteger("moq");
                    iSxscCommoditySpecificationsService.updateSxscCommoditySpecificationsBySupplierMapperId(specId,stock,moq);
                }
            }
        }
        commodityMapping.setInventoryJson(json);
        iSxscDingdongCommodityMappingService.updateSxscCommodityDingdongMappingByDingdongId(commodityMapping);
    }

    private  Request getRequest(String url,RequestBody body){
        String timestamp= String.valueOf(DateUtils.getNowDate().getTime()/1000);
        String nonce= IdUtils.fastSimpleUUID();
        String md5="appKey"+appKey+"timestamp"+timestamp+"token"+getToken()+"nonce"+nonce+"appSecret"+appSecret;
        String signature= Md5Utils.hash(md5).toUpperCase();
        return new Request.Builder()
                .url(baseUrl+url)
                .method("POST", body)
                .addHeader("appKey", appKey)
                .addHeader("token", getToken())
                .addHeader("nonce", nonce)
                .addHeader("timestamp", timestamp)
                .addHeader("signature", signature)
                .addHeader("Content-Type", "application/json")
                .build();
    }

}

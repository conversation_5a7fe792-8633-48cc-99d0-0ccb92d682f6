package com.ruoyi.sxsc.dingdong.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 供应商订单回调信息对象
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscDingdongCommodityOrderNotify extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 供应商订单主键 */
    @Excel(name = "供应商订单主键")
    private String orderSn;

    /** 供应商订单回调数据 */
    @Excel(name = "供应商订单回调数据")
    private String notifyJson;

    /** 系统主动请求 */
    @Excel(name = "系统主动请求")
    private String proactivelyJson;

    /** 系统订单主键 */
    private String commodityOrderId;




}

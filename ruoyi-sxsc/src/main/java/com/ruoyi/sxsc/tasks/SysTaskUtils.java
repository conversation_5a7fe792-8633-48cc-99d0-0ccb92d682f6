package com.ruoyi.sxsc.tasks;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.commodity.domain.*;
import com.ruoyi.sxsc.commodity.model.SxscCommodityOrderExcelModel;
import com.ruoyi.sxsc.commodity.service.*;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.person.service.*;
import com.ruoyi.sxsc.utils.EmailSender;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


/**
 * 系统任务调度
 */
@Component("sysSxscTask")
public class SysTaskUtils {

    @Autowired
    ISxscCommodityOrderService iSxscCommodityOrderService;
    @Autowired
    private ISxscCommodityDeliveryService iSxscCommodityDeliveryService;
    @Autowired
    ISxscUserInfoService iSxscUserInfoService;
    @Autowired
    ISysUserService iSysUserService;
    @Autowired
    ISxscUserConsumeService iSxscUserConsumeService;
    @Autowired
    ISxscUserConsumeBuyService iSxscUserConsumeBuyService;
    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;
    @Autowired
    RedisCache redisCache;
    @Autowired
    ISxscCommodityService iSxscCommodityService;

    @Autowired
    private ISxscCommodityOrderRefundService sxscCommodityOrderRefundService;

    /**
     *  系统自动发送订单邮件
     */
    @Transactional
    public void sendOrder(){
        List<SysUser> sysUserList=iSysUserService.selectUserListUserType("02");
        for(SysUser sysUser:sysUserList){
            if(StringUtils.isEmpty(sysUser.getEmail())){
                continue;
            }
            LambdaQueryWrapper<SxscCommodityOrder> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.eq(SxscCommodityOrder::getStatus,2);
            queryWrapper.eq(SxscCommodityOrder::getSellerUserId,sysUser.getUserId());
            List<SxscCommodityOrder> list=iSxscCommodityOrderService.list(queryWrapper);

            if(StringUtils.isNull(list)||list.size()==0){
                continue;
            }
            List<SxscCommodityOrderExcelModel> excelModels=new ArrayList<>();
            for(SxscCommodityOrder commodityOrder:list){
                SxscCommodityDelivery sxscCommodityDelivery=iSxscCommodityDeliveryService.selectSxscCommodityDeliveryByOrderId(commodityOrder.getId(),1l);
                excelModels.add(new SxscCommodityOrderExcelModel(iSxscCommodityOrderService.selectSxscCommodityOrderById(commodityOrder.getId()),sxscCommodityDelivery));
            }
            ExcelUtil<SxscCommodityOrderExcelModel> util = new ExcelUtil<>(SxscCommodityOrderExcelModel.class);
            String filename=util.exportExcel(excelModels, "待发货明细");

            EmailSender.sendEmail(filename,sysUser.getEmail());
        }
    }

}

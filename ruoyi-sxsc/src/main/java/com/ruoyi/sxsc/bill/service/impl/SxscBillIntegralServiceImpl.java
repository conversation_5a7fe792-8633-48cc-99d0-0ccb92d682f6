package com.ruoyi.sxsc.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.constant.IntegralBillConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.bill.domain.SxscBillIntegral;
import com.ruoyi.sxsc.bill.mapper.SxscBillIntegralMapper;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ruoyi.common.utils.PageUtils.startPage;

/**
 * 账单信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
public class SxscBillIntegralServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscBillIntegralMapper,SxscBillIntegral> implements ISxscBillIntegralService
{


    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    private SxscBillIntegralMapper sxscBillIntegralMapper;

    @Autowired
    private ISysConfigService iSysConfigService;
    /**
     * 查询账单信息
     * 
     * @param id 账单信息主键
     * @return 账单信息
     */
    @Override
    public SxscBillIntegral selectSxscBillIntegralById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询账单信息
     *
     * @param orderId 订单编号
     * @param userId 用户主键
     * @return 账单信息
     */
    @Override
    public List<SxscBillIntegral> selectSxscBillIntegralByOrderId(String orderId,Long userId,Long  billType){

        LambdaQueryWrapper<SxscBillIntegral> wrapper=new LambdaQueryWrapper();

        wrapper.eq(SxscBillIntegral::getOrderId,orderId);

        wrapper.eq(SxscBillIntegral::getUserId,userId);

        wrapper.eq(SxscBillIntegral::getBillType,billType);

        return list(wrapper);
    }

    /**
     * 查询账单信息列表
     * 
     * @param sxscBillIntegral 账单信息
     * @return 账单信息
     */
    @Override
    public List<SxscBillIntegral> selectSxscBillIntegralList(SxscBillIntegral sxscBillIntegral)
    {
        LambdaQueryWrapper<SxscBillIntegral> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscBillIntegral.getOrderId()),SxscBillIntegral::getOrderId,sxscBillIntegral.getOrderId());

        wrapper.eq(StringUtils.isNotNull(sxscBillIntegral.getBillName()),SxscBillIntegral::getBillName,sxscBillIntegral.getBillName());

        wrapper.eq(StringUtils.isNotNull(sxscBillIntegral.getBillType()),SxscBillIntegral::getBillType,sxscBillIntegral.getBillType());

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscBillIntegral::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscBillIntegral.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscBillIntegral.getParams().get("phonenumber")+"', '%'))");
        }

        wrapper.orderByDesc(SxscBillIntegral::getCreateTime);

        wrapper.orderByDesc(SxscBillIntegral::getId);

        List<SxscBillIntegral> sxscBillIntegrals=list(wrapper);

        for(SxscBillIntegral billIntegral:sxscBillIntegrals){
            billIntegral.setSysUser(iSysUserService.selectUserMainById(billIntegral.getUserId()));
        }


        return sxscBillIntegrals;
    }


    /**
     * 查询账单信息列表
     *
     * @param billType 账单类型
     * @param createDate 账单月份
     * @return 账单信息集合
     */
    public AjaxResult selectSxscBillIntegralListByMonth(Long billType,String createDate){
        Map<String,Object> map=new HashMap<>();
        map.put("expenditure",sxscBillIntegralMapper.expenditure(billType,createDate,SecurityUtils.getUserId()));
        map.put("income",sxscBillIntegralMapper.income(billType,createDate,SecurityUtils.getUserId()));

        startPage();

        LambdaQueryWrapper<SxscBillIntegral> wrapper=new LambdaQueryWrapper();

        wrapper.eq(SxscBillIntegral::getBillType,billType);

        wrapper.eq(SxscBillIntegral::getUserId,SecurityUtils.getUserId());

        wrapper.apply( "DATE_FORMAT(create_time,'%Y-%m') = '"+ createDate+"'");

        wrapper.orderByDesc(SxscBillIntegral::getCreateTime);

        wrapper.orderByDesc(SxscBillIntegral::getId);

        List<SxscBillIntegral> list=list(wrapper);
        map.put("rows",list);
        map.put("total",new PageInfo(list).getTotal());

        return AjaxResult.success(map);
    }

    /**
     * 新增账单信息
     *
     * @param orderId 订单主键
     * @param billName 账单名称
     * @param billType 账单类型1贡献值2企业股3权证4权益值5积分
     * @param integral 积分
     * @return 结果
     */
    @Override
    @Transactional
    public void insertSxscBillIntegral(String orderId, String billName, Long billType, BigDecimal integral){
        SxscUserInfo sxscUserInfoData=iSxscUserInfoService.getById(SecurityUtils.getUserId());
        SxscUserInfo sxscUserInfo=new SxscUserInfo();
        SxscBillIntegral sxscBillIntegral=new SxscBillIntegral();
        switch (billType.intValue()){
            case 1:
                sxscUserInfo.setIntegralGxz(sxscUserInfoData.getIntegralGxz().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralGxz());
                break;
            case 2:
                sxscUserInfo.setIntegralJl(sxscUserInfoData.getIntegralJl().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralJl());
                break;
            case 3:
                sxscUserInfo.setIntegralSzqz(sxscUserInfoData.getIntegralSzqz().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralSzqz());
                break;
            case 4:
                sxscUserInfo.setIntegralSj(sxscUserInfoData.getIntegralSj().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralSj());
                break;
            case 5:
                sxscUserInfo.setIntegralTy(sxscUserInfoData.getIntegralTy().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralTy());
                break;
            case 6:
                sxscUserInfo.setBlindBox(sxscUserInfoData.getBlindBox().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getBlindBox());
                break;
            case 7:
                sxscUserInfo.setShare(sxscUserInfoData.getShare().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getShare());
                break;
            case 8:
                sxscUserInfo.setAcceptorAmount(sxscUserInfoData.getAcceptorAmount().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getAcceptorAmount());
                break;
            default:
                log.error("积分类型错误");
        }
        sxscUserInfo.setUserId(sxscUserInfoData.getUserId());
        sxscUserInfo.setUpdateTime(DateUtils.getNowDate());
        sxscUserInfo.setUpdateBy(SecurityUtils.getUsername());
        sxscBillIntegral.setIntegral(integral);
        sxscBillIntegral.setOrderId(orderId);
        sxscBillIntegral.setBillType(billType);
        sxscBillIntegral.setBillName(billName);
        sxscBillIntegral.setUserId(SecurityUtils.getUserId());
        sxscBillIntegral.setCreateBy(SecurityUtils.getUsername());
        sxscBillIntegral.setCreateTime(DateUtils.getNowDate());
        save(sxscBillIntegral);
        iSxscUserInfoService.updateSxscUserInfoIntegral(sxscUserInfo);
    }

    /**
     * 新增账单信息
     *
     * @param orderId 订单主键
     * @param billName 账单名称
     * @param billType 账单类型1贡献值2企业股3权证4权益值5积分
     * @param integral 积分
     * @param userId 用户主键
     * @return 结果
     */
    @Override
    @Transactional
    public void insertSxscBillIntegral(String orderId, String billName, Long billType, BigDecimal integral,Long userId){
        SxscUserInfo sxscUserInfoData=iSxscUserInfoService.getById(userId);
        SxscUserInfo sxscUserInfo=new SxscUserInfo();
        SxscBillIntegral sxscBillIntegral=new SxscBillIntegral();
        switch (billType.intValue()){
            case 1:
                sxscUserInfo.setIntegralGxz(sxscUserInfoData.getIntegralGxz().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralGxz());
                break;
            case 2:
                sxscUserInfo.setIntegralJl(sxscUserInfoData.getIntegralJl().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralJl());
                break;
            case 3:
                sxscUserInfo.setIntegralSzqz(sxscUserInfoData.getIntegralSzqz().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralSzqz());
                break;
            case 4:
                sxscUserInfo.setIntegralSj(sxscUserInfoData.getIntegralSj().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralSj());
                break;
            case 5:
                sxscUserInfo.setIntegralTy(sxscUserInfoData.getIntegralTy().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getIntegralTy());
                break;
            case 6:
                sxscUserInfo.setBlindBox(sxscUserInfoData.getBlindBox().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getBlindBox());
                break;
            case 7:
                sxscUserInfo.setShare(sxscUserInfoData.getShare().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getShare());
                break;
            case 8:
                sxscUserInfo.setAcceptorAmount(sxscUserInfoData.getAcceptorAmount().add(integral));
                sxscBillIntegral.setBalance(sxscUserInfo.getAcceptorAmount());
                break;
            default:
                log.error("积分类型错误");
        }
        sxscUserInfo.setUserId(sxscUserInfoData.getUserId());
        sxscUserInfo.setUpdateTime(DateUtils.getNowDate());
        sxscBillIntegral.setIntegral(integral);
        sxscBillIntegral.setOrderId(orderId);
        sxscBillIntegral.setBillType(billType);
        sxscBillIntegral.setBillName(billName);
        sxscBillIntegral.setUserId(userId);
        sxscBillIntegral.setCreateTime(DateUtils.getNowDate());
        save(sxscBillIntegral);
        iSxscUserInfoService.updateSxscUserInfoIntegral(sxscUserInfo);
    }


    /**
     * 退款账单信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public void refundSxscBillIntegral(String orderId){
        LambdaQueryWrapper<SxscBillIntegral> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscBillIntegral::getOrderId,orderId);
        List<SxscBillIntegral> list=list(wrapper);
        for(SxscBillIntegral integral:list){
            this.insertSxscBillIntegral(integral.getOrderId(),"退款-"+integral.getBillName(),integral.getBillType(),integral.getIntegral().multiply(new BigDecimal("-1")),integral.getUserId());
        }
    }

}

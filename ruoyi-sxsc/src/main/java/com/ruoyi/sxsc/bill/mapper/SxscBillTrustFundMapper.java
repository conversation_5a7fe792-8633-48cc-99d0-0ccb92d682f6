package com.ruoyi.sxsc.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.bill.domain.SxscBillTrustFund;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 信托基金账单信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface SxscBillTrustFundMapper extends BaseMapper<SxscBillTrustFund>
{

    /**
     * 查询信托基金总和
     * @param  createTime 账单日期
     * @return  利润总额
     */
    @Select("<script>"+
            "select IFNULL(sum(integral), 0) from sxsc_bill_trust_fund "+
            "where 1=1" +
            "<if test='createTime != null and createTime != \"\" '>  "+
                 "and DATE_FORMAT(create_time,'%Y-%m-%d') =#{createTime}"+
            "</if>"+
            "</script>")
    BigDecimal totalProfit(@Param("createTime")String createTime);


}

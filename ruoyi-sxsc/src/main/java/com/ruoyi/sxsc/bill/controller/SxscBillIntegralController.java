package com.ruoyi.sxsc.bill.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.domain.SxscBillIntegral;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 积分账单
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/bill/integral")
public class SxscBillIntegralController extends BaseController
{
    @Autowired
    private ISxscBillIntegralService sxscBillIntegralService;

    @Autowired
    private ISysUserService iSysUserService;

    /**
     * 查询账单信息列表
     */
    @PreAuthorize("@ss.hasPermi('bill:integral:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscBillIntegral sxscBillIntegral)
    {
        startPage();
        List<SxscBillIntegral> list = sxscBillIntegralService.selectSxscBillIntegralList(sxscBillIntegral);
        return getDataTable(list);
    }

    /**
     * 按月份查询账单信息列表
     */
    @PreAuthorize("@ss.hasPermi('bill:integral:list')")
    @GetMapping("/list/month")
    public AjaxResult listByMonth(@RequestParam("billType") Long billType,@RequestParam("createDate") String createDate)
    {

        return sxscBillIntegralService.selectSxscBillIntegralListByMonth(billType,createDate);
    }


    /**
     * 获取账单信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bill:integral:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscBillIntegralService.selectSxscBillIntegralById(id));
    }


    /**
     * 后台批量增加或减少积分
     */
    @PreAuthorize("@ss.hasPermi('sxsc:bill:integral:htBatch:recharge')")
    @Log(title = "后台批量操作积分", businessType = BusinessType.UPDATE)
    @PostMapping("/ht-recharge/batch/{billType}")
    public AjaxResult rechargeTxBatch(@PathVariable("billType") Long billType, @RequestBody Map<String,String> map)
    {
        String text=map.get("text");
        String orderId= IdUtils.fastSimpleUUID();
        String billName="系统操作";
        StringBuilder successMsg = new StringBuilder();
        for(String phoneAndIntegral:text.split(";")){
            String userName=phoneAndIntegral.split(",")[0];
            String integral=phoneAndIntegral.split(",")[1];
            SysUser sysUser=iSysUserService.selectUserByUserName(userName);
            if(StringUtils.isNotNull(sysUser)){
                sxscBillIntegralService.insertSxscBillIntegral(orderId,billName,billType,new BigDecimal(integral),sysUser.getUserId());
                successMsg.append("<br/>账号:<" + userName + ">,操作成功");
            }else{
              successMsg.append("<br/><span style='color:red'>账号:<" + phoneAndIntegral  + ">操作失败,账号不存在</span>");
            }
        }
        return success(successMsg.toString());
    }

}

package com.ruoyi.sxsc.bill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 账单信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscBillIntegral extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 订单主键 */
    @Excel(name = "订单主键")
    private String orderId;

    /** 账单名称 */
    @Excel(name = "账单名称")
    private String billName;

    /** 账单类型1贡献值2企业股3权证4权益值5积分8承兑商额度 */
    @Excel(name = "账单类型1贡献值2企业股3权证4权益值5积分")
    private Long billType;

    /** 账号主键 */
    @Excel(name = "账号主键")
    private Long userId;

    /** 余额 */
    @Excel(name = "余额")
    private BigDecimal balance;

    /** 积分 */
    @Excel(name = "积分")
    private BigDecimal integral;


    @TableField(exist = false)
    private SysUserMain sysUser;


}

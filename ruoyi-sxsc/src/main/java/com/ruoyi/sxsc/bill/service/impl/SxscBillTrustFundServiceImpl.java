package com.ruoyi.sxsc.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.bill.domain.SxscBillTrustFund;
import com.ruoyi.sxsc.bill.mapper.SxscBillTrustFundMapper;
import com.ruoyi.sxsc.bill.service.ISxscBillTrustFundService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBackService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 信托基金账单信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class SxscBillTrustFundServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscBillTrustFundMapper,SxscBillTrustFund> implements ISxscBillTrustFundService
{

    @Autowired
    SxscBillTrustFundMapper sxscBillTrustFundMapper;

    @Autowired
    ISxscUserCommissionOrderService iSxscUserCommissionOrderService;

    @Autowired
    ISxscUserConsumeBackService iSxscUserConsumeBackService;

    /**
     * 查询信托基金账单信息
     * 
     * @param id 信托基金账单信息主键
     * @return 信托基金账单信息
     */
    @Override
    public SxscBillTrustFund selectSxscBillTrustFundById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询信托基金账单信息列表
     * 
     * @param sxscBillTrustFund 信托基金账单信息
     * @return 信托基金账单信息
     */
    @Override
    public List<SxscBillTrustFund> selectSxscBillTrustFundList(SxscBillTrustFund sxscBillTrustFund)
    {
        LambdaQueryWrapper<SxscBillTrustFund> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscBillTrustFund.getId()),SxscBillTrustFund::getId,sxscBillTrustFund.getId());

        wrapper.eq(StringUtils.isNotNull(sxscBillTrustFund.getOrderId()),SxscBillTrustFund::getOrderId,sxscBillTrustFund.getOrderId());

        wrapper.orderByDesc(SxscBillTrustFund::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增信托基金账单信息
     *
     * @param integral 积分
     * @param proportion 获取积分比例
     * @param orderId 订单主键
     * @return 结果
     */
    @Override
    public int insertSxscBillTrustFund(BigDecimal integral, BigDecimal proportion, String orderId)
    {
        SxscBillTrustFund sxscBillTrustFund=new SxscBillTrustFund();
        sxscBillTrustFund.setIntegral(integral);
        sxscBillTrustFund.setProportion(proportion);
        sxscBillTrustFund.setOrderId(orderId);
        sxscBillTrustFund.setCreateTime(DateUtils.getNowDate());
        return save(sxscBillTrustFund)?1:0;
    }

    /**
     * 退款信托基金账单信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    @Override
    public void refundSxscBillTrustFund(String orderId){
        LambdaQueryWrapper<SxscBillTrustFund> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscBillTrustFund::getOrderId,orderId);
        List<SxscBillTrustFund> list=list(wrapper);
        for(SxscBillTrustFund sxscBillTrustFund:list){
            this.insertSxscBillTrustFund(sxscBillTrustFund.getIntegral().multiply(new BigDecimal("-1")),sxscBillTrustFund.getProportion(),sxscBillTrustFund.getOrderId());
        }
    }

    /**
     * 查询信托基金总和
     *
     * @param createTime 传入日期则查询日期当天，不传则是全部
     * @return 结果
     */
    @Override
    public BigDecimal totalProfit(String createTime){
        return sxscBillTrustFundMapper.totalProfit(createTime);
    }

    /**
     * 查询当前商城结余总利润（信托基金总和-佣金提现-优惠券回购）
     *
     * @return 结果
     */
    @Override
    public BigDecimal totalSurplusProfit(){
        //信托基金总和
        BigDecimal totalProfit=totalProfit(null);
        //佣金提现
        BigDecimal commissionOrderSum=iSxscUserCommissionOrderService.commissionOrderSum(null);
        //优惠券回购
        BigDecimal BackTotalAmount=iSxscUserConsumeBackService.selectSxscUserConsumeBackTotalAmount();

        return totalProfit.subtract(commissionOrderSum).subtract(BackTotalAmount);
    }

}

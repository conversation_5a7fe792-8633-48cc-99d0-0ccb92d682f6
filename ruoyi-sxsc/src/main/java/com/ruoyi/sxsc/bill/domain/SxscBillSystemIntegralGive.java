package com.ruoyi.sxsc.bill.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 系统赠送用户积分对象
 * 
 * <AUTHOR>
 * @date 2024-09-18
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscBillSystemIntegralGive extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户主键 */
    @TableId(type = IdType.INPUT)
    private Long userId;

    /** 用户手机号 */
    @Excel(name = "用户手机号")
    private String phonenumber;

    /** 总积分 */
    @Excel(name = "总积分")
    private BigDecimal integral;


    /** 释放基数 */
    @Excel(name = "释放基数")
    private BigDecimal baseNumber;

    /** 释放比例 */
    @Excel(name = "释放比例")
    private BigDecimal proportion;

    /** 剩余积分 */
    @Excel(name = "剩余积分")
    private BigDecimal balance;

    /** 剩余积分 */
    @Excel(name = "剩余积分")
    private Long delFlag;


}

package com.ruoyi.sxsc.bill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 信托基金账单信息对象
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscBillTrustFund extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 获取积分比例 */
    @Excel(name = "获取积分比例")
    private BigDecimal proportion;

    /** 积分 */
    @Excel(name = "积分")
    private BigDecimal integral;

    /** 订单主键 */
    @Excel(name = "订单主键")
    private String orderId;




}

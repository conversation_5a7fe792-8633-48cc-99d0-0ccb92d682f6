package com.ruoyi.sxsc.bill.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.bill.domain.SxscBillSystemIntegralGive;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 系统赠送用户积分Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-18
 */
public interface ISxscBillSystemIntegralGiveService extends IService<SxscBillSystemIntegralGive>
{
    /**
     * 查询系统赠送用户积分
     * 
     * @param userId 系统赠送用户积分主键
     * @return 系统赠送用户积分
     */
    public SxscBillSystemIntegralGive selectSxscUserIntegralGiveByUserId(Long userId);

    /**
     * 查询系统赠送用户积分列表
     * 
     * @param sxscBIllSystemIntegralGive 系统赠送用户积分
     * @return 系统赠送用户积分集合
     */
    public List<SxscBillSystemIntegralGive> selectSxscUserIntegralGiveList(SxscBillSystemIntegralGive sxscBIllSystemIntegralGive);

    /**
     * 新增系统赠送用户积分
     * 
     * @param sxscBIllSystemIntegralGive 系统赠送用户积分
     * @return 结果
     */
    public AjaxResult insertSxscUserIntegralGive(SxscBillSystemIntegralGive sxscBIllSystemIntegralGive);

    /**
     * 修改系统赠送用户积分
     * 
     * @param sxscBIllSystemIntegralGive 系统赠送用户积分
     * @return 结果
     */
    public AjaxResult updateSxscUserIntegralGive(SxscBillSystemIntegralGive sxscBIllSystemIntegralGive);


}

package com.ruoyi.sxsc.bill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.bill.domain.SxscBillIntegral;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 账单信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface SxscBillIntegralMapper extends BaseMapper<SxscBillIntegral>
{

    @Select("select IFNULL(sum(integral), 0) from sxsc_bill_integral " +
            "where DATE_FORMAT(create_time,'%Y-%m') =#{createDate} and integral<0 and bill_type=#{billType} and user_id=#{userId}")
    BigDecimal expenditure(@Param("billType")Long billType,
                           @Param("createDate")String createDate,
                           @Param("userId")Long userId);

    @Select("select IFNULL(sum(integral), 0) from sxsc_bill_integral " +
            "where DATE_FORMAT(create_time,'%Y-%m') =#{createDate} and integral>0 and bill_type=#{billType} and user_id=#{userId}")
    BigDecimal income(@Param("billType")Long billType,
                      @Param("createDate")String createDate,
                      @Param("userId")Long userId);

}

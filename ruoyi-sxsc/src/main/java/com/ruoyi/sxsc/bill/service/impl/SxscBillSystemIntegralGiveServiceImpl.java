package com.ruoyi.sxsc.bill.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.bill.mapper.SxscBillSystemIntegralGiveMapper;
import com.ruoyi.sxsc.bill.domain.SxscBillSystemIntegralGive;
import com.ruoyi.sxsc.bill.service.ISxscBillSystemIntegralGiveService;

/**
 * 系统赠送用户积分Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-18
 */
@Service
public class SxscBillSystemIntegralGiveServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscBillSystemIntegralGiveMapper, SxscBillSystemIntegralGive> implements ISxscBillSystemIntegralGiveService
{

    @Autowired
    ISysUserService iSysUserService;

    /**
     * 查询系统赠送用户积分
     * 
     * @param userId 系统赠送用户积分主键
     * @return 系统赠送用户积分
     */
    @Override
    public SxscBillSystemIntegralGive selectSxscUserIntegralGiveByUserId(Long userId)
    {
        return getById(userId);
    }

    /**
     * 查询系统赠送用户积分列表
     * 
     * @param sxscBIllSystemIntegralGive 系统赠送用户积分
     * @return 系统赠送用户积分
     */
    @Override
    public List<SxscBillSystemIntegralGive> selectSxscUserIntegralGiveList(SxscBillSystemIntegralGive sxscBIllSystemIntegralGive)
    {
        LambdaQueryWrapper<SxscBillSystemIntegralGive> wrapper=new LambdaQueryWrapper();
        wrapper.eq(StringUtils.isNotNull(sxscBIllSystemIntegralGive.getPhonenumber()), SxscBillSystemIntegralGive::getPhonenumber, sxscBIllSystemIntegralGive.getPhonenumber());
        wrapper.eq(StringUtils.isNotNull(sxscBIllSystemIntegralGive.getIntegral()), SxscBillSystemIntegralGive::getIntegral, sxscBIllSystemIntegralGive.getIntegral());
        wrapper.eq(StringUtils.isNotNull(sxscBIllSystemIntegralGive.getProportion()), SxscBillSystemIntegralGive::getProportion, sxscBIllSystemIntegralGive.getProportion());
        wrapper.eq(SxscBillSystemIntegralGive::getDelFlag,0l);
        wrapper.orderByAsc(SxscBillSystemIntegralGive::getUserId);
        return list(wrapper);
    }

    /**
     * 新增系统赠送用户积分
     * 
     * @param sxscBIllSystemIntegralGive 系统赠送用户积分
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscUserIntegralGive(SxscBillSystemIntegralGive sxscBIllSystemIntegralGive)
    {
        SysUser sysUser=iSysUserService.selectUserByUserName(sxscBIllSystemIntegralGive.getPhonenumber());
        if(StringUtils.isNull(sysUser)){
            return AjaxResult.error("用户不存在，无法添加");
        }
        SxscBillSystemIntegralGive integralGiveData=getById(sysUser.getUserId());
        if(StringUtils.isNotNull(integralGiveData)){
            return AjaxResult.error("用户已存在，无法添加");
        }
        sxscBIllSystemIntegralGive.setUserId(sysUser.getUserId());
        sxscBIllSystemIntegralGive.setBalance(sxscBIllSystemIntegralGive.getIntegral());
        sxscBIllSystemIntegralGive.setCreateTime(DateUtils.getNowDate());
        sxscBIllSystemIntegralGive.setCreateBy(SecurityUtils.getUsername());
        save(sxscBIllSystemIntegralGive);
        return AjaxResult.success();
    }

    /**
     * 修改系统赠送用户积分
     * 
     * @param sxscBIllSystemIntegralGive 系统赠送用户积分
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscUserIntegralGive(SxscBillSystemIntegralGive sxscBIllSystemIntegralGive)
    {
        SxscBillSystemIntegralGive integralGiveData=getById(sxscBIllSystemIntegralGive.getUserId());

        SxscBillSystemIntegralGive updateGive=new SxscBillSystemIntegralGive();
        //总积分=（已知的总积分-剩余的余额）=已经赠送的积分 +本次输入的余额（还需赠送的积分）
        updateGive.setIntegral(integralGiveData.getIntegral().subtract(integralGiveData.getBalance()).add(sxscBIllSystemIntegralGive.getBalance()));
        updateGive.setBalance(sxscBIllSystemIntegralGive.getBalance());
        updateGive.setProportion(sxscBIllSystemIntegralGive.getProportion());
        updateGive.setUpdateTime(DateUtils.getNowDate());
        updateGive.setUpdateBy(SecurityUtils.getUsername());
        updateGive.setUserId(sxscBIllSystemIntegralGive.getUserId());
        updateGive.setBaseNumber(sxscBIllSystemIntegralGive.getBaseNumber());
        updateById(updateGive);
        return AjaxResult.success();
    }


}

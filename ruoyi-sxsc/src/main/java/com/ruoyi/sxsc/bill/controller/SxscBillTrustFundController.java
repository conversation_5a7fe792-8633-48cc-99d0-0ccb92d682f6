package com.ruoyi.sxsc.bill.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.sxsc.bill.domain.SxscBillTrustFund;
import com.ruoyi.sxsc.bill.service.ISxscBillTrustFundService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 信托基金账单信息
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/bill/trustFund")
public class SxscBillTrustFundController extends BaseController
{
    @Autowired
    private ISxscBillTrustFundService sxscBillTrustFundService;

    /**
     * 查询信托基金账单信息列表
     */
    @PreAuthorize("@ss.hasPermi('bill:trustFund:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscBillTrustFund sxscBillTrustFund)
    {
        startPage();
        List<SxscBillTrustFund> list = sxscBillTrustFundService.selectSxscBillTrustFundList(sxscBillTrustFund);
        return getDataTable(list);
    }

    /**
     * 获取信托基金账单信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bill:trustFund:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscBillTrustFundService.selectSxscBillTrustFundById(id));
    }


}

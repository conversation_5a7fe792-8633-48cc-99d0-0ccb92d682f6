package com.ruoyi.sxsc.bill.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.bill.domain.SxscBillSystemIntegralGive;
import com.ruoyi.sxsc.bill.service.ISxscBillSystemIntegralGiveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 赠送积分
 * 
 * <AUTHOR>
 * @date 2024-09-23
 */
@RestController
@RequestMapping("/bill/integralGive")
public class SxscBillSystemIntegralGiveController extends BaseController
{
    @Autowired
    private ISxscBillSystemIntegralGiveService sxscUserIntegralGiveService;

    /**
     * 查询赠送积分列表
     */
    @PreAuthorize("@ss.hasPermi('person:integralGive:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscBillSystemIntegralGive sxscBIllSystemIntegralGive)
    {
        startPage();
        List<SxscBillSystemIntegralGive> list = sxscUserIntegralGiveService.selectSxscUserIntegralGiveList(sxscBIllSystemIntegralGive);
        return getDataTable(list);
    }



    /**
     * 获取赠送积分详细信息
     */
    @PreAuthorize("@ss.hasPermi('person:integralGive:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(sxscUserIntegralGiveService.selectSxscUserIntegralGiveByUserId(userId));
    }

    /**
     * 新增赠送积分
     */
    @PreAuthorize("@ss.hasPermi('person:integralGive:add')")
    @Log(title = "赠送积分", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscBillSystemIntegralGive sxscBIllSystemIntegralGive)
    {
        return sxscUserIntegralGiveService.insertSxscUserIntegralGive(sxscBIllSystemIntegralGive);
    }

    /**
     * 修改赠送积分
     */
    @PreAuthorize("@ss.hasPermi('person:integralGive:edit')")
    @Log(title = "赠送积分", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscBillSystemIntegralGive sxscBIllSystemIntegralGive)
    {
        return sxscUserIntegralGiveService.updateSxscUserIntegralGive(sxscBIllSystemIntegralGive);
    }


}

package com.ruoyi.sxsc.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.bill.domain.SxscBillTrustFund;

import java.math.BigDecimal;
import java.util.List;

/**
 * 信托基金账单信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface ISxscBillTrustFundService extends IService<SxscBillTrustFund>
{
    /**
     * 查询信托基金账单信息
     * 
     * @param id 信托基金账单信息主键
     * @return 信托基金账单信息
     */
    SxscBillTrustFund selectSxscBillTrustFundById(Long id);

    /**
     * 查询信托基金账单信息列表
     * 
     * @param sxscBillTrustFund 信托基金账单信息
     * @return 信托基金账单信息集合
     */
    List<SxscBillTrustFund> selectSxscBillTrustFundList(SxscBillTrustFund sxscBillTrustFund);

    /**
     * 新增信托基金账单信息
     * 
     * @param integral 积分
     * @param proportion 获取积分比例
     * @param orderId 订单主键
     * @return 结果
     */
    int insertSxscBillTrustFund(BigDecimal integral,BigDecimal proportion,String orderId);

    /**
     * 退款信托基金账单信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    void refundSxscBillTrustFund(String orderId);

    /**
     * 查询信托基金总和
     * 
     * @param createTime 传入日期则查询日期当天，不传则是全部
     * @return 结果
     */
    BigDecimal totalProfit(String createTime);

    /**
     * 查询当前商城结余总利润（信托基金总和-佣金提现-优惠券回购）
     *
     * @return 结果
     */
    BigDecimal totalSurplusProfit();
}

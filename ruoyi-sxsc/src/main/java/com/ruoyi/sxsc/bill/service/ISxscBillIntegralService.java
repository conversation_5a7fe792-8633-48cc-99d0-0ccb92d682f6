package com.ruoyi.sxsc.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.bill.domain.SxscBillIntegral;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账单信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface ISxscBillIntegralService extends IService<SxscBillIntegral>
{
    /**
     * 查询账单信息
     * 
     * @param id 账单信息主键
     * @return 账单信息
     */
    SxscBillIntegral selectSxscBillIntegralById(Long id);

    /**
     * 查询账单信息
     *
     * @param orderId 订单编号
     * @param userId 用户主键
     * @return 账单信息
     */
    List<SxscBillIntegral> selectSxscBillIntegralByOrderId(String orderId,Long userId,Long  billType);

    /**
     * 查询账单信息列表
     * 
     * @param sxscBillIntegral 账单信息
     * @return 账单信息集合
     */
    List<SxscBillIntegral> selectSxscBillIntegralList(SxscBillIntegral sxscBillIntegral);

    /**
     * 查询账单信息列表
     *
     * @param billType 账单类型
     * @param createDate 账单月份
     * @return 账单信息集合
     */
    AjaxResult selectSxscBillIntegralListByMonth(Long billType,String createDate);


    /**
     * 新增账单信息
     *
     * @param orderId 订单主键
     * @param billName 账单名称
     * @param billType 账单类型1贡献值2企业股3权证4权益值5积分6盲盒7集团股8承兑商额度
     * @param integral 积分
     * @return 结果
     */
    void insertSxscBillIntegral(String orderId, String billName, Long billType, BigDecimal integral);


    /**
     * 新增账单信息
     *
     * @param orderId 订单主键
     * @param billName 账单名称
     * @param billType 账单类型1贡献值2企业股3权证4权益值5积分6盲盒7集团股8承兑商额度
     * @param integral 积分
     * @param userId 用户主键
     * @return 结果
     */
    void insertSxscBillIntegral(String orderId, String billName, Long billType, BigDecimal integral,Long userId);

    /**
     * 退款账单信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    void refundSxscBillIntegral(String orderId);

}

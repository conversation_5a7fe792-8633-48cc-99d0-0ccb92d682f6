package com.ruoyi.sxsc.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 支付宝订单信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface SxscAliPayOrderMapper extends BaseMapper<SxscAliPayOrder>
{

    /**
     * 查询支付宝订单支付金额
     * @param month 月份
     * @param userId 用户主键
     * @return 支付宝订单信息
     */
    @Select("<script>"+
            "select IFNULL(sum(actual_total_amount), 0) from sxsc_ali_pay_order " +
            "where pay_status=1" +
            "<if test=' month!=null and month!=\"\" '>  and DATE_FORMAT(create_time,'%Y-%m') =#{month}   </if>"+
            "<if test=' userId!=null  '>  and user_id =#{userId}   </if>"+
            "</script>")
    BigDecimal aliPaySum(@Param("month")String month,@Param("userId")Long userId);

}

package com.ruoyi.sxsc.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.payment.domain.SxscAliPayWithdrawal;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付宝提现信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface ISxscAliPayWithdrawalService extends IService<SxscAliPayWithdrawal>
{
    /**
     * 查询支付宝提现信息
     * 
     * @param id 支付宝提现信息主键
     * @return 支付宝提现信息
     */
    SxscAliPayWithdrawal selectSxscAliPayWithdrawalById(String id);

    /**
     * 查询支付宝提现总金额
     *
     * @param orderName 订单名称
     * @param month 月份
     * @return 支付宝提现信息
     */
    BigDecimal aliPayWithdrawalSum(String orderName,String month);

    /**
     * 查询支付宝提现信息列表
     * 
     * @param sxscAliPayWithdrawal 支付宝提现信息
     * @return 支付宝提现信息集合
     */
    List<SxscAliPayWithdrawal> selectSxscAliPayWithdrawalList(SxscAliPayWithdrawal sxscAliPayWithdrawal);

    /**
     * 新增支付宝提现信息
     * 
     * @param amount 金额
     * @param userId 用户主键
     * @param orderName 订单名称
     * @param orderId 订单主键
     * @return 结果
     */
    SxscAliPayWithdrawal insertSxscAliPayWithdrawal(BigDecimal amount,Long userId,String orderId,String orderName);

    /**
     * 重新发起提现
     * 
     * @param id 主键
     * @return 结果
     */
    AjaxResult reissueRefund(String id);


}

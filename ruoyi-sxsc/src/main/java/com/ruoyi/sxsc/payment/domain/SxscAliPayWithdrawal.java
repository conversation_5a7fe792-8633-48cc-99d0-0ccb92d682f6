package com.ruoyi.sxsc.payment.domain;

import com.alipay.api.response.AlipayFundTransToaccountTransferResponse;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 支付宝提现信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscAliPayWithdrawal extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 关联订单号 */
    @Excel(name = "关联订单号")
    private String orderId;

    /** 收款方账户类型 */
    @Excel(name = "收款方账户类型")
    private String payeeType;

    /** 收款方账户 */
    @Excel(name = "收款方账户")
    private String payeeAccount;

    /** 收款方真实姓名 */
    @Excel(name = "收款方真实姓名")
    private String payeeRealName;

    /** 金额 */
    @Excel(name = "金额")
    private BigDecimal amount;

    /** 转账备注 */
    @Excel(name = "转账备注")
    private String orderName;

    /** 返回信息 */
    @Excel(name = "返回信息")
    private String res;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 状态1成功0失败 */
    @Excel(name = "状态1成功0失败")
    private Long status;

    /** 支付宝返回数据对象 */
    @TableField(exist = false)
    private AlipayFundTransUniTransferResponse alipayFundTransUniTransferResponse;


}

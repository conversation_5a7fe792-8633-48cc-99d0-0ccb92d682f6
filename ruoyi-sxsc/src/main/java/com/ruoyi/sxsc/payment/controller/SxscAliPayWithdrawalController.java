package com.ruoyi.sxsc.payment.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.payment.domain.SxscAliPayWithdrawal;
import com.ruoyi.sxsc.payment.service.ISxscAliPayWithdrawalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 支付宝提现信息
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/ali/payment/withdrawal")
public class SxscAliPayWithdrawalController extends BaseController
{
    @Autowired
    private ISxscAliPayWithdrawalService sxscAliPayWithdrawalService;

    /**
     * 查询支付宝提现信息列表
     */
    @PreAuthorize("@ss.hasPermi('payment:withdrawal:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscAliPayWithdrawal sxscAliPayWithdrawal)
    {
        startPage();
        List<SxscAliPayWithdrawal> list = sxscAliPayWithdrawalService.selectSxscAliPayWithdrawalList(sxscAliPayWithdrawal);
        return getDataTable(list);
    }

    /**
     * 获取支付宝提现信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('payment:withdrawal:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscAliPayWithdrawalService.selectSxscAliPayWithdrawalById(id));
    }

    /**
     * 重新发起提现
     */
    @PreAuthorize("@ss.hasPermi('payment:withdrawal:reissueRefund')")
    @Log(title = "支付宝提现", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    public AjaxResult reissueRefund(@PathVariable String id)
    {
        return sxscAliPayWithdrawalService.reissueRefund(id);
    }
}

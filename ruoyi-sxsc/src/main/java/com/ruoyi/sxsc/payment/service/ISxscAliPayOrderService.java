package com.ruoyi.sxsc.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 支付宝订单信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface ISxscAliPayOrderService extends IService<SxscAliPayOrder>
{
    /**
     * 查询支付宝订单信息
     * 
     * @param id 支付宝订单信息主键
     * @return 支付宝订单信息
     */
    SxscAliPayOrder selectSxscAliPayOrderById(String id);

    /**
     * 查询支付宝订单支付总金额
     * @param month 月份
     * @param userId 用户主键
     * @return 支付宝订单信息
     */
    BigDecimal aliPaySum(String month,Long userId);

    /**
     * 查询支付宝订单信息列表
     * 
     * @param sxscAliPayOrder 支付宝订单信息
     * @return 支付宝订单信息集合
     */
    List<SxscAliPayOrder> selectSxscAliPayOrderList(SxscAliPayOrder sxscAliPayOrder);

    /**
     * 新增支付宝订单信息
     * 
     * @param sxscAliPayOrder 支付宝订单信息
     * @return 结果
     */
    String insertSxscAliPayOrder(SxscAliPayOrder sxscAliPayOrder);


    /**
     * APP支付宝支付成功后,回调该接口
     *
     * @return 结果
     */
    String notifyData(HttpServletRequest request, HttpServletResponse response);


    /**
     * 支付成功，业务回调
     *
     * @param tradeStatus 支付状态
     * @param id 订单主键
     * @param totalAmount 支付金额
     *
     * @return 结果
     */
    void call(String tradeStatus,String id,String totalAmount,String requestParams);




}

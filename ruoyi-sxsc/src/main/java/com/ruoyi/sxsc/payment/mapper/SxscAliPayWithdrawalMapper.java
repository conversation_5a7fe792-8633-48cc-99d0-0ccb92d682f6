package com.ruoyi.sxsc.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.payment.domain.SxscAliPayWithdrawal;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 支付宝提现信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface SxscAliPayWithdrawalMapper extends BaseMapper<SxscAliPayWithdrawal>
{

    /**
     * 查询支付宝订单支付总金额
     * @param month 月份
     * @return 支付宝订单信息
     */
    @Select("<script>"+
            "select IFNULL(sum(amount), 0) from sxsc_ali_pay_withdrawal " +
            "where status=1" +
            "<if test=' month!=null and month!=\"\" '>  and DATE_FORMAT(create_time,'%Y-%m') =#{month}   </if>"+
            "<if test=' orderName!=null and orderName!=\"\" '>  and order_name =#{orderName}   </if>"+
            "</script>")
    BigDecimal aliPayWithdrawalSum(@Param("orderName")String orderName,@Param("month")String month);

}

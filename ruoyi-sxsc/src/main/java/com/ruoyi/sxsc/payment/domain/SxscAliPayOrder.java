package com.ruoyi.sxsc.payment.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityStaticCodeOrder;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumePurchase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付宝订单信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscAliPayOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 订单名称 */
    @Excel(name = "订单名称")
    private String subject;

    /** 付款金额 */
    @Excel(name = "付款金额")
    private BigDecimal totalAmount;

    /** 实际支付金额 */
    @Excel(name = "实际支付金额")
    private BigDecimal actualTotalAmount;

    /** 卖家ID */
    @Excel(name = "卖家ID")
    private Long sellerUserId;

    /** 买家ID */
    @Excel(name = "买家ID")
    private Long buyerUserId;

    /** 0待支付1支付成功2交易到账 */
    @Excel(name = "0待支付1支付成功2交易到账")
    private Long payStatus;


    /** 调用支付宝返回信息 */
    @Excel(name = "调用支付宝返回信息")
    private String response;

    /** 支付成功回调时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付成功回调时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date notifyDate;

    /** 支付宝回调返回信息 */
    @Excel(name = "支付宝回调返回信息")
    private String notifyResponse;

    /** 订单类型 1商品2静态码3优惠券4商品保证金*/
    @Excel(name = "订单类型")
    private Long orderType;

    public SxscAliPayOrder(SxscCommodityOrder sxscCommodityOrder){
        this.totalAmount=sxscCommodityOrder.getTotalAmount();
        this.id=sxscCommodityOrder.getId();
        this.subject=sxscCommodityOrder.getCommodityName()+"<规格:"+sxscCommodityOrder.getSpecificationsName()+">";
        this.sellerUserId=sxscCommodityOrder.getSellerUserId();
        this.buyerUserId=sxscCommodityOrder.getBuyerUserId();
        this.payStatus=0l;
        this.orderType=1l;
    }

    public SxscAliPayOrder(SxscCommodityStaticCodeOrder staticCodeOrder){
        this.totalAmount=staticCodeOrder.getTotalAmount();
        this.id=staticCodeOrder.getId();
        this.subject=staticCodeOrder.getSubject();
        this.sellerUserId=staticCodeOrder.getSellerUserId();
        this.buyerUserId=staticCodeOrder.getBuyerUserId();
        this.payStatus=0l;
        this.orderType=2l;
    }

    public SxscAliPayOrder(SxscUserConsumePurchase consumePurchase,BigDecimal amountSum){
        this.totalAmount=amountSum;
        this.id=consumePurchase.getId();
        this.subject="优惠券<规格:"+consumePurchase.getPurchaseAmount()+">("+consumePurchase.getPurchaseNum()+"张)";
        this.buyerUserId=consumePurchase.getUserId();
        this.payStatus=0l;
        this.orderType=3l;
    }

    public SxscAliPayOrder(SxscCommodity sxscCommodity){
        this.totalAmount=new BigDecimal("100");
        this.id= sxscCommodity.getPayOrderId();
        this.subject="商品保证金<名称:"+sxscCommodity.getName()+">";
        this.buyerUserId=sxscCommodity.getUserId();
        this.payStatus=0l;
        this.orderType=4l;
    }
}

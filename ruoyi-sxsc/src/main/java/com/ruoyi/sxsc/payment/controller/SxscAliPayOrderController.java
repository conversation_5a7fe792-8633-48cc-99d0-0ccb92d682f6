package com.ruoyi.sxsc.payment.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 支付宝订单信息
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/ali/payment/order")
public class SxscAliPayOrderController extends BaseController
{
    @Autowired
    private ISxscAliPayOrderService sxscAliPayOrderService;

    /**
     * 查询支付宝订单信息列表
     */
    @PreAuthorize("@ss.hasPermi('payment:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscAliPayOrder sxscAliPayOrder)
    {
        startPage();
        List<SxscAliPayOrder> list = sxscAliPayOrderService.selectSxscAliPayOrderList(sxscAliPayOrder);
        return getDataTable(list);
    }

    /**
     * 获取支付宝订单信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('payment:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscAliPayOrderService.selectSxscAliPayOrderById(id));
    }


    /**
     * 支付宝支付成功回调接口
     */
    @PostMapping(value="/ali-notify")
    public String notify(HttpServletRequest request, HttpServletResponse response) {

        return sxscAliPayOrderService.notifyData(request,response);
    }

    /**
     * 支付宝支付成功线下回调
     */
    @PostMapping(value="/ali-trends/offline/{id}")
    @PreAuthorize("@ss.hasPermi('ali:trends:notify:offline')")
    @Log(title = "支付宝支付成功线下回调", businessType = BusinessType.UPDATE)
    public AjaxResult trendsNotifyOffline(@PathVariable("id") String id) {

        SxscAliPayOrder payment=sxscAliPayOrderService.selectSxscAliPayOrderById(id);

        sxscAliPayOrderService.call("TRADE_SUCCESS",id,payment.getTotalAmount().toString(),"手动回调");
        return AjaxResult.success();
    }

}

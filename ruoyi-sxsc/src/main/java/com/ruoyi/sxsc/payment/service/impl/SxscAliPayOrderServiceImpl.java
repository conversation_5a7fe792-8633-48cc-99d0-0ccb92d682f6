package com.ruoyi.sxsc.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityStaticCodeOrderService;
import com.ruoyi.sxsc.config.AliPayConfig;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumePurchaseService;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import com.ruoyi.sxsc.payment.mapper.SxscAliPayOrderMapper;
import com.ruoyi.sxsc.payment.mapper.SxscAliPayWithdrawalMapper;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import com.ruoyi.sxsc.utils.AliPayUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * 支付宝订单信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Slf4j
@Service
public class SxscAliPayOrderServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscAliPayOrderMapper,SxscAliPayOrder> implements ISxscAliPayOrderService
{

    @Autowired
    AliPayUtils aliPayUtils;

    @Autowired
    AliPayConfig aliPayConfig;

    @Autowired
    ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    ISxscCommodityStaticCodeOrderService iSxscCommodityStaticCodeOrderService;

    @Autowired
    SxscAliPayOrderMapper sxscAliPayOrderMapper;

    @Autowired
    ISxscUserConsumePurchaseService iSxscUserConsumePurchaseService;

    @Autowired
    ISxscCommodityService iSxscCommodityService;

    /**
     * 查询支付宝订单信息
     * 
     * @param id 支付宝订单信息主键
     * @return 支付宝订单信息
     */
    @Override
    public SxscAliPayOrder selectSxscAliPayOrderById(String id)
    {
        return getById(id);
    }

    /**
     * 查询支付宝订单支付总金额
     * @param month 月份
     * @param userId 用户主键
     * @return 支付宝订单信息
     */
    @Override
    public BigDecimal aliPaySum(String month,Long userId){
        return sxscAliPayOrderMapper.aliPaySum(month,userId);
    }


    /**
     * 查询支付宝订单信息列表
     * 
     * @param sxscAliPayOrder 支付宝订单信息
     * @return 支付宝订单信息
     */
    @Override
    public List<SxscAliPayOrder> selectSxscAliPayOrderList(SxscAliPayOrder sxscAliPayOrder)
    {
        LambdaQueryWrapper<SxscAliPayOrder> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscAliPayOrder.getId()),SxscAliPayOrder::getId,sxscAliPayOrder.getId());

        wrapper.like(StringUtils.isNotNull(sxscAliPayOrder.getSubject()),SxscAliPayOrder::getSubject,sxscAliPayOrder.getSubject());

        wrapper.eq(StringUtils.isNotNull(sxscAliPayOrder.getActualTotalAmount()),SxscAliPayOrder::getActualTotalAmount,sxscAliPayOrder.getActualTotalAmount());

        wrapper.eq(StringUtils.isNotNull(sxscAliPayOrder.getSellerUserId()),SxscAliPayOrder::getSellerUserId,sxscAliPayOrder.getSellerUserId());

        wrapper.eq(StringUtils.isNotNull(sxscAliPayOrder.getBuyerUserId()),SxscAliPayOrder::getBuyerUserId,sxscAliPayOrder.getBuyerUserId());

        wrapper.eq(StringUtils.isNotNull(sxscAliPayOrder.getPayStatus()),SxscAliPayOrder::getPayStatus,sxscAliPayOrder.getPayStatus());

        wrapper.orderByDesc(SxscAliPayOrder::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增支付宝订单信息
     * 
     * @param sxscAliPayOrder 支付宝订单信息
     * @return 结果
     */
    @Override
    public String insertSxscAliPayOrder(SxscAliPayOrder sxscAliPayOrder)
    {
        SxscAliPayOrder aliPayOrder=getById(sxscAliPayOrder.getId());
        if(StringUtils.isNotNull(aliPayOrder)){
            return aliPayOrder.getResponse();
        }
        sxscAliPayOrder.setCreateBy(SecurityUtils.getUsername());
        sxscAliPayOrder.setCreateTime(DateUtils.getNowDate());
        String res=aliPayUtils.aliPayOrder(sxscAliPayOrder);
        sxscAliPayOrder.setResponse(res);
        save(sxscAliPayOrder);
        return res;
    }



    /**
     * APP支付宝支付成功后,回调该接口
     *
     * @return 结果
     */
    @Override
    public synchronized String notifyData(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> params = new HashMap<>();
        // 从支付宝回调的request域中取值
        Map<String, String[]> requestParams = request.getParameterMap();
        for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = iter.next();
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]: valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }
        for (Map.Entry<String, String> entry : params.entrySet()) {
            log.info("key:{}, value:{}",entry.getKey(),entry.getValue());
        }
        // 封装必须参数  商户订单号   订单内容    交易状态(TRADE_SUCCESS)total_amount
        String outTradeNo = request.getParameter("out_trade_no");
        String orderType = request.getParameter("body");
        String tradeStatus = request.getParameter("trade_status");
        String totalAmount = request.getParameter("total_amount");
        log.info("商户订单号:{},订单内容:{},交易状态:{}",outTradeNo,orderType,tradeStatus);
        // 签名验证(对支付宝返回的数据验证，确定是支付宝返回的)
        try {

            boolean  signVerified = AlipaySignature.rsaCertCheckV1(params, aliPayConfig.alipay_cert_path, aliPayConfig.charset, aliPayConfig.sign_type);

            if (signVerified) {
                //验签成功
                log.info("*******验签成功******");

                call(tradeStatus, outTradeNo, totalAmount, JSONObject.toJSONString(requestParams));

                return "success";
            } else {
                // 验签失败
                log.info("*******验签失败******");
                return "fail";
            }
        } catch (AlipayApiException e) {
            log.info("*******验签异常******");
            e.printStackTrace();
            return "fail";
        } catch (Exception e){
            log.info("*******业务处理异常，先返回成功******");
            e.printStackTrace();
            log.error(e.getMessage());
            return "success";
        }
    }

    /**
     * 支付成功，业务回调
     *
     * @return 结果
     */
    @Override
    public void call(String tradeStatus,String id,String totalAmount,String requestParams){
        if(tradeStatus.equals("TRADE_SUCCESS")){
            SxscAliPayOrder payment=selectSxscAliPayOrderById(id);
            if(StringUtils.isNull(payment)||payment.getPayStatus()==1){
                return;
            }
            payment.setActualTotalAmount(new BigDecimal(totalAmount));
            payment.setNotifyDate(new Date());
            payment.setPayStatus(1L);
            payment.setNotifyResponse(requestParams);
            updateById(payment);
            try {
                if(payment.getOrderType()==1){
                    iSxscCommodityOrderService.updateXgfmCommodityOrderPayment(id,payment.getActualTotalAmount());
                }else if(payment.getOrderType()==2){
                    iSxscCommodityStaticCodeOrderService.updateSxscCommodityStaticCodeOrder(id,payment.getSubject(),1L,payment.getActualTotalAmount());
                }else if(payment.getOrderType()==3){
                    iSxscUserConsumePurchaseService.updateSxscUserConsumePurchase(id,1l);
                }else if(payment.getOrderType()==4){
                    iSxscCommodityService.updateSxscCommodityPayStatus(id);
                }
            }catch (Exception e){
                e.printStackTrace();
            }

        }
    }
}

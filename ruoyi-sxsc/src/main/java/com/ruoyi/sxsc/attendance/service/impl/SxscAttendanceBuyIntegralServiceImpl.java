package com.ruoyi.sxsc.attendance.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.constant.IntegralBillConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBuy;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.attendance.mapper.SxscAttendanceBuyIntegralMapper;
import com.ruoyi.sxsc.attendance.domain.SxscAttendanceBuyIntegral;
import com.ruoyi.sxsc.attendance.service.ISxscAttendanceBuyIntegralService;

/**
 * 签到积分Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class SxscAttendanceBuyIntegralServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscAttendanceBuyIntegralMapper, SxscAttendanceBuyIntegral> implements ISxscAttendanceBuyIntegralService
{

    @Autowired
    ISxscUserConsumeBuyService iSxscUserConsumeBuyService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    @Autowired
    ISysUserService iSysUserService;



    private ThreadPoolTaskExecutor executor = SpringUtils.getBean("threadPoolTaskExecutor");

    /**
     * 查询签到积分
     * 
     * @param id 签到积分主键
     * @return 签到积分
     */
    @Override
    public SxscAttendanceBuyIntegral selectSxscUserAttendanceBuyIntegralById(String id)
    {
        return getById(id);
    }

    /**
     * 查询签到积分列表
     * 
     * @param buyIntegral 签到积分
     * @return 签到积分
     */
    @Override
    public List<SxscAttendanceBuyIntegral> selectSxscUserAttendanceBuyIntegralList(SxscAttendanceBuyIntegral buyIntegral)
    {
        LambdaQueryWrapper<SxscAttendanceBuyIntegral> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscAttendanceBuyIntegral::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(buyIntegral.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+buyIntegral.getParams().get("phonenumber")+"', '%'))");
        }

        wrapper.orderByDesc(SxscAttendanceBuyIntegral::getCreateTime);

        List<SxscAttendanceBuyIntegral> list=list(wrapper);

        for(SxscAttendanceBuyIntegral integral:list){

            integral.setSysUser(iSysUserService.selectUserMainById(integral.getUserId()));

        }
        return list;
    }

    /**
     * 新增签到积分
     * 
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscUserAttendanceBuyIntegral()
    {
        SxscAttendanceBuyIntegral buyIntegral=new SxscAttendanceBuyIntegral();
        LambdaQueryWrapper<SxscAttendanceBuyIntegral> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscAttendanceBuyIntegral::getUserId,SecurityUtils.getUserId());
        wrapper.orderByDesc(SxscAttendanceBuyIntegral::getCreateTime);
        wrapper.last("limit 1");
        SxscAttendanceBuyIntegral buyIntegralData=getOne(wrapper);
        if(StringUtils.isNotNull(buyIntegralData)){
            if(DateUtils.isToday(buyIntegralData.getCreateTime())){
                return AjaxResult.error("今日已签到完成");
            }
            buyIntegral.setDays(buyIntegralData.getDays()+1);
        }else{
            buyIntegral.setDays(1l);
        }
        buyIntegral.setId(IdUtils.fastSimpleUUID());
        buyIntegral.setUserId(SecurityUtils.getUserId());
        buyIntegral.setCreateBy(SecurityUtils.getUsername());
        buyIntegral.setCreateTime(DateUtils.getNowDate());
        save(buyIntegral);

        executor.submit(()->{
            BigDecimal integralSum=new BigDecimal("0");
            LambdaQueryWrapper<SxscUserConsumeBuy> wrapperBuy=new LambdaQueryWrapper<>();
            wrapperBuy.eq(SxscUserConsumeBuy::getStatus,1);
            wrapperBuy.eq(SxscUserConsumeBuy::getUserId,buyIntegral.getUserId());
            List<SxscUserConsumeBuy> userConsumeBuys= iSxscUserConsumeBuyService.list(wrapperBuy);
            for(SxscUserConsumeBuy consumeBuy:userConsumeBuys){
                String orderId=buyIntegral.getId();
                BigDecimal proportion=consumeBuy.getProportion();
                Long days=consumeBuy.getMortgageDays();
                BigDecimal integral=proportion.multiply(consumeBuy.getConsumeAmount()).divide(new BigDecimal(days), 2, RoundingMode.HALF_UP);
                iSxscBillIntegralService.insertSxscBillIntegral(orderId, IntegralBillConstants.ConsumeMortgage,5l,integral, consumeBuy.getUserId());
                integralSum=integralSum.add(integral);
            }
            SxscAttendanceBuyIntegral buyIntegralById=selectSxscUserAttendanceBuyIntegralById(buyIntegral.getId());
            buyIntegralById.setIntegralTyjf(integralSum);
            buyIntegralById.setUpdateTime(DateUtils.getNowDate());
            updateById(buyIntegralById);
        });
        return AjaxResult.success();
    }


}

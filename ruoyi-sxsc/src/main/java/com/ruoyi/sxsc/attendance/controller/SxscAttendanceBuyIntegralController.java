package com.ruoyi.sxsc.attendance.controller;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.attendance.domain.SxscAttendanceBuyIntegral;
import com.ruoyi.sxsc.attendance.service.ISxscAttendanceBuyIntegralService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 签到积分
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/attendance/integral")
public class SxscAttendanceBuyIntegralController extends BaseController
{
    @Autowired
    private ISxscAttendanceBuyIntegralService sxscUserAttendanceBuyIntegralService;

    /**
     * 查询签到积分列表
     */
    @PreAuthorize("@ss.hasPermi('attendance:integral:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscAttendanceBuyIntegral sxscAttendanceBuyIntegral)
    {
        startPage();
        List<SxscAttendanceBuyIntegral> list = sxscUserAttendanceBuyIntegralService.selectSxscUserAttendanceBuyIntegralList(sxscAttendanceBuyIntegral);
        return getDataTable(list);
    }


    /**
     * 获取签到积分详细信息
     */
    @PreAuthorize("@ss.hasPermi('attendance:integral:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscUserAttendanceBuyIntegralService.selectSxscUserAttendanceBuyIntegralById(id));
    }

    /**
     * 新增签到积分
     */
    @Log(title = "签到积分", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add()
    {
        return sxscUserAttendanceBuyIntegralService.insertSxscUserAttendanceBuyIntegral();
    }


}

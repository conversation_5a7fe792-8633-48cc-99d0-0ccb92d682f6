package com.ruoyi.sxsc.attendance.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.attendance.domain.SxscAttendanceBuyIntegral;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 签到积分Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ISxscAttendanceBuyIntegralService extends IService<SxscAttendanceBuyIntegral>
{
    /**
     * 查询签到积分
     * 
     * @param id 签到积分主键
     * @return 签到积分
     */
    public SxscAttendanceBuyIntegral selectSxscUserAttendanceBuyIntegralById(String id);

    /**
     * 查询签到积分列表
     * 
     * @param sxscAttendanceBuyIntegral 签到积分
     * @return 签到积分集合
     */
    public List<SxscAttendanceBuyIntegral> selectSxscUserAttendanceBuyIntegralList(SxscAttendanceBuyIntegral sxscAttendanceBuyIntegral);

    /**
     * 新增签到积分
     * 
     * @param
     * @return 结果
     */
    public AjaxResult insertSxscUserAttendanceBuyIntegral();


}

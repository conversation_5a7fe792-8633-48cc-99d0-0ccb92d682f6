package com.ruoyi.sxsc.attendance.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 签到积分对象
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscAttendanceBuyIntegral extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 天数 */
    @Excel(name = "天数")
    private Long days;

    /** 获取通用积分 */
    @Excel(name = "获取通用积分")
    private BigDecimal integralTyjf;

    @TableField(exist = false)
    private SysUserMain sysUser;


}

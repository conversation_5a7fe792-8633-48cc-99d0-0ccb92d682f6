package com.ruoyi.sxsc.consume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeGive;

import java.util.List;

/**
 * 富星卡赠送Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface ISxscUserConsumeGiveService extends IService<SxscUserConsumeGive>
{
    /**
     * 查询富星卡赠送
     * 
     * @param id 富星卡赠送主键
     * @return 富星卡赠送
     */
    SxscUserConsumeGive selectSxscUserConsumeGiveById(Long id);

    /**
     * 查询富星卡赠送列表
     * 
     * @param sxscUserConsumeGive 富星卡赠送
     * @return 富星卡赠送集合
     */
    List<SxscUserConsumeGive> selectSxscUserConsumeGiveList(SxscUserConsumeGive sxscUserConsumeGive);

    /**
     * 新增富星卡赠送
     * 
     * @param sxscUserConsumeGive 富星卡赠送
     * @return 结果
     */
    AjaxResult insertSxscUserConsumeGive(SxscUserConsumeGive sxscUserConsumeGive);

    /**
     * 修改富星卡赠送
     * 
     * @param sxscUserConsumeGive 富星卡赠送
     * @return 结果
     */
    int updateSxscUserConsumeGive(SxscUserConsumeGive sxscUserConsumeGive);

    /**
     * 批量删除富星卡赠送
     * 
     * @param ids 需要删除的富星卡赠送主键集合
     * @return 结果
     */
    int deleteSxscUserConsumeGiveByIds(Long[] ids);

    /**
     * 删除富星卡赠送信息
     * 
     * @param id 富星卡赠送主键
     * @return 结果
     */
    int deleteSxscUserConsumeGiveById(Long id);
}

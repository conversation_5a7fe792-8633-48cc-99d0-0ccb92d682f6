package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 富星卡赠送对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeGive extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 用户主键 */
    private Long userId;

    /** 只做导出使用字段：用户昵称 */
    @Excel(name = "用户昵称")
    @TableField(exist = false)
    private String nickName;

    /** 只做导出使用字段：用户手机号 */
    @Excel(name = "用户手机号")
    @TableField(exist = false)
    private String username;

    /** 富星卡卡号 */
    @Excel(name = "富星卡卡号")
    private String consumeNumber;

    /** 只做导出使用字段：赠送用户昵称 */
    @Excel(name = "赠送用户昵称")
    @TableField(exist = false)
    private String giveNickName;

    /** 只做导出使用字段：赠送用户手机号 */
    @Excel(name = "赠送用户手机号")
    @TableField(exist = false)
    private String giveUsername;

    /** 赠送后富星卡卡号 */
    @Excel(name = "赠送后富星卡卡号")
    private String giveConsumeNumber;

    /** 赠送人主键 */
    private Long giveUserId;

    /** 富星卡名称 */
    @Excel(name = "富星卡名称")
    private String consumeName;

    /** 富星卡金额 */
    @Excel(name = "富星卡金额")
    private BigDecimal consumeAmount;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 赠送用户信息 */
    @TableField(exist = false)
    private SysUserMain giveSysUser;

    /** 赠送筛选 1赠送记录  2受赠记录 */
    @TableField(exist = false)
    private Long giveFilter;

    /** 一次多张赠送：赠送数量 */
    @TableField(exist = false)
    private Long giveNumber;

}

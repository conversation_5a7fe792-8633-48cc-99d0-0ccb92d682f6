package com.ruoyi.sxsc.consume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeShare;

import java.util.List;

/**
 * 富星卡兑换Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-10
 */
public interface ISxscUserConsumeShareService extends IService<SxscUserConsumeShare>
{
    /**
     * 查询富星卡兑换
     * 
     * @param id 富星卡兑换主键
     * @return 富星卡兑换
     */
    public SxscUserConsumeShare selectSxscUserConsumeShareById(Long id);

    /**
     * 查询富星卡兑换列表
     * 
     * @param sxscUserConsumeShare 富星卡兑换
     * @return 富星卡兑换集合
     */
    public List<SxscUserConsumeShare> selectSxscUserConsumeShareList(SxscUserConsumeShare sxscUserConsumeShare);

    /**
     * 新增富星卡兑换
     * 
     * @param consumeAmount 优惠券金额
     * @return 结果
     */
    public AjaxResult insertSxscUserConsumeShare(String consumeAmount);


}

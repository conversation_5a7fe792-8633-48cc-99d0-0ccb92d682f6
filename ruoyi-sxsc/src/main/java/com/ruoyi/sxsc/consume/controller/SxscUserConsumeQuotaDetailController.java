package com.ruoyi.sxsc.consume.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeQuotaDetail;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeQuotaDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 额度明细
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
@RestController
@RequestMapping("/consumeQuota/detail")
public class SxscUserConsumeQuotaDetailController extends BaseController
{
    @Autowired
    private ISxscUserConsumeQuotaDetailService sxscUserConsumeQuotaDetailService;

    /**
     * 查询额度明细列表
     */
    @PreAuthorize("@ss.hasPermi('consumeQuota:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeQuotaDetail sxscUserConsumeQuotaDetail)
    {
        startPage();
        List<SxscUserConsumeQuotaDetail> list = sxscUserConsumeQuotaDetailService.selectSxscUserConsumeQuotaDetailList(sxscUserConsumeQuotaDetail);
        return getDataTable(list);
    }

    /**
     * 导出额度明细列表
     */
    @PreAuthorize("@ss.hasPermi('consumeQuota:detail:export')")
    @Log(title = "额度明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscUserConsumeQuotaDetail sxscUserConsumeQuotaDetail)
    {
        List<SxscUserConsumeQuotaDetail> list = sxscUserConsumeQuotaDetailService.selectSxscUserConsumeQuotaDetailList(sxscUserConsumeQuotaDetail);
        ExcelUtil<SxscUserConsumeQuotaDetail> util = new ExcelUtil<SxscUserConsumeQuotaDetail>(SxscUserConsumeQuotaDetail.class);
        util.exportExcel(response, list, "额度明细数据");
    }

    /**
     * 新增用户优惠券额度明细
     */
    @PreAuthorize("@ss.hasPermi('consumeQuota:detail:add')")
    @Log(title = "用户优惠券额度明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserConsumeQuotaDetail sxscUserConsumeQuotaDetail)
    {
        return toAjax(sxscUserConsumeQuotaDetailService.insertSxscUserConsumeQuotaDetail(sxscUserConsumeQuotaDetail));
    }

    /**
     * 获取额度明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('consumeQuota:detail:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserConsumeQuotaDetailService.selectSxscUserConsumeQuotaDetailById(id));
    }

    /**
     * 获取额度信息
     */
    @GetMapping(value = "/quota")
    public AjaxResult quota()
    {
        return success(sxscUserConsumeQuotaDetailService.quota());
    }

}

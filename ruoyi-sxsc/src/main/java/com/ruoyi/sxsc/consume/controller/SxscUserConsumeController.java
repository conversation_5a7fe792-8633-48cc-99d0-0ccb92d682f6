package com.ruoyi.sxsc.consume.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeCompound;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeSplit;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeCompoundService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeSplitService;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 优惠券信息
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/consume")
public class SxscUserConsumeController extends BaseController
{
    @Autowired
    private ISxscUserConsumeService sxscUserConsumeService;

    @Autowired
    private ISxscUserConsumeSplitService sxscUserConsumeSplitService;

    @Autowired
    private ISxscUserConsumeCompoundService sxscUserConsumeCompoundService;

    @Autowired
    ISysConfigService iSysConfigService;

    /**
     * 查询优惠券列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsume sxscUserConsume)
    {
        startPage();
        List<SxscUserConsume> list = sxscUserConsumeService.selectSxscUserConsumeList(sxscUserConsume);
        return getDataTable(list);
    }

    /**
     * 获取优惠券详细信息
     */
    @PreAuthorize("@ss.hasPermi('consume:query')")
    @GetMapping(value = "/{consumeNumber}")
    public AjaxResult getInfo(@PathVariable("consumeNumber") String consumeNumber)
    {
        return success(sxscUserConsumeService.selectSxscUserConsumeByConsumeNumber(consumeNumber));
    }



    /**
     * 统计优惠券信息
     */
    @GetMapping(value = "/statistics")
    public AjaxResult statistics()
    {
        return success(sxscUserConsumeService.consumeStatistics());
    }


    /**
     * 新增优惠券合成
     */
    @PreAuthorize("@ss.hasPermi('consume:compound:add')")
    @Log(title = "优惠券合成", businessType = BusinessType.INSERT)
    @PostMapping("/compound")
    public AjaxResult add(@RequestBody SxscUserConsumeCompound sxscUserConsumeCompound)
    {
        return sxscUserConsumeCompoundService.insertSxscUserConsumeCompound(sxscUserConsumeCompound);
    }

    /**
     * 新增优惠券拆分
     */
    @PreAuthorize("@ss.hasPermi('consume:split:add')")
    @Log(title = "优惠券拆分", businessType = BusinessType.INSERT)
    @PostMapping("/split")
    public AjaxResult add(@RequestBody SxscUserConsumeSplit sxscUserConsumeSplit)
    {
        return sxscUserConsumeSplitService.insertSxscUserConsumeSplit(sxscUserConsumeSplit);
    }


    /**
     * 删除优惠券
     */
    @PreAuthorize("@ss.hasPermi('consume:remove')")
    @DeleteMapping(value = "/{consumeNumber}")
    public AjaxResult del(@PathVariable("consumeNumber") String consumeNumber)
    {
        SxscUserConsume sxscUserConsume=new SxscUserConsume();
        sxscUserConsume.setConsumeNumber(consumeNumber);
        sxscUserConsume.setStatus(10L);
        return success(sxscUserConsumeService.updateSxscUserConsume(sxscUserConsume));
    }


}

package com.ruoyi.sxsc.consume.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumePurchase;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumePurchaseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 优惠券预购订单
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/consume/purchase")
public class SxscUserConsumePurchaseController extends BaseController
{
    @Autowired
    private ISxscUserConsumePurchaseService sxscUserConsumePurchaseService;

    @Autowired
    private ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    private ISysConfigService iSysConfigService;

    /**
     * 查询优惠券预购订单列表
     */
    @PreAuthorize("@ss.hasPermi('consume:purchase:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumePurchase sxscUserConsumePurchase)
    {
        String phone=iSysConfigService.selectConfigByKey("sxsc.user.consume.purchase.phone");
        if(!phone.contains(SecurityUtils.getUsername())){
            startPage();
        }
        List<SxscUserConsumePurchase> list = sxscUserConsumePurchaseService.selectSxscUserConsumePurchaseList(sxscUserConsumePurchase);
        return getDataTable(list);
    }

    /**
     * 导出优惠券预购订单列表
     */
    @PreAuthorize("@ss.hasPermi('consume:purchase:export')")
    @Log(title = "优惠券预购订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscUserConsumePurchase sxscUserConsumePurchase)
    {
        List<SxscUserConsumePurchase> list = sxscUserConsumePurchaseService.selectSxscUserConsumePurchaseList(sxscUserConsumePurchase);
        ExcelUtil<SxscUserConsumePurchase> util = new ExcelUtil<SxscUserConsumePurchase>(SxscUserConsumePurchase.class);
        util.exportExcel(response, list, "优惠券预购订单数据");
    }

    /**
     * 获取优惠券预购订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('consume:purchase:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscUserConsumePurchaseService.selectSxscUserConsumePurchaseById(id));
    }

    /**
     * 新增优惠券预购订单
     */
    @PreAuthorize("@ss.hasPermi('consume:purchase:add')")
    @Log(title = "优惠券预购订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserConsumePurchase sxscUserConsumePurchase)
    {
        return sxscUserConsumePurchaseService.insertSxscUserConsumePurchase(sxscUserConsumePurchase);
    }

    /**
     * 修改优惠券预购订单
     */
    @PreAuthorize("@ss.hasPermi('consume:purchase:edit')")
    @Log(title = "优惠券预购订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscUserConsumePurchase sxscUserConsumePurchase)
    {
        return toAjax(sxscUserConsumePurchaseService.updateSxscUserConsumePurchase(sxscUserConsumePurchase));
    }

    /**
     * 确认优惠券预购订单
     */
    @Log(title = "优惠券预购订单", businessType = BusinessType.UPDATE)
    @PutMapping("/confirm/{id}")
    public AjaxResult confirm(@PathVariable String id)
    {
        return sxscUserConsumePurchaseService.confirm(id);
    }

    /**
     * 删除优惠券预购订单
     */
    @PreAuthorize("@ss.hasPermi('consume:purchase:remove')")
    @Log(title = "优惠券预购订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable String id)
    {
        return sxscUserConsumePurchaseService.deleteSxscUserConsumePurchaseById(id);
    }

    /**
     * 优惠券实时单价
     */
    @GetMapping("/unitPrice")
    public AjaxResult unitPrice()
    {
        return AjaxResult.success(iSxscUserConsumeService.currentAmountOne());
    }
}

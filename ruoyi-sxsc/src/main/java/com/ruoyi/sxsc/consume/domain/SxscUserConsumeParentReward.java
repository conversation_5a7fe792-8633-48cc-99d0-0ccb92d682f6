package com.ruoyi.sxsc.consume.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 优惠券抵押父级获得奖励明细对象
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeParentReward extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 优惠券面值 */
    @Excel(name = "优惠券面值")
    private BigDecimal consumeAmount;

    /** 抵押优惠券卡号 */
    @Excel(name = "抵押优惠券卡号")
    private String consumeNumber;

    /** 获得面值比例积分 */
    @Excel(name = "获得面值比例积分")
    private BigDecimal proportion;

    /** 抵押用户主键 */
    @Excel(name = "抵押用户主键")
    private Long userId;

    /** 抵押用户父级主键 */
    @Excel(name = "抵押用户父级主键")
    private Long parentUserId;

    /** 抵押时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "抵押时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pledgeTime;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 父级用户信息 */
    @TableField(exist = false)
    private SysUserMain parentSysUser;
}

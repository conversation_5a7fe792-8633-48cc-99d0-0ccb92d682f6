package com.ruoyi.sxsc.consume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityPlate;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityPlateService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBuy;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeBuyMapper;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券抵押明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-04
 */
@Slf4j
@Service
public class SxscUserConsumeBuyServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeBuyMapper,SxscUserConsumeBuy> implements ISxscUserConsumeBuyService
{

    @Autowired
    ISxscCommodityService iSxscCommodityService;

    @Autowired
    ISxscCommodityPlateService iSxscCommodityPlateService;

    @Autowired
    ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    ISxscCommodityOrderService iSxscCommodityOrderService;


    @Autowired
    RedisCache redisCache;

    @Autowired
    ISysConfigService iSysConfigService;

    @Autowired
    ISysUserService iSysUserService;

    /**
     * 查询优惠券抵押明细
     * 
     * @param id 优惠券抵押明细主键
     * @return 优惠券抵押明细
     */
    @Override
    public SxscUserConsumeBuy selectSxscUserConsumeBuyById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询优惠券抵押明细列表
     * 
     * @param sxscUserConsumeBuy 优惠券抵押明细
     * @return 优惠券抵押明细
     */
    @Override
    public List<SxscUserConsumeBuy> selectSxscUserConsumeBuyList(SxscUserConsumeBuy sxscUserConsumeBuy)
    {
        LambdaQueryWrapper<SxscUserConsumeBuy> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsumeBuy::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserConsumeBuy.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeBuy.getParams().get("phonenumber")+"', '%'))");
        }
        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeBuy.getCommodityOrderId()),SxscUserConsumeBuy::getCommodityOrderId,sxscUserConsumeBuy.getCommodityOrderId());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeBuy.getConsumeNumber()),SxscUserConsumeBuy::getConsumeNumber,sxscUserConsumeBuy.getConsumeNumber());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeBuy.getBuyStartTime()),SxscUserConsumeBuy::getBuyStartTime,sxscUserConsumeBuy.getBuyStartTime());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeBuy.getBuyEndTime()),SxscUserConsumeBuy::getBuyEndTime,sxscUserConsumeBuy.getBuyEndTime());

        wrapper.orderByDesc(SxscUserConsumeBuy::getBuyStartTime);

        List<SxscUserConsumeBuy> list=list(wrapper);
        for(SxscUserConsumeBuy consumeBuy:list){
            consumeBuy.setSysUser(iSysUserService.selectUserMainById(consumeBuy.getUserId()));
        }
        return list;
    }

    /**
     * 校验优惠券抵押是否满足
     *
     * @param sxscCommodityOrder 订单信息
     * @param sxscCommodity 商品信息
     * @return 结果
     */
    @Override
    public void checkSxscUserConsumeBuy(SxscCommodityOrder sxscCommodityOrder,SxscCommodity sxscCommodity){

        SxscCommodityPlate commodityPlate=iSxscCommodityPlateService.getById(sxscCommodity.getPlateId());
        if(StringUtils.isNull(commodityPlate)||commodityPlate.getParentId()!=1){
            return;
        }
        if(StringUtils.isNull(commodityPlate.getConsumeAmount())||StringUtils.isNull(commodityPlate.getBuyNumber())){
            throw new ServiceException("板块信息异常，请联系系统管理员");
        }
        LambdaQueryWrapper<SxscUserConsumeBuy> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscUserConsumeBuy::getUserId,sxscCommodityOrder.getBuyerUserId());
        queryWrapper.eq(SxscUserConsumeBuy::getConsumeAmount,commodityPlate.getConsumeAmount());
        queryWrapper.eq(SxscUserConsumeBuy::getStatus,1l);
        long count=count(queryWrapper);
        if((count+sxscCommodityOrder.getNumber())>commodityPlate.getBuyNumber()){
            throw new ServiceException("超出最大抵押数量，无法结算");
        }
        long sxscUserConsumeCount=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmountCount(String.valueOf(commodityPlate.getConsumeAmount()));
        if(sxscUserConsumeCount<sxscCommodityOrder.getNumber()){
            throw new ServiceException("优惠券数量不足，无法结算");
        }
    }
    /**
     * 新增优惠券抵押明细
     *
     * @param sxscCommodityOrder 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSxscUserConsumeBuy(SxscCommodityOrder sxscCommodityOrder)
    {
        SxscCommodity sxscCommodity=iSxscCommodityService.getById(sxscCommodityOrder.getCommodityId());
        if(StringUtils.isNull(sxscCommodity)){
            return 0;
        }
        //父节点不等于1的，直接返回
        SxscCommodityPlate commodityPlate=iSxscCommodityPlateService.getById(sxscCommodity.getPlateId());
        if(StringUtils.isNull(commodityPlate)||commodityPlate.getParentId()!=1){
            return 0;
        }

        log.info("*******开始质押优惠券******");

        log.info("*******质押单号******:"+sxscCommodityOrder.getId());
        Long mortgageDays=30l;
        //优惠券抵押天数累计重置开始生效时间
        String buyStartTime=iSysConfigService.selectConfigByKey("sxsc.user.consume.buy.reset.buyStartTime");
        //查询当前人员质押复兴卡数量
        //其中,复投 10 优惠券质押时间不变,复投质押 100 优惠券每张质押时间递增+1 天复投质押 500 优惠券每张质押时间递增+2天,复投质押 1000 优惠券每张质押时间递增+3天，100卡、500卡、1000 卡的质押时间皆为满 60 天封顶。
        long buyCount=selectSxscUserConsumeBuyCount(sxscCommodityOrder.getBuyerUserId(),commodityPlate.getConsumeAmount(),buyStartTime);
        if(commodityPlate.getConsumeAmount().compareTo(new BigDecimal("100"))==0){
            mortgageDays=mortgageDays+buyCount;
        }else if(commodityPlate.getConsumeAmount().compareTo(new BigDecimal("500"))==0){
            mortgageDays=mortgageDays+buyCount*2;
        }else if(commodityPlate.getConsumeAmount().compareTo(new BigDecimal("1000"))==0){
            mortgageDays=mortgageDays+buyCount*3;
        }
        if(mortgageDays.intValue()>60){
            mortgageDays=60l;
        }
        log.info("*******质押天数******:"+mortgageDays);
        BigDecimal proportion=new BigDecimal(iSysConfigService.selectConfigByKey("sxsc.user.consume.buy.mortgageDays"));
        List<SxscUserConsume> sxscUserConsumeList=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmount(sxscCommodityOrder.getBuyerUserId(),String.valueOf(commodityPlate.getConsumeAmount()),sxscCommodityOrder.getNumber().intValue());
        log.info("*******查询实际数量******:"+sxscUserConsumeList.size());
        for(SxscUserConsume consume:sxscUserConsumeList){
            SxscUserConsumeBuy sxscUserConsumeBuy=new SxscUserConsumeBuy(sxscCommodityOrder);
            sxscUserConsumeBuy.setConsumeNumber(consume.getConsumeNumber());
            sxscUserConsumeBuy.setCreateBy(SecurityUtils.getUsername());
            sxscUserConsumeBuy.setCreateTime(DateUtils.getNowDate());
            sxscUserConsumeBuy.setConsumeAmount(consume.getConsumeAmount());
            sxscUserConsumeBuy.setMortgageDays(mortgageDays);

            sxscUserConsumeBuy.setProportion(proportion);

            consume.setStatus(2l);
            iSxscUserConsumeService.updateSxscUserConsume(consume);
            save(sxscUserConsumeBuy);
            log.info("*******质押完成******:"+sxscCommodityOrder.getId());
        }
        return 1;
    }

    /**
     * 修改优惠券抵押明细
     *
     * @param sxscUserConsumeBuy 优惠券抵押明细
     * @return 结果
     */
    @Override
    public int updateSxscUserConsumeBuy(SxscUserConsumeBuy sxscUserConsumeBuy)
    {
        sxscUserConsumeBuy.setUpdateBy("系统操作");
        sxscUserConsumeBuy.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscUserConsumeBuy)?1:0;
    }


    /**
     * 查询优惠券抵押数量
     *
     * @param userId 用户主键
     * @param consumeAmount 优惠券金额
     * @param buyStartTime 抵押开始时间
     * @return 结果
     */
    @Override
    public long selectSxscUserConsumeBuyCount(Long userId,BigDecimal consumeAmount,String buyStartTime){
        LambdaQueryWrapper<SxscUserConsumeBuy> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserConsumeBuy::getUserId,userId);
        wrapper.eq(SxscUserConsumeBuy::getConsumeAmount,consumeAmount);
        wrapper.eq(SxscUserConsumeBuy::getStatus,1l);
        wrapper.ge(StringUtils.isNotEmpty(buyStartTime),SxscUserConsumeBuy::getBuyStartTime,buyStartTime);
        return count(wrapper);
    }

    /**
     * 查询优惠券抵押信息
     *
     * @param userId 用户主键
     * @param consumeAmount 优惠券金额
     * @return 结果
     */
    @Override
    public List<SxscUserConsumeBuy> selectSxscUserConsumeBuy(Long userId, BigDecimal consumeAmount){
        LambdaQueryWrapper<SxscUserConsumeBuy> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserConsumeBuy::getUserId,userId);
        wrapper.eq(SxscUserConsumeBuy::getConsumeAmount,consumeAmount);
        wrapper.eq(SxscUserConsumeBuy::getStatus,1l);
        return list(wrapper);
    }
}

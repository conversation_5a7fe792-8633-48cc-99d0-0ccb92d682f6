package com.ruoyi.sxsc.consume.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeSystemGive;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 系统赠送优惠券Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-31
 */
public interface ISxscUserConsumeSystemGiveService extends IService<SxscUserConsumeSystemGive>
{
    /**
     * 查询系统赠送优惠券
     * 
     * @param id 系统赠送优惠券主键
     * @return 系统赠送优惠券
     */
    public SxscUserConsumeSystemGive selectSxscUserConsumeSystemGiveById(Long id);

    /**
     * 查询系统赠送优惠券列表
     * 
     * @param sxscUserConsumeSystemGive 系统赠送优惠券
     * @return 系统赠送优惠券集合
     */
    public List<SxscUserConsumeSystemGive> selectSxscUserConsumeSystemGiveList(SxscUserConsumeSystemGive sxscUserConsumeSystemGive);

    /**
     * 新增系统赠送优惠券
     * 
     * @param sxscUserConsumeSystemGive 系统赠送优惠券
     * @return 结果
     */
    public AjaxResult insertSxscUserConsumeSystemGive(SxscUserConsumeSystemGive sxscUserConsumeSystemGive);

    /**
     * 修改系统赠送优惠券
     * 
     * @param sxscUserConsumeSystemGive 系统赠送优惠券
     * @return 结果
     */
    public int updateSxscUserConsumeSystemGive(SxscUserConsumeSystemGive sxscUserConsumeSystemGive);


    /**
     * 删除系统赠送优惠券信息
     * 
     * @param id 系统赠送优惠券主键
     * @return 结果
     */
    public AjaxResult deleteSxscUserConsumeSystemGiveById(Long id);
}

package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券回购信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeBack extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    private String consumeName;

    /** 优惠券金额 */
    @Excel(name = "优惠券金额")
    private BigDecimal consumeAmount;

    /** 回购总数量 */
    @Excel(name = "回购总数量")
    private Long number;

    /** 回购总金额 */
    @Excel(name = "回购总金额")
    private BigDecimal buyBack;

    /** 审核状态0待审核1审核通过2拒绝 */
    @Excel(name = "审核状态0待审核1审核通过2拒绝")
    private Long status;

    @TableField(exist = false)
    /** 优惠券回购明细信息信息 */
    private List<SxscUserConsumeBackDetails> sxscUserConsumeBackDetailsList;

    @TableField(exist = false)
    private SysUserMain sysUser;

}

package com.ruoyi.sxsc.consume.service.impl;

import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBuy;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeParentRewardMapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeParentReward;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeParentRewardService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 优惠券抵押父级获得奖励明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */
@Service
public class SxscUserConsumeParentRewardServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeParentRewardMapper,SxscUserConsumeParentReward> implements ISxscUserConsumeParentRewardService
{


    @Autowired
    ISysConfigService iSysConfigService;

    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    ISxscUserConsumeBuyService iSxscUserConsumeBuyService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    @Autowired
    ISysUserService iSysUserService;
    /**
     * 查询优惠券抵押父级获得奖励明细
     * 
     * @param id 优惠券抵押父级获得奖励明细主键
     * @return 优惠券抵押父级获得奖励明细
     */
    @Override
    public SxscUserConsumeParentReward selectSxscUserConsumeParentRewardById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询优惠券抵押父级获得奖励明细列表
     * 
     * @param sxscUserConsumeParentReward 优惠券抵押父级获得奖励明细
     * @return 优惠券抵押父级获得奖励明细
     */
    @Override
    public List<SxscUserConsumeParentReward> selectSxscUserConsumeParentRewardList(SxscUserConsumeParentReward sxscUserConsumeParentReward)
    {
        LambdaQueryWrapper<SxscUserConsumeParentReward> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsumeParentReward::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserConsumeParentReward.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeParentReward.getParams().get("phonenumber")+"', '%'))");
        }

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeParentReward.getConsumeAmount()),SxscUserConsumeParentReward::getConsumeAmount,sxscUserConsumeParentReward.getConsumeAmount());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeParentReward.getConsumeNumber()),SxscUserConsumeParentReward::getConsumeNumber,sxscUserConsumeParentReward.getConsumeNumber());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeParentReward.getProportion()),SxscUserConsumeParentReward::getProportion,sxscUserConsumeParentReward.getProportion());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeParentReward.getPledgeTime()),SxscUserConsumeParentReward::getPledgeTime,sxscUserConsumeParentReward.getPledgeTime());

        wrapper.orderByDesc(SxscUserConsumeParentReward::getCreateTime);

        List<SxscUserConsumeParentReward> list=list(wrapper);

        for(SxscUserConsumeParentReward parentReward:list){
            parentReward.setParentSysUser(iSysUserService.selectUserMainById(parentReward.getParentUserId()));
            parentReward.setSysUser(iSysUserService.selectUserMainById(parentReward.getUserId()));
        }
        return list;
    }

    /**
     * 新增优惠券抵押父级获得奖励明细
     * 
     * @param sxscUserConsumeBuy 优惠券抵押信息
     * @return 结果
     */
    @Override
    @Transactional
    public void insertSxscUserConsumeParentReward(SxscUserConsumeBuy sxscUserConsumeBuy)
    {
        SxscUserInfo sxscUserInfo=iSxscUserInfoService.selectSxscUserInfoByUserId(sxscUserConsumeBuy.getUserId());
        if(StringUtils.isNull(sxscUserInfo)||StringUtils.isNull(sxscUserInfo.getParentId())){
            return;
        }
        //推荐人需质押相同面值的优惠券
        long buyNumber=iSxscUserConsumeBuyService.selectSxscUserConsumeBuyCount(sxscUserInfo.getParentId(),sxscUserConsumeBuy.getConsumeAmount(),null);
        if(buyNumber<1){
            return;
        }
        String proportion=iSysConfigService.selectConfigByKey("sxsc.user.consume.parent.reward.proportion");
        long multiple=sxscUserConsumeBuy.getMortgageDays()/30;
        SxscUserConsumeParentReward parentReward=new SxscUserConsumeParentReward();
        parentReward.setConsumeAmount(sxscUserConsumeBuy.getConsumeAmount());
        parentReward.setConsumeNumber(sxscUserConsumeBuy.getConsumeNumber());
        parentReward.setProportion(new BigDecimal(proportion).multiply(new BigDecimal(multiple)));
        parentReward.setUserId(sxscUserConsumeBuy.getUserId());
        parentReward.setParentUserId(sxscUserInfo.getParentId());
        parentReward.setPledgeTime(sxscUserConsumeBuy.getBuyStartTime());
        parentReward.setCreateBy(SecurityUtils.getUsername());
        parentReward.setCreateTime(DateUtils.getNowDate());
        save(parentReward);
        BigDecimal integral=parentReward.getProportion().multiply(parentReward.getConsumeAmount());
        iSxscBillIntegralService.insertSxscBillIntegral("fxk-dy-parent-qyz-"+parentReward.getId(),"推荐人优惠券抵押获得权益值",4l,integral,parentReward.getParentUserId());
    }


}

package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 优惠券回购明细信息对象 sxsc_user_consume_back_details
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeBackDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 回购主键 */
    @Excel(name = "回购主键")
    private String backId;

    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    private String consumeName;

    /** 优惠券金额 */
    @Excel(name = "优惠券金额")
    private BigDecimal consumeAmount;

    /** 优惠券卡号 */
    private String consumeNumber;

    /** 回购金额 */
    @Excel(name = "回购金额")
    private BigDecimal buyBack;


}

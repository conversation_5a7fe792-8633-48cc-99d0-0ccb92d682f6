package com.ruoyi.sxsc.consume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.constant.IntegralBillConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeSplit;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeSplitMapper;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeSplitService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券拆分Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
public class SxscUserConsumeSplitServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeSplitMapper,SxscUserConsumeSplit> implements ISxscUserConsumeSplitService
{


    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    private ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    /**
     * 查询优惠券拆分
     * 
     * @param id 优惠券拆分主键
     * @return 优惠券拆分
     */
    @Override
    public SxscUserConsumeSplit selectSxscUserConsumeSplitById(Long id)
    {
        SxscUserConsumeSplit sxscUserConsumeSplit=getById(id);

        if(sxscUserConsumeSplit.getType()==1){
            LambdaQueryWrapper<SxscUserConsume> wrapper=new LambdaQueryWrapper();

            wrapper.in(SxscUserConsume::getConsumeNumber,sxscUserConsumeSplit.getSplitConsumeNumber().split(","));

            wrapper.orderByDesc(SxscUserConsume::getCreateTime);

            sxscUserConsumeSplit.setConsumeList(iSxscUserConsumeService.list(wrapper));
        }

        return sxscUserConsumeSplit;
    }

    /**
     * 查询优惠券拆分列表
     * 
     * @param sxscUserConsumeSplit 优惠券拆分
     * @return 优惠券拆分
     */
    @Override
    public List<SxscUserConsumeSplit> selectSxscUserConsumeSplitList(SxscUserConsumeSplit sxscUserConsumeSplit)
    {
        LambdaQueryWrapper<SxscUserConsumeSplit> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsumeSplit::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserConsumeSplit.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeSplit.getParams().get("phonenumber")+"', '%'))");
        }
        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeSplit.getUserId()),SxscUserConsumeSplit::getUserId,sxscUserConsumeSplit.getUserId());

        wrapper.like(StringUtils.isNotNull(sxscUserConsumeSplit.getConsumeNumber()),SxscUserConsumeSplit::getConsumeNumber,sxscUserConsumeSplit.getConsumeNumber());

        wrapper.like(StringUtils.isNotNull(sxscUserConsumeSplit.getSplitConsumeNumber()),SxscUserConsumeSplit::getSplitConsumeNumber,sxscUserConsumeSplit.getSplitConsumeNumber());

        wrapper.orderByDesc(SxscUserConsumeSplit::getId);

        List<SxscUserConsumeSplit> list=list(wrapper);

        for(SxscUserConsumeSplit consumeSplit:list){
            consumeSplit.setSysUser(iSysUserService.selectUserMainById(consumeSplit.getUserId()));
        }

        return list;
    }

    /**
     * 新增优惠券拆分
     * 
     * @param sxscUserConsumeSplit 优惠券拆分
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertSxscUserConsumeSplit(SxscUserConsumeSplit sxscUserConsumeSplit)
    {

        if(StringUtils.isNull(sxscUserConsumeSplit.getType())){
            return AjaxResult.error("请填写拆分类型");
        }
        if(sxscUserConsumeSplit.getType()!=1){
            return AjaxResult.error("拆分类型错误");
        }
        if(StringUtils.isNull(sxscUserConsumeSplit.getSplitConsumeAmount())){
            return AjaxResult.error("请填写拆分金额");
        }
        SxscUserConsume sxscUserConsume=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmount(sxscUserConsumeSplit.getConsumeAmount());

        if(StringUtils.isNull(sxscUserConsume)){
            return AjaxResult.error("优惠券数量不足，无法拆分");
        }
        if(sxscUserConsumeSplit.getType()==1&&sxscUserConsume.getConsumeAmount().compareTo(sxscUserConsumeSplit.getSplitConsumeAmount())<=0){
            return AjaxResult.error("拆分金额不合法");
        }

        sxscUserConsumeSplit.setConsumeNumber(sxscUserConsume.getConsumeNumber());
        String splitConsumeNumber="";
        if(sxscUserConsumeSplit.getType()==1){
            for(int i=0;i<sxscUserConsume.getConsumeAmount().divide(sxscUserConsumeSplit.getSplitConsumeAmount()).intValue();i++){
                SxscUserConsume userConsume=new SxscUserConsume(sxscUserConsumeSplit);
                iSxscUserConsumeService.insertSxscUserConsume(userConsume);
                splitConsumeNumber=splitConsumeNumber+userConsume.getConsumeNumber()+",";
            }
        }else{
            sxscUserConsumeSplit.setSplitIntegralSj(sxscUserConsume.getConsumeAmount());
        }
        sxscUserConsume.setStatus(3l);
        iSxscUserConsumeService.updateSxscUserConsume(sxscUserConsume);
        sxscUserConsumeSplit.setSplitConsumeNumber(splitConsumeNumber);
        sxscUserConsumeSplit.setUserId(SecurityUtils.getUserId());
        sxscUserConsumeSplit.setConsume(new BigDecimal("0"));
        sxscUserConsumeSplit.setCreateBy(SecurityUtils.getUsername());
        sxscUserConsumeSplit.setCreateTime(DateUtils.getNowDate());
        save(sxscUserConsumeSplit);
        if(sxscUserConsumeSplit.getType()==2){
            iSxscBillIntegralService.insertSxscBillIntegral(IdUtils.fastSimpleUUID(), IntegralBillConstants.ConsumeSplit,3l,sxscUserConsume.getConsumeAmount());
        }
        return AjaxResult.success();
    }


}

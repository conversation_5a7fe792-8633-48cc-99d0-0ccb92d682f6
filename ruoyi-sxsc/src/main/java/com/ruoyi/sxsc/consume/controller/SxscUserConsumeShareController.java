package com.ruoyi.sxsc.consume.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeShare;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 富星卡兑换股权
 * 
 * <AUTHOR>
 * @date 2024-09-10
 */
@RestController
@RequestMapping("/consume/share")
public class SxscUserConsumeShareController extends BaseController
{
    @Autowired
    private ISxscUserConsumeShareService sxscUserConsumeShareService;

    /**
     * 查询富星卡兑换列表
     */
    @PreAuthorize("@ss.hasPermi('consume:share:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeShare sxscUserConsumeShare)
    {
        startPage();
        List<SxscUserConsumeShare> list = sxscUserConsumeShareService.selectSxscUserConsumeShareList(sxscUserConsumeShare);
        return getDataTable(list);
    }


    /**
     * 获取富星卡兑换详细信息
     */
    @PreAuthorize("@ss.hasPermi('consume:share:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserConsumeShareService.selectSxscUserConsumeShareById(id));
    }

    /**
     * 新增富星卡兑换股权
     */
    @PreAuthorize("@ss.hasPermi('consume:share:add')")
    @Log(title = "富星卡兑换股权", businessType = BusinessType.INSERT)
    @PostMapping("/{consumeAmount}")
    public AjaxResult add(@PathVariable String consumeAmount)
    {
        return sxscUserConsumeShareService.insertSxscUserConsumeShare(consumeAmount);
    }


}

package com.ruoyi.sxsc.consume.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 优惠券对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeModel
{


    /** 优惠券名称 */
    private String consumeName;

    /** 优惠券金额 */
    private BigDecimal consumeAmount;

    /** 可流通 */
    private Long negotiable;

    /** 冻结中 */
    private Long freeze;

    /** 当前优惠券回购价值 */
    private BigDecimal currentAmount;

    /** 一面值回购价值 */
    private BigDecimal currentOneAmount;


    public SxscUserConsumeModel(String consumeName){
        this.consumeName=consumeName;
        this.consumeAmount=new BigDecimal(consumeName);
        this.negotiable=0l;
        this.freeze=0l;
    }

}

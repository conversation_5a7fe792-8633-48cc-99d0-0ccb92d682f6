package com.ruoyi.sxsc.consume.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.ruoyi.sxsc.consume.domain.SxscUserConsumeQuotaDetail;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 额度明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface ISxscUserConsumeQuotaDetailService extends IService<SxscUserConsumeQuotaDetail>
{
    /**
     * 查询额度明细
     * 
     * @param id 额度明细主键
     * @return 额度明细
     */
    public SxscUserConsumeQuotaDetail selectSxscUserConsumeQuotaDetailById(Long id);

    /**
     * 查询额度明细列表
     * 
     * @param sxscUserConsumeQuotaDetail 额度明细
     * @return 额度明细集合
     */
    public List<SxscUserConsumeQuotaDetail> selectSxscUserConsumeQuotaDetailList(SxscUserConsumeQuotaDetail sxscUserConsumeQuotaDetail);

    /**
     * 新增额度明细
     *
     * @param userId 用户主键
     * @param quota 额度
     * @param type 额度类型1优惠券可用额度2优惠券使用额度
     * @return 结果
     */
    public int insertSxscUserConsumeQuotaDetail(Long userId, BigDecimal quota, Long type);
    /**
     * 新增用户优惠券额度明细
     *
     * @param sxscUserConsumeQuotaDetail 用户优惠券额度明细
     * @return 结果
     */
    public int insertSxscUserConsumeQuotaDetail(SxscUserConsumeQuotaDetail sxscUserConsumeQuotaDetail);
    /**
     * 查询用户优惠券额度统计截止day天数前的可用额度
     *
     * @param userId 用户主键
     * @param day 截至天数
     * @return 结果
     */
    public BigDecimal sumQuotaDetailAvailable(Long userId,Long day);


    /**
     * 查询当前用户可用额度
     *
     * @return 结果
     */
    public Map<String,BigDecimal> quota();
}

package com.ruoyi.sxsc.consume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.IntegralBillConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeShare;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeShareMapper;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeShareService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 富星卡兑换Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-10
 */
@Service
public class SxscUserConsumeShareServiceImpl extends ServiceImpl<SxscUserConsumeShareMapper,SxscUserConsumeShare> implements ISxscUserConsumeShareService
{

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    ISysConfigService iSysConfigService;

    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    SxscUserConsumeShareMapper sxscUserConsumeShareMapper;

    @Autowired
    RedisCache redisCache;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    /**
     * 查询富星卡兑换
     * 
     * @param id 富星卡兑换主键
     * @return 富星卡兑换
     */
    @Override
    public SxscUserConsumeShare selectSxscUserConsumeShareById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询富星卡兑换列表
     * 
     * @param sxscUserConsumeShare 富星卡兑换
     * @return 富星卡兑换
     */
    @Override
    public List<SxscUserConsumeShare> selectSxscUserConsumeShareList(SxscUserConsumeShare sxscUserConsumeShare)
    {
        LambdaQueryWrapper<SxscUserConsumeShare> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsumeShare::getUserId,SecurityUtils.getUserId());
        }
        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeShare.getConsumeName()),SxscUserConsumeShare::getConsumeName,sxscUserConsumeShare.getConsumeName());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeShare.getConsumeAmount()),SxscUserConsumeShare::getConsumeAmount,sxscUserConsumeShare.getConsumeAmount());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeShare.getConsumeNumber()),SxscUserConsumeShare::getConsumeNumber,sxscUserConsumeShare.getConsumeNumber());

        wrapper.like(StringUtils.isNotNull(sxscUserConsumeShare.getCreateBy()),SxscUserConsumeShare::getCreateBy,sxscUserConsumeShare.getCreateBy());

        wrapper.orderByDesc(SxscUserConsumeShare::getCreateTime);

        List<SxscUserConsumeShare> list=list(wrapper);

        for(SxscUserConsumeShare consumeShare:list){
            consumeShare.setSysUserMain(iSysUserService.selectUserMainById(consumeShare.getUserId()));
        }

        return list;
    }

    /**
     * 新增富星卡兑换
     * @param consumeAmount 优惠券金额
     * @return 结果
     */
    @Override
    public synchronized AjaxResult insertSxscUserConsumeShare(String consumeAmount)
    {
        //富星卡兑换股权开启
        String exchangeDate=iSysConfigService.selectConfigByKey("sxsc.consume.share.exchange.open");
        if(StringUtils.isEmpty(exchangeDate)||exchangeDate.equals("0")){
            return AjaxResult.error("暂未开启兑换，敬请期待");
        }
        SxscUserInfo sxscUserInfo=iSxscUserInfoService.selectSxscUserInfoByUserId(SecurityUtils.getUserId());

        if(StringUtils.isNull(sxscUserInfo)){
            return AjaxResult.error("账号异常，请联系管理员");
        }
        //按比例换算将要兑换的股权
        BigDecimal proportion=new BigDecimal(iSysConfigService.selectConfigByKey("sxsc.consume.share.exchange.proportion"));

        BigDecimal share=new BigDecimal(consumeAmount).multiply(proportion);

        SxscUserConsume sxscUserConsume=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmount(consumeAmount);

        if(StringUtils.isNull(sxscUserConsume)){
            return AjaxResult.error("暂无可兑换的富星卡");
        }
        SxscUserConsumeShare sxscUserConsumeShare=new SxscUserConsumeShare();
        sxscUserConsumeShare.setId(IdUtils.fastSimpleUUID());
        sxscUserConsumeShare.setUserId(SecurityUtils.getUserId());
        sxscUserConsumeShare.setProportion(proportion);
        sxscUserConsumeShare.setConsumeAmount(sxscUserConsume.getConsumeAmount());
        sxscUserConsumeShare.setConsumeName(sxscUserConsume.getConsumeName());
        sxscUserConsumeShare.setConsumeNumber(sxscUserConsume.getConsumeNumber());
        sxscUserConsumeShare.setCreateBy(SecurityUtils.getUsername());
        sxscUserConsumeShare.setCreateTime(DateUtils.getNowDate());
        sxscUserConsumeShare.setShare(share);
        save(sxscUserConsumeShare);
        sxscUserConsume.setStatus(8L);
        //更新富星卡状态
        iSxscUserConsumeService.updateSxscUserConsume(sxscUserConsume);
        //更新人员数据
        iSxscBillIntegralService.insertSxscBillIntegral(sxscUserConsumeShare.getId(), IntegralBillConstants.Share,7l,share);
        return AjaxResult.success();
    }


}

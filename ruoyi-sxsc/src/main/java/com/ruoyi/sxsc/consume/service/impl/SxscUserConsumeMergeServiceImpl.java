package com.ruoyi.sxsc.consume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.constant.IntegralBillConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeMerge;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeMergeMapper;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeMergeService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 优惠券合并Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
@Service
public class SxscUserConsumeMergeServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeMergeMapper,SxscUserConsumeMerge> implements ISxscUserConsumeMergeService
{

    @Autowired
    ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    @Autowired
    ISysUserService iSysUserService;


    /**
     * 查询优惠券合并
     * 
     * @param id 优惠券合并主键
     * @return 优惠券合并
     */
    @Override
    public SxscUserConsumeMerge selectSxscUserConsumeMergeById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询优惠券合并列表
     * 
     * @param sxscUserConsumeMerge 优惠券合并
     * @return 优惠券合并
     */
    @Override
    public List<SxscUserConsumeMerge> selectSxscUserConsumeMergeList(SxscUserConsumeMerge sxscUserConsumeMerge)
    {
        LambdaQueryWrapper<SxscUserConsumeMerge> wrapper=new LambdaQueryWrapper();


        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsumeMerge::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserConsumeMerge.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeMerge.getParams().get("phonenumber")+"', '%'))");
        }

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeMerge.getConsumeNumber()),SxscUserConsumeMerge::getConsumeNumber,sxscUserConsumeMerge.getConsumeNumber());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeMerge.getMergeConsumeNumber()),SxscUserConsumeMerge::getMergeConsumeNumber,sxscUserConsumeMerge.getMergeConsumeNumber());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeMerge.getMergeConsumeAmount()),SxscUserConsumeMerge::getMergeConsumeAmount,sxscUserConsumeMerge.getMergeConsumeAmount());

        wrapper.orderByDesc(SxscUserConsumeMerge::getId);

        List<SxscUserConsumeMerge> list=list(wrapper);

        for(SxscUserConsumeMerge consumeMerge:list){

            consumeMerge.setSysUser(iSysUserService.selectUserMainById(consumeMerge.getUserId()));
        }

        return list;
    }

    /**
     * 新增优惠券合并
     * 
     * @param sxscUserConsumeMerge 优惠券合并
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertSxscUserConsumeMerge(SxscUserConsumeMerge sxscUserConsumeMerge)
    {

        if(StringUtils.isNull(sxscUserConsumeMerge.getConsumeAmount())){
            return AjaxResult.error("请填写合并金额");
        }

        long sxscUserConsumeCount=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmountCount(sxscUserConsumeMerge.getConsumeAmount());

        List<SxscUserConsume> sxscUserConsumeList=new ArrayList<>();

        switch (sxscUserConsumeMerge.getConsumeAmount()){
            case "1":
                if(sxscUserConsumeCount<10){
                    return AjaxResult.error("优惠券不足，无法向上合并");
                }
                sxscUserConsumeMerge.setMergeConsumeAmount(new BigDecimal("10"));
                sxscUserConsumeList=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmount(SecurityUtils.getUserId(),sxscUserConsumeMerge.getConsumeAmount(),10);
                break;
            case "10":
                if(sxscUserConsumeCount<10){
                    return AjaxResult.error("优惠券不足，无法向上合并");
                }
                sxscUserConsumeMerge.setMergeConsumeAmount(new BigDecimal("100"));
                sxscUserConsumeList=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmount(SecurityUtils.getUserId(),sxscUserConsumeMerge.getConsumeAmount(),10);
                break;
            case "100":
                if(sxscUserConsumeCount<5){
                    return AjaxResult.error("优惠券不足，无法向上合并");
                }
                sxscUserConsumeMerge.setMergeConsumeAmount(new BigDecimal("500"));
                sxscUserConsumeList=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmount(SecurityUtils.getUserId(),sxscUserConsumeMerge.getConsumeAmount(),5);
                break;
            case "500":
                if(sxscUserConsumeCount<2){
                    return AjaxResult.error("优惠券不足，无法向上合并");
                }
                sxscUserConsumeMerge.setMergeConsumeAmount(new BigDecimal("1000"));
                sxscUserConsumeList=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmount(SecurityUtils.getUserId(),sxscUserConsumeMerge.getConsumeAmount(),2);
                break;
            default:
                return AjaxResult.error("合并失败，请联系管理员");
        }
        sxscUserConsumeMerge.setUserId(SecurityUtils.getUserId());

        //保存合并后的新卡
        SxscUserConsume userConsume=new SxscUserConsume(sxscUserConsumeMerge);
        iSxscUserConsumeService.insertSxscUserConsume(userConsume);

        sxscUserConsumeMerge.setMergeConsumeNumber(userConsume.getConsumeNumber());
        sxscUserConsumeMerge.setConsume(new BigDecimal("0"));

        for(SxscUserConsume sxscUserConsume:sxscUserConsumeList){

            sxscUserConsume.setStatus(5l);
            iSxscUserConsumeService.updateSxscUserConsume(sxscUserConsume);

            sxscUserConsumeMerge.setConsumeNumber(sxscUserConsume.getConsumeNumber());
            sxscUserConsumeMerge.setCreateBy(SecurityUtils.getUsername());
            sxscUserConsumeMerge.setCreateTime(DateUtils.getNowDate());
            sxscUserConsumeMerge.setId(IdUtils.fastSimpleUUID());
            save(sxscUserConsumeMerge);

        }

        return AjaxResult.success();
    }

    /**
     * 修改优惠券合并
     * 
     * @param sxscUserConsumeMerge 优惠券合并
     * @return 结果
     */
    @Override
    public int updateSxscUserConsumeMerge(SxscUserConsumeMerge sxscUserConsumeMerge)
    {
        sxscUserConsumeMerge.setUpdateBy(SecurityUtils.getUsername());
        sxscUserConsumeMerge.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscUserConsumeMerge)?1:0;
    }

    /**
     * 批量删除优惠券合并
     * 
     * @param ids 需要删除的优惠券合并主键
     * @return 结果
     */
    @Override
    public int deleteSxscUserConsumeMergeByIds(Long[] ids)
    {
        return removeByIds(Arrays.asList(ids))?1:0;
    }

    /**
     * 删除优惠券合并信息
     * 
     * @param id 优惠券合并主键
     * @return 结果
     */
    @Override
    public int deleteSxscUserConsumeMergeById(Long id)
    {
        return removeById(id)?1:0;
    }
}

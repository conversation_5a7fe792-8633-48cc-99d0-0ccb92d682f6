package com.ruoyi.sxsc.consume.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityOrderMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderRefundService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBuy;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeQuotaDetailService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionOrderService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumePurchaseMapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumePurchase;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumePurchaseService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 优惠券预购订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Service
public class SxscUserConsumePurchaseServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumePurchaseMapper,SxscUserConsumePurchase> implements ISxscUserConsumePurchaseService
{


    @Autowired
    ISxscUserCommissionService iSxscUserCommissionService;

    @Autowired
    ISxscUserCommissionOrderService iSxscUserCommissionOrderService;

    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    ISxscUserConsumeBuyService iSxscUserConsumeBuyService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    @Autowired
    ISxscUserConsumeQuotaDetailService iSxscUserConsumeQuotaDetailService;

    @Autowired
    ISxscAliPayOrderService iSxscAliPayOrderService;

    @Autowired
    ISxscCommodityOrderRefundService iSxscCommodityOrderRefundService;

    @Autowired
    SxscCommodityOrderMapper sxscCommodityOrderMapper;

    @Autowired
    ISysConfigService iSysConfigService;

    // 定义不同购买金额对应的限制规则
    private static final Map<BigDecimal, Integer> AMOUNT_LIMIT_MAP = new HashMap<>();
    static {
        AMOUNT_LIMIT_MAP.put(new BigDecimal("10.00"), 2);
        AMOUNT_LIMIT_MAP.put(new BigDecimal("100.00"), 2);
        AMOUNT_LIMIT_MAP.put(new BigDecimal("500.00"), 2);
        AMOUNT_LIMIT_MAP.put(new BigDecimal("1000.00"), 1);
    }
    /**
     * 查询优惠券预购订单
     * 
     * @param id 优惠券预购订单主键
     * @return 优惠券预购订单
     */
    @Override
    public SxscUserConsumePurchase selectSxscUserConsumePurchaseById(String id)
    {
        SxscUserConsumePurchase consumePurchase=getById(id);
        consumePurchase.setSysUser(iSysUserService.selectUserMainById(consumePurchase.getUserId()));
        consumePurchase.setBuySysUser(iSysUserService.selectUserMainById(consumePurchase.getBuyUserId()));
        return consumePurchase;
    }

    /**
     * 查询优惠券预购订单列表
     * 
     * @param sxscUserConsumePurchase 优惠券预购订单
     * @return 优惠券预购订单
     */
    @Override
    public List<SxscUserConsumePurchase> selectSxscUserConsumePurchaseList(SxscUserConsumePurchase sxscUserConsumePurchase)
    {
        LambdaQueryWrapper<SxscUserConsumePurchase> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsumePurchase::getPayStatus,1);
            wrapper.eq(SxscUserConsumePurchase::getDelFlag,0);
        }
        wrapper.eq(StringUtils.isNotNull(sxscUserConsumePurchase.getDelFlag()),SxscUserConsumePurchase::getDelFlag,sxscUserConsumePurchase.getDelFlag());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumePurchase.getId()),SxscUserConsumePurchase::getId,sxscUserConsumePurchase.getId());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumePurchase.getPurchaseAmount()),SxscUserConsumePurchase::getPurchaseAmount,sxscUserConsumePurchase.getPurchaseAmount());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumePurchase.getStatus()),SxscUserConsumePurchase::getStatus,sxscUserConsumePurchase.getStatus());

        wrapper.apply(StringUtils.isNotNull(sxscUserConsumePurchase.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumePurchase.getParams().get("phonenumber")+"', '%'))");

        wrapper.apply(StringUtils.isNotNull(sxscUserConsumePurchase.getParams().get("buyPhonenumber"))," buy_user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumePurchase.getParams().get("buyPhonenumber")+"', '%'))");

        wrapper.orderByAsc(SxscUserConsumePurchase::getCreateTime);

        List<SxscUserConsumePurchase> list=list(wrapper);

        String phone=iSysConfigService.selectConfigByKey("sxsc.user.consume.purchase.phone");

        for(SxscUserConsumePurchase consumePurchase:list){
            if(SecurityUtils.getLoginUser().getUser().getUserType().equals("00")||phone.contains(SecurityUtils.getUsername())){
                consumePurchase.setSysUser(iSysUserService.selectUserMainById(consumePurchase.getUserId()));
                consumePurchase.setBuySysUser(iSysUserService.selectUserMainById(consumePurchase.getBuyUserId()));
            }
        }
        return list;
    }

    /**
     * 新增优惠券预购订单
     * 
     * @param sxscUserConsumePurchase 优惠券预购订单
     * @return 结果
     */
    @Override
    @Transactional
    public synchronized AjaxResult insertSxscUserConsumePurchase(SxscUserConsumePurchase sxscUserConsumePurchase)
    {
        if(StringUtils.isNull(sxscUserConsumePurchase.getPurchaseAmount())){
            return AjaxResult.error("请选择预购面值");
        }
        if(StringUtils.isNull(sxscUserConsumePurchase.getPurchaseNum())){
            return AjaxResult.error("请选择预购数量");
        }
        if(StringUtils.isNull(sxscUserConsumePurchase.getPayType())){
            return AjaxResult.error("请选择支付类型");
        }
        sxscUserConsumePurchase.setUnitPrice(iSxscUserConsumeService.currentAmountOne());
        BigDecimal amountSum=sxscUserConsumePurchase.getPurchaseAmount().multiply(BigDecimal.valueOf(sxscUserConsumePurchase.getPurchaseNum())).multiply(sxscUserConsumePurchase.getUnitPrice());
        sxscUserConsumePurchase.setId(IdUtils.fastSimpleUUID());
        sxscUserConsumePurchase.setUserId(SecurityUtils.getUserId());
        sxscUserConsumePurchase.setStatus(0l);
        sxscUserConsumePurchase.setDelFlag(0l);
        sxscUserConsumePurchase.setCreateBy(SecurityUtils.getUsername());
        sxscUserConsumePurchase.setCreateTime(DateUtils.getNowDate());
        switch (sxscUserConsumePurchase.getPayType().intValue()){
            case 1:
                sxscUserConsumePurchase.setPayStatus(0l);
                String res=iSxscAliPayOrderService.insertSxscAliPayOrder(new SxscAliPayOrder(sxscUserConsumePurchase,amountSum));
                save(sxscUserConsumePurchase);
                return AjaxResult.success("操作成功",res);
            case 4:
                BigDecimal usedUserCommission=iSxscUserCommissionService.usedUserCommission(SecurityUtils.getUserId());
                if(usedUserCommission.compareTo(amountSum)<0){
                    return AjaxResult.error("佣金额不足,无法发布预购单");
                }
                sxscUserConsumePurchase.setPayStatus(1l);
                iSxscUserCommissionOrderService.insertSxscUserCommissionOrder(amountSum,sxscUserConsumePurchase.getId());
                save(sxscUserConsumePurchase);
                return AjaxResult.success();
            default:
                return AjaxResult.error("暂不支持，敬请期待");
        }
    }

    /**
     * 修改优惠券预购订单
     * 
     * @param sxscUserConsumePurchase 优惠券预购订单
     * @return 结果
     */
    @Override
    public int updateSxscUserConsumePurchase(SxscUserConsumePurchase sxscUserConsumePurchase)
    {
        sxscUserConsumePurchase.setUpdateBy(SecurityUtils.getUsername());
        sxscUserConsumePurchase.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscUserConsumePurchase)?1:0;
    }
    /**
     * 修改优惠券预购订单
     *
     * @param payStatus 支付状态0待支付1支付成功
     * @return 结果
     */
    @Override
    public void updateSxscUserConsumePurchase(String id,Long payStatus){
        LambdaUpdateWrapper<SxscUserConsumePurchase> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscUserConsumePurchase::getId,id);
        updateWrapper.set(SxscUserConsumePurchase::getPayStatus,payStatus);
        update(updateWrapper);
    }

    /**
     * 确认优惠券预购订单
     *
     * @param id 优惠券预购订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public synchronized AjaxResult confirm(String id){
        SxscUserConsumePurchase sxscUserConsumePurchase=getById(id);
        if(StringUtils.isNull(sxscUserConsumePurchase)||sxscUserConsumePurchase.getStatus()!=0||sxscUserConsumePurchase.getDelFlag()!=0){
            return AjaxResult.error("预购单不存在，无法确认卖出");
        }
        SxscUserInfo sxscUserInfo=iSxscUserInfoService.selectSxscUserInfoByUserId(SecurityUtils.getUserId());
        if(StringUtils.isNull(sxscUserInfo)){
            return AjaxResult.error("用户信息有误，请联系管理员");
        }
        if(sxscUserInfo.getNodeAcc()==1){
            return AjaxResult.error("承兑商无法操作，请联系管理员");
        }
        //总面值
        BigDecimal amountSum=sxscUserConsumePurchase.getPurchaseAmount().multiply(BigDecimal.valueOf(sxscUserConsumePurchase.getPurchaseNum())).multiply(sxscUserConsumePurchase.getUnitPrice());
        //需要额度
        BigDecimal sumQuota=sxscUserConsumePurchase.getPurchaseAmount().multiply(BigDecimal.valueOf(sxscUserConsumePurchase.getPurchaseNum()));

        String phone=iSysConfigService.selectConfigByKey("sxsc.user.consume.purchase.phone");
        //需要股权
        BigDecimal useShare=new BigDecimal("0.05");
        //需要积分
        BigDecimal useTy=new BigDecimal("0.25").multiply(sumQuota);
        //查询优惠券抵押额度
        List<SxscUserConsumeBuy> sxscUserConsumeBuys=iSxscUserConsumeBuyService.selectSxscUserConsumeBuy(SecurityUtils.getUserId(),sxscUserConsumePurchase.getPurchaseAmount());
        if(!phone.contains(SecurityUtils.getUsername())){
            if(sxscUserInfo.getIntegralTy().compareTo(useTy)<0){
                return AjaxResult.error("积分不足，无法确认卖出");
            }
            if(sxscUserInfo.getShare().compareTo(useShare)<0){
                return AjaxResult.error("GS费不足，无法确认卖出");
            }
            //可用额度
            BigDecimal sumQuotaDetailObtain=iSxscUserConsumeQuotaDetailService.sumQuotaDetailAvailable(SecurityUtils.getUserId(),9l);
            if(sumQuotaDetailObtain.compareTo(sumQuota)<0){
                return AjaxResult.error("可用转增额度不足，无法确认卖出");
            }
            if(useQuota(sxscUserConsumeBuys,sxscUserConsumePurchase.getPurchaseAmount()).intValue()<sxscUserConsumePurchase.getPurchaseNum().intValue()){
                return AjaxResult.error("可用优惠券额度不足，无法确认卖出");
            }
            String consumption=iSysConfigService.selectConfigByKey("sxsc.consume.purchase.limitation.consumption.amount");
            //总消费额
            BigDecimal totalConsumption=sxscCommodityOrderMapper.totalConsumption(SecurityUtils.getUserId());
            if(totalConsumption.compareTo(new BigDecimal(consumption))<0){
                return AjaxResult.error("优选购消费金额不足");
            }
        }
        //需要可用优惠券
        List<SxscUserConsume> userConsumeList=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmount(SecurityUtils.getUserId(),sxscUserConsumePurchase.getPurchaseAmount().toString(),sxscUserConsumePurchase.getPurchaseNum().intValue());
        if(userConsumeList.size()<sxscUserConsumePurchase.getPurchaseNum()){
            return AjaxResult.error("可用优惠券不足，无法确认卖出");
        }

        int purchaseNum=sxscUserConsumePurchase.getPurchaseNum().intValue();
        //更新可卖出抵押额度
        processPurchases(purchaseNum,sxscUserConsumePurchase.getPurchaseAmount(),sxscUserConsumeBuys);

        sxscUserConsumePurchase.setBuyUserId(SecurityUtils.getUserId());
        sxscUserConsumePurchase.setStatus(1l);
        sxscUserConsumePurchase.setDeductIntegrate(useTy);
        sxscUserConsumePurchase.setDeductShare(useShare);
        updateSxscUserConsumePurchase(sxscUserConsumePurchase);
        iSxscBillIntegralService.insertSxscBillIntegral(sxscUserConsumePurchase.getId(),"优惠券交易",5l,useTy.multiply(new BigDecimal("-1")));
        iSxscBillIntegralService.insertSxscBillIntegral(sxscUserConsumePurchase.getId(),"优惠券交易",7l,useShare.multiply(new BigDecimal("-1")));
        for(SxscUserConsume userConsume:userConsumeList){
            userConsume.setStatus(6l);
            iSxscUserConsumeService.updateSxscUserConsume(userConsume);
            iSxscUserConsumeService.insertSxscUserConsume(new SxscUserConsume(userConsume.getConsumeAmount(),sxscUserConsumePurchase.getUserId()));
        }
        //增加佣金额
        iSxscUserCommissionService.insertSxscUserCommission(SecurityUtils.getUserId(),id,amountSum,"优惠券交易");
        //增加买卡人的转增额度
        iSxscUserConsumeQuotaDetailService.insertSxscUserConsumeQuotaDetail(sxscUserConsumePurchase.getUserId(),sumQuota.multiply(new BigDecimal("2")),1l);
        //减少卖卡人的转增额度
        iSxscUserConsumeQuotaDetailService.insertSxscUserConsumeQuotaDetail(SecurityUtils.getUserId(),sumQuota.multiply(new BigDecimal("-1")),2l);

        return AjaxResult.success();
    }
    /**
     * 删除优惠券预购订单信息
     * 
     * @param id 优惠券预购订单主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteSxscUserConsumePurchaseById(String id)
    {
        SxscUserConsumePurchase sxscUserConsumePurchase=getById(id);
        if(StringUtils.isNull(sxscUserConsumePurchase)||sxscUserConsumePurchase.getStatus()!=0||sxscUserConsumePurchase.getDelFlag()!=0){
            return AjaxResult.error("预购单无法删除");
        }
        if(SecurityUtils.getUserId().intValue()!=sxscUserConsumePurchase.getUserId().intValue()){
            return AjaxResult.error("预购单无法删除");
        }
        sxscUserConsumePurchase.setDelFlag(1l);
        updateSxscUserConsumePurchase(sxscUserConsumePurchase);
        //退款
        BigDecimal amountSum=sxscUserConsumePurchase.getPurchaseAmount().multiply(BigDecimal.valueOf(sxscUserConsumePurchase.getPurchaseNum())).multiply(sxscUserConsumePurchase.getUnitPrice());
        iSxscCommodityOrderRefundService.insertSxscCommodityOrderRefund(sxscUserConsumePurchase,amountSum);
        return AjaxResult.success();
    }

    private Integer useQuota( List<SxscUserConsumeBuy> list,BigDecimal consumeAmount){
        int SellOutCount=0;
        for(SxscUserConsumeBuy sxscUserConsumeBuy:list){
            if(StringUtils.isNotNull(sxscUserConsumeBuy.getSellOut())){
                SellOutCount=SellOutCount+sxscUserConsumeBuy.getSellOut().intValue();
            }
        }
        if(new BigDecimal("10").compareTo(consumeAmount)==0){
            return list.size()*2-SellOutCount;
        }else if(new BigDecimal("100").compareTo(consumeAmount)==0){
            return list.size()*2-SellOutCount;
        }else if(new BigDecimal("500").compareTo(consumeAmount)==0){
            return list.size()*2-SellOutCount;
        }else if(new BigDecimal("1000").compareTo(consumeAmount)==0){
            return list.size()*1-SellOutCount;
        }
        return 0;
    }


    public  void processPurchases(int purchaseNum, BigDecimal purchaseAmount,List<SxscUserConsumeBuy> sxscUserConsumeBuys) {
        // 获取当前金额对应的限制值
        Integer limit = AMOUNT_LIMIT_MAP.get(purchaseAmount);
        if (limit == null) {
            throw new ServiceException("系统异常，请联系管理员");
        }

        for (SxscUserConsumeBuy userConsumeBuy : sxscUserConsumeBuys) {
            if (purchaseNum <= 0) {
                break;
            }

            // 处理null值
            Long sellOut = userConsumeBuy.getSellOut() != null ? userConsumeBuy.getSellOut() : 0L;

            if (sellOut >= limit) {
                continue;
            }

            int available = limit - sellOut.intValue();
            int allocate = Math.min(purchaseNum, available);

            userConsumeBuy.setSellOut(sellOut + allocate);
            purchaseNum -= allocate;
            iSxscUserConsumeBuyService.updateSxscUserConsumeBuy(userConsumeBuy);
        }
    }


}

package com.ruoyi.sxsc.consume.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 额度明细对象
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeQuotaDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 优惠券获取额度 */
    @Excel(name = "优惠券获取额度")
    private BigDecimal quota;

    /** 额度类型1优惠券可用额度2优惠券使用额度*/
    @Excel(name = "额度类型1优惠券可用额度2优惠券使用额度")
    private Long type;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;


}

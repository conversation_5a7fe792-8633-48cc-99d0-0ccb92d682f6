package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券拆分对象
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeSplit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 优惠券卡号 */
    @Excel(name = "优惠券卡号")
    private String consumeNumber;


    /** 类型1拆分优惠券2同比例权益值 */
    @Excel(name = "类型1拆分优惠券2同比例权益值")
    private Long type;

    /** 拆分后的卡号 */
    @Excel(name = "拆分后的卡号")
    private String splitConsumeNumber;

    /** 拆分金额 */
    @Excel(name = "拆分金额")
    private BigDecimal splitConsumeAmount;

    /** 拆分权益值 */
    @Excel(name = "拆分权益值")
    private BigDecimal splitIntegralSj;

    /** 消耗百分比 */
    @Excel(name = "消耗百分比")
    private BigDecimal consume;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 优惠券金额 */
    @TableField(exist = false)
    private String consumeAmount;

    @TableField(exist = false)
    private List<SxscUserConsume> consumeList;


}

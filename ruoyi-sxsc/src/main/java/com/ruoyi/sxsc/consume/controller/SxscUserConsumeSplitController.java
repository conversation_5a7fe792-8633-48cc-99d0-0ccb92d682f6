package com.ruoyi.sxsc.consume.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeSplit;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeSplitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 优惠券拆分
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/consume/split")
public class SxscUserConsumeSplitController extends BaseController
{
    @Autowired
    private ISxscUserConsumeSplitService sxscUserConsumeSplitService;

    /**
     * 查询优惠券拆分列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeSplit sxscUserConsumeSplit)
    {
        startPage();
        List<SxscUserConsumeSplit> list = sxscUserConsumeSplitService.selectSxscUserConsumeSplitList(sxscUserConsumeSplit);
        return getDataTable(list);
    }


    /**
     * 获取优惠券拆分详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserConsumeSplitService.selectSxscUserConsumeSplitById(id));
    }




}

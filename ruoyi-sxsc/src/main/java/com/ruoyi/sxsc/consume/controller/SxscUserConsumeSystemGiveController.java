package com.ruoyi.sxsc.consume.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeSystemGive;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeSystemGiveService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 系统赠送优惠券
 * 
 * <AUTHOR>
 * @date 2025-03-31
 */
@RestController
@RequestMapping("/consume/systemGive")
public class SxscUserConsumeSystemGiveController extends BaseController
{
    @Autowired
    private ISxscUserConsumeSystemGiveService sxscUserConsumeSystemGiveService;

    /**
     * 查询系统赠送优惠券列表
     */
    @PreAuthorize("@ss.hasPermi('consume:systemGive:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeSystemGive sxscUserConsumeSystemGive)
    {
        startPage();
        List<SxscUserConsumeSystemGive> list = sxscUserConsumeSystemGiveService.selectSxscUserConsumeSystemGiveList(sxscUserConsumeSystemGive);
        return getDataTable(list);
    }

    /**
     * 新增系统赠送优惠券
     */
    @PreAuthorize("@ss.hasPermi('consume:systemGive:add')")
    @Log(title = "系统赠送优惠券", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserConsumeSystemGive sxscUserConsumeSystemGive)
    {
        return sxscUserConsumeSystemGiveService.insertSxscUserConsumeSystemGive(sxscUserConsumeSystemGive);
    }


    /**
     * 删除系统赠送优惠券
     */
    @PreAuthorize("@ss.hasPermi('consume:systemGive:remove')")
    @Log(title = "系统赠送优惠券", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return sxscUserConsumeSystemGiveService.deleteSxscUserConsumeSystemGiveById(id);
    }
}

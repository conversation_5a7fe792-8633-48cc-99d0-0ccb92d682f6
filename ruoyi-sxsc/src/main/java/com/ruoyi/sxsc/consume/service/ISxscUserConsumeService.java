package com.ruoyi.sxsc.consume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.model.SxscUserConsumeModel;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface ISxscUserConsumeService extends IService<SxscUserConsume>
{
    /**
     * 查询优惠券
     * 
     * @param consumeNumber 优惠券主键
     * @return 优惠券
     */
    SxscUserConsume selectSxscUserConsumeByConsumeNumber(String consumeNumber);


    /**
     * 根据金额查询当前登录人可使用的优惠券，日期正序
     *
     * @param consumeAmount 优惠券金额
     * @return 优惠券
     */
    SxscUserConsume selectSxscUserConsumeByConsumeAmount(String consumeAmount);

    /**
     * 根据金额查可使用的优惠券，日期正序
     *
     * @param userId 账号主键
     * @param consumeAmount 优惠券金额
     * @param limit 数据条数
     * @return 优惠券
     */
    List<SxscUserConsume> selectSxscUserConsumeByConsumeAmount(Long userId,String consumeAmount,int limit);

    /**
     * 查询当前优惠券可用总值
     *
     * @return 优惠券
     */
    BigDecimal selectSxscUserConsumeByConsumeAmountAvailableSum();

    /**
     * 查询当前优惠券总值
     *
     * @return 优惠券
     */
    BigDecimal selectSxscUserConsumeByConsumeAmountSum();

    /**
     * 根据金额查询当前登录人可使用的优惠券，日期正序
     *
     * @param consumeAmount 优惠券金额
     * @return 优惠券
     */
    long selectSxscUserConsumeByConsumeAmountCount(String consumeAmount);

    /**
     * 统计优惠券
     *
     * @return 优惠券
     */
    List<SxscUserConsumeModel> consumeStatistics();

    /**
     * 计算优惠券一面值回购价格
     *
     * @return BigDecimal
     */
    BigDecimal currentAmountOne();
    /**
     * 查询优惠券列表
     * 
     * @param sxscUserConsume 优惠券
     * @return 优惠券集合
     */
    List<SxscUserConsume> selectSxscUserConsumeList(SxscUserConsume sxscUserConsume);
    /**
     * 查询优惠券列表
     *
     * @param userId 用户主键
     * @return 优惠券集合
     */
    List<SxscUserConsume> selectSxscUserConsumeList(Long userId, BigDecimal consumeAmount);

    /**
     * 新增优惠券
     * 
     * @param sxscUserConsume 优惠券
     * @return 结果
     */
    int insertSxscUserConsume(SxscUserConsume sxscUserConsume);

    /**
     * 新增优惠券-系统赠送
     *
     * @param userId 用户主键
     * @param consumeAmount 优惠券金额
     * @return 结果
     */
    AjaxResult insertSxscUserConsumeSystem(Long userId,BigDecimal consumeAmount);

    /**
     * 修改优惠券
     *
     * @param sxscUserConsume 优惠券
     * @return 结果
     */
    int updateSxscUserConsume(SxscUserConsume sxscUserConsume);





}

package com.ruoyi.sxsc.consume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeGive;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeGiveMapper;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeGiveService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 富星卡赠送Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
public class SxscUserConsumeGiveServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeGiveMapper,SxscUserConsumeGive> implements ISxscUserConsumeGiveService
{

    @Autowired
    ISysUserService iSysUserService;
    @Autowired
    private ISxscUserConsumeService iSxscUserConsumeService;
    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    @Autowired
    ISxscUserConsumeBuyService iSxscUserConsumeBuyService;


    /**
     * 查询富星卡赠送
     * 
     * @param id 富星卡赠送主键
     * @return 富星卡赠送
     */
    @Override
    public SxscUserConsumeGive selectSxscUserConsumeGiveById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询富星卡赠送列表
     * 
     * @param sxscUserConsumeGive 富星卡赠送
     * @return 富星卡赠送
     */
    @Override
    public List<SxscUserConsumeGive> selectSxscUserConsumeGiveList(SxscUserConsumeGive sxscUserConsumeGive)
    {
        LambdaQueryWrapper<SxscUserConsumeGive> wrapper=new LambdaQueryWrapper();

        if(StringUtils.isNotNull(sxscUserConsumeGive.getGiveFilter())){
            if(sxscUserConsumeGive.getGiveFilter()==1){
                wrapper.eq(SxscUserConsumeGive::getUserId,SecurityUtils.getUserId());
            }
            if(sxscUserConsumeGive.getGiveFilter()==2){
                wrapper.eq(SxscUserConsumeGive::getGiveUserId,SecurityUtils.getUserId());
            }
        }else{
            if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
                wrapper.eq(SxscUserConsumeGive::getUserId,SecurityUtils.getUserId());
            }
        }
        wrapper.apply(StringUtils.isNotNull(sxscUserConsumeGive.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeGive.getParams().get("phonenumber")+"', '%'))");

        wrapper.like(StringUtils.isNotNull(sxscUserConsumeGive.getConsumeNumber()),SxscUserConsumeGive::getConsumeNumber,sxscUserConsumeGive.getConsumeNumber());

        wrapper.apply(StringUtils.isNotNull(sxscUserConsumeGive.getParams().get("givePhonenumber"))," give_user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeGive.getParams().get("givePhonenumber")+"', '%'))");

        wrapper.like(StringUtils.isNotNull(sxscUserConsumeGive.getConsumeName()),SxscUserConsumeGive::getConsumeName,sxscUserConsumeGive.getConsumeName());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeGive.getConsumeAmount()),SxscUserConsumeGive::getConsumeAmount,sxscUserConsumeGive.getConsumeAmount());

        wrapper.orderByDesc(SxscUserConsumeGive::getCreateTime);

        List<SxscUserConsumeGive> list=list(wrapper);

        for(SxscUserConsumeGive userConsumeGive:list){
            userConsumeGive.setSysUser(iSysUserService.selectUserMainById(userConsumeGive.getUserId()));

            userConsumeGive.setGiveSysUser(iSysUserService.selectUserMainById(userConsumeGive.getGiveUserId()));
        }

        return list;
    }

    /**
     * 新增富星卡赠送
     * 富星卡转赠：
     * @param sxscUserConsumeGive 富星卡赠送
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertSxscUserConsumeGive(SxscUserConsumeGive sxscUserConsumeGive)
    {

        SysUser sysUser=iSysUserService.selectUserByUserName(sxscUserConsumeGive.getGiveUsername());
        if(StringUtils.isNull(sysUser)||!sysUser.getDelFlag().equals("0")){
            return AjaxResult.error("赠送账号异常，无法赠送");
        }

        SxscUserConsume sxscUserConsume=iSxscUserConsumeService.selectSxscUserConsumeByConsumeNumber(sxscUserConsumeGive.getConsumeNumber());

        if(StringUtils.isNull(sxscUserConsume)||sxscUserConsume.getStatus()!=1){
            return AjaxResult.error("优惠券不存在，无法赠送");
        }
        sxscUserConsumeGive.setGiveUserId(sysUser.getUserId());
        sxscUserConsumeGive.setGiveNumber(1l);
        sxscUserConsumeGive.setConsumeNumber(sxscUserConsume.getConsumeNumber());
        sxscUserConsumeGive.setConsumeAmount(sxscUserConsume.getConsumeAmount());
        sxscUserConsumeGive.setConsumeName(sxscUserConsume.getConsumeName());
        SxscUserConsume userConsume=new SxscUserConsume(sxscUserConsumeGive);
        iSxscUserConsumeService.insertSxscUserConsume(userConsume);
        sxscUserConsumeGive.setGiveConsumeNumber(userConsume.getConsumeNumber());
        sxscUserConsumeGive.setUserId(SecurityUtils.getUserId());
        sxscUserConsumeGive.setCreateBy(SecurityUtils.getUsername());
        sxscUserConsumeGive.setCreateTime(DateUtils.getNowDate());
        sxscUserConsumeGive.setId(IdUtils.fastSimpleUUID());
        save(sxscUserConsumeGive);
        sxscUserConsume.setStatus(9l);
        iSxscUserConsumeService.updateSxscUserConsume(sxscUserConsume);
        return AjaxResult.success();
    }

    /**
     * 修改富星卡赠送
     * 
     * @param sxscUserConsumeGive 富星卡赠送
     * @return 结果
     */
    @Override
    public int updateSxscUserConsumeGive(SxscUserConsumeGive sxscUserConsumeGive)
    {
        sxscUserConsumeGive.setUpdateBy(SecurityUtils.getUsername());
        sxscUserConsumeGive.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscUserConsumeGive)?1:0;
    }

    /**
     * 批量删除富星卡赠送
     * 
     * @param ids 需要删除的富星卡赠送主键
     * @return 结果
     */
    @Override
    public int deleteSxscUserConsumeGiveByIds(Long[] ids)
    {
        return removeByIds(Arrays.asList(ids))?1:0;
    }

    /**
     * 删除富星卡赠送信息
     * 
     * @param id 富星卡赠送主键
     * @return 结果
     */
    @Override
    public int deleteSxscUserConsumeGiveById(Long id)
    {
        return removeById(id)?1:0;
    }
}

package com.ruoyi.sxsc.consume.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeSystemGiveMapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeSystemGive;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeSystemGiveService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 系统赠送优惠券Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-31
 */
@Service
public class SxscUserConsumeSystemGiveServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeSystemGiveMapper,SxscUserConsumeSystemGive> implements ISxscUserConsumeSystemGiveService
{

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    ISxscUserConsumeService iSxscUserConsumeService;

    /**
     * 查询系统赠送优惠券
     * 
     * @param id 系统赠送优惠券主键
     * @return 系统赠送优惠券
     */
    @Override
    public SxscUserConsumeSystemGive selectSxscUserConsumeSystemGiveById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询系统赠送优惠券列表
     * 
     * @param sxscUserConsumeSystemGive 系统赠送优惠券
     * @return 系统赠送优惠券
     */
    @Override
    public List<SxscUserConsumeSystemGive> selectSxscUserConsumeSystemGiveList(SxscUserConsumeSystemGive sxscUserConsumeSystemGive)
    {
        LambdaQueryWrapper<SxscUserConsumeSystemGive> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsumeSystemGive::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserConsumeSystemGive.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeSystemGive.getParams().get("phonenumber")+"', '%'))");
        }
        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeSystemGive.getConsumeAmount()),SxscUserConsumeSystemGive::getConsumeAmount,sxscUserConsumeSystemGive.getConsumeAmount());

        wrapper.eq(SxscUserConsumeSystemGive::getDelFlag,0l);

        wrapper.like(StringUtils.isNotNull(sxscUserConsumeSystemGive.getCreateBy()),SxscUserConsumeSystemGive::getCreateBy,sxscUserConsumeSystemGive.getCreateBy());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeSystemGive.getCreateTime()),SxscUserConsumeSystemGive::getCreateTime,sxscUserConsumeSystemGive.getCreateTime());

        List<SxscUserConsumeSystemGive> list=list(wrapper);

        for(SxscUserConsumeSystemGive consumeSystemGive:list){
            consumeSystemGive.setSysUser(iSysUserService.selectUserMainById(consumeSystemGive.getUserId()));
        }

        return list;
    }

    /**
     * 新增系统赠送优惠券
     * 
     * @param systemGive 系统赠送优惠券
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertSxscUserConsumeSystemGive(SxscUserConsumeSystemGive systemGive)
    {
        SysUser sysUser=iSysUserService.selectUserByUserName(systemGive.getPhonenumber());
        if(StringUtils.isNull(sysUser)){
            return AjaxResult.error("手机号有误，无法赠送");
        }
        systemGive.setUserId(sysUser.getUserId());
        for(int i=0;i<systemGive.getNumberCount();i++){
            iSxscUserConsumeService.insertSxscUserConsumeSystem(sysUser.getUserId(),new BigDecimal(systemGive.getConsumeAmount()));
        }
        systemGive.setDelFlag(0l);
        systemGive.setCreateBy(SecurityUtils.getUsername());
        systemGive.setCreateTime(DateUtils.getNowDate());
        save(systemGive);
        return AjaxResult.success();
    }

    /**
     * 修改系统赠送优惠券
     * 
     * @param sxscUserConsumeSystemGive 系统赠送优惠券
     * @return 结果
     */
    @Override
    public int updateSxscUserConsumeSystemGive(SxscUserConsumeSystemGive sxscUserConsumeSystemGive)
    {
        sxscUserConsumeSystemGive.setUpdateBy(SecurityUtils.getUsername());
        sxscUserConsumeSystemGive.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscUserConsumeSystemGive)?1:0;
    }



    /**
     * 删除系统赠送优惠券信息
     * 
     * @param id 系统赠送优惠券主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteSxscUserConsumeSystemGiveById(Long id)
    {
        SxscUserConsumeSystemGive systemGive=getById(id);
        if(StringUtils.isNull(systemGive)||systemGive.getDelFlag()==1){
            return AjaxResult.error("数据不存在");
        }
        List<SxscUserConsume> consumeList=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmount(systemGive.getUserId(), String.valueOf(systemGive.getConsumeAmount()),systemGive.getNumberCount().intValue());
        if(consumeList.size()<systemGive.getNumberCount().intValue()){
            return AjaxResult.error("用户优惠券不足");
        }
        iSxscUserConsumeService.removeByIds(consumeList.stream().map(SxscUserConsume::getConsumeNumber).collect(Collectors.toList()));
        LambdaUpdateWrapper<SxscUserConsumeSystemGive> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserConsumeSystemGive::getDelFlag,1l);
        updateWrapper.eq(SxscUserConsumeSystemGive::getId,id);
        updateWrapper.set(SxscUserConsumeSystemGive::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.set(SxscUserConsumeSystemGive::getUpdateTime,DateUtils.getNowDate());
        update(updateWrapper);
        return AjaxResult.success();
    }
}

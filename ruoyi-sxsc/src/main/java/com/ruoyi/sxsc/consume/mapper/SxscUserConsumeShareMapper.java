package com.ruoyi.sxsc.consume.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeShare;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 富星卡兑换Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-10
 */
public interface SxscUserConsumeShareMapper extends BaseMapper<SxscUserConsumeShare>
{

    /**
     * 查询兑换总股权
     * @param  createTime 兑换时间
     * @param  userId 账号主键
     * @return  总额
     */
    @Select("<script>"+
            "select IFNULL(sum(share), 0) from sxsc_user_consume_share " +
            "where 1=1 " +
            "<if test='createTime != null '> and DATE_FORMAT(create_time,'%Y-%m') = date_format(#{createTime},'%Y-%m') </if> " +
            "<if test='userId != null '>  and  user_id=#{userId} </if>"+
            "</script>")
    BigDecimal sumConsumeShare( @Param("userId") Long userId,@Param("createTime") Date createTime);


}

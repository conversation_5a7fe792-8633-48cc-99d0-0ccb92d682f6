package com.ruoyi.sxsc.consume.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeMerge;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeMergeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 优惠券合并
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
@RestController
@RequestMapping("/consume/merge")
public class SxscUserConsumeMergeController extends BaseController
{
    @Autowired
    private ISxscUserConsumeMergeService sxscUserConsumeMergeService;

    /**
     * 查询优惠券合并列表
     */
    @PreAuthorize("@ss.hasPermi('consume:merge:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeMerge sxscUserConsumeMerge)
    {
        startPage();
        List<SxscUserConsumeMerge> list = sxscUserConsumeMergeService.selectSxscUserConsumeMergeList(sxscUserConsumeMerge);
        return getDataTable(list);
    }

    /**
     * 获取优惠券合并详细信息
     */
    @PreAuthorize("@ss.hasPermi('consume:merge:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserConsumeMergeService.selectSxscUserConsumeMergeById(id));
    }

    /**
     * 新增优惠券合并
     */
    @PreAuthorize("@ss.hasPermi('consume:merge:add')")
    @Log(title = "优惠券合并", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserConsumeMerge sxscUserConsumeMerge)
    {
        return sxscUserConsumeMergeService.insertSxscUserConsumeMerge(sxscUserConsumeMerge);
    }

    
}

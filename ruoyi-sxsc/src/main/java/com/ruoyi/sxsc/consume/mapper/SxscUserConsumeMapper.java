package com.ruoyi.sxsc.consume.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.model.SxscUserConsumeModel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface SxscUserConsumeMapper extends BaseMapper<SxscUserConsume>
{

    @Select("<script>"+
            "select consume_name,consume_amount," +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS negotiable," +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) AS freeze     " +
            "from sxsc_user_consume " +
            "<if test='userId != null '> where  user_id=#{userId} </if>"+
            "group by consume_name " +
            "order by consume_amount asc"+
            "</script>")
    List<SxscUserConsumeModel> consumeCountByStatus( @Param("userId")Long userId);


    /**
     * 查询购物卡总金额
     * @param  status 购物卡状态
     * @param  userId 账号主键
     * @return  总额
     */
    @Select("<script>"+
            "select IFNULL(sum(consume_amount), 0) from sxsc_user_consume " +
            "where status=#{status} " +
            "<if test='userId != null '> and  user_id=#{userId} </if>"+
            "</script>")
    BigDecimal sumConsumeAmount(@Param("status")Long status,@Param("userId")Long userId);


    /**
     * 查询购物卡总金额
     * @param  status 购物卡状态
     * @param  userId 账号主键
     * @return  总额
     */
    @Select("<script>"+
            "select IFNULL(sum(consume_amount), 0) from sxsc_user_consume " +
            "where status=#{status} " +
            "<if test='userIds != null and userIds.size() > 0'> " +
            "   and user_id in " +
            "   <foreach collection='userIds' item='id' open='(' separator=',' close=')'>" +
            "       #{id}" +
            "   </foreach>" +
            "</if>" +
            "</script>")
    BigDecimal sumConsumeAmountByUserIds(@Param("status")Long status,@Param("userIds")long[] userIds);

}

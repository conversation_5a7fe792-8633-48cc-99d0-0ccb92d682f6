package com.ruoyi.sxsc.consume.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeQuotaDetailMapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeQuotaDetail;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeQuotaDetailService;

/**
 * 额度明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
@Service
public class SxscUserConsumeQuotaDetailServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeQuotaDetailMapper,SxscUserConsumeQuotaDetail> implements ISxscUserConsumeQuotaDetailService
{

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    private SxscUserConsumeQuotaDetailMapper sxscUserConsumeQuotaDetailMapper;

    /**
     * 查询额度明细
     * 
     * @param id 额度明细主键
     * @return 额度明细
     */
    @Override
    public SxscUserConsumeQuotaDetail selectSxscUserConsumeQuotaDetailById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询额度明细列表
     * 
     * @param sxscUserConsumeQuotaDetail 额度明细
     * @return 额度明细
     */
    @Override
    public List<SxscUserConsumeQuotaDetail> selectSxscUserConsumeQuotaDetailList(SxscUserConsumeQuotaDetail sxscUserConsumeQuotaDetail)
    {
        LambdaQueryWrapper<SxscUserConsumeQuotaDetail> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeQuotaDetail.getUserId()),SxscUserConsumeQuotaDetail::getUserId,sxscUserConsumeQuotaDetail.getUserId());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeQuotaDetail.getQuota()),SxscUserConsumeQuotaDetail::getQuota,sxscUserConsumeQuotaDetail.getQuota());

        wrapper.orderByDesc(SxscUserConsumeQuotaDetail::getCreateTime);

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeQuotaDetail.getType()),SxscUserConsumeQuotaDetail::getType,sxscUserConsumeQuotaDetail.getType());

        wrapper.apply(StringUtils.isNotNull(sxscUserConsumeQuotaDetail.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeQuotaDetail.getParams().get("phonenumber")+"', '%'))");

        List<SxscUserConsumeQuotaDetail> list=list(wrapper);

        for(SxscUserConsumeQuotaDetail quotaDetail:list){
            quotaDetail.setSysUser(iSysUserService.selectUserMainById(quotaDetail.getUserId()));
        }

        return list;
    }

    /**
     * 新增额度明细
     * 
     * @param userId 用户主键
     * @param quota 额度
     * @param type 额度类型
     * @return 结果
     */
    @Override
    public int insertSxscUserConsumeQuotaDetail(Long userId, BigDecimal quota,Long type)
    {
        SxscUserConsumeQuotaDetail sxscUserConsumeQuotaDetail=new SxscUserConsumeQuotaDetail();
        sxscUserConsumeQuotaDetail.setQuota(quota);
        sxscUserConsumeQuotaDetail.setUserId(userId);
        sxscUserConsumeQuotaDetail.setType(type);
        sxscUserConsumeQuotaDetail.setCreateBy(SecurityUtils.getUsername());
        sxscUserConsumeQuotaDetail.setCreateTime(DateUtils.getNowDate());
        return save(sxscUserConsumeQuotaDetail)?1:0;
    }

    /**
     * 查询用户优惠券额度统计截止day天数前的可用额度
     *
     * @param userId 用户主键
     * @param day 截至天数
     * @return 结果
     */
    @Override
    public BigDecimal sumQuotaDetailAvailable(Long userId, Long day){
        BigDecimal sumQuotaDetailObtain=sxscUserConsumeQuotaDetailMapper.sumQuotaDetailObtain(userId,1l,day);
        BigDecimal sumQuotaDetailUse=sxscUserConsumeQuotaDetailMapper.sumQuotaDetailUse(userId,2l);
        return sumQuotaDetailObtain.add(sumQuotaDetailUse);
    }

    /**
     * 查询当前用户可用额度
     *
     * @return 结果
     */
    @Override
    public Map<String,BigDecimal> quota(){
        Map<String,BigDecimal> map=new HashMap<>();
        BigDecimal sumQuotaDetailObtain=sxscUserConsumeQuotaDetailMapper.sumQuotaDetailObtain(SecurityUtils.getUserId(),1l,-1l);
        BigDecimal sumQuotaDetailUse=sxscUserConsumeQuotaDetailMapper.sumQuotaDetailUse(SecurityUtils.getUserId(),2l);
        map.put("sumQuotaDetailObtain",sumQuotaDetailObtain.add(sumQuotaDetailUse));
        map.put("sumQuotaDetailUse",sumQuotaDetailUse.multiply(new BigDecimal("-1")));
        return map;
    }


    /**
     * 新增用户优惠券额度明细
     *
     * @param sxscUserConsumeQuotaDetail 用户优惠券额度明细
     * @return 结果
     */
    @Override
    public int insertSxscUserConsumeQuotaDetail(SxscUserConsumeQuotaDetail sxscUserConsumeQuotaDetail)
    {
        sxscUserConsumeQuotaDetail.setCreateBy(SecurityUtils.getUsername());
        sxscUserConsumeQuotaDetail.setCreateTime(DateUtils.getNowDate());
        return save(sxscUserConsumeQuotaDetail)?1:0;
    }
}

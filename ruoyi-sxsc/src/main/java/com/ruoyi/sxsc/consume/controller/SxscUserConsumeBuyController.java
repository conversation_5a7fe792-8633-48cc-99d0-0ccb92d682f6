package com.ruoyi.sxsc.consume.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBuy;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBuyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 优惠券抵押明细
 * 
 * <AUTHOR>
 * @date 2024-11-14
 */
@RestController
@RequestMapping("/consume/buy")
public class SxscUserConsumeBuyController extends BaseController
{
    @Autowired
    private ISxscUserConsumeBuyService sxscUserConsumeBuyService;

    /**
     * 查询优惠券抵押明细列表
     */
    @PreAuthorize("@ss.hasPermi('consume:buy:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeBuy sxscUserConsumeBuy)
    {
        startPage();
        List<SxscUserConsumeBuy> list = sxscUserConsumeBuyService.selectSxscUserConsumeBuyList(sxscUserConsumeBuy);
        return getDataTable(list);
    }

    /**
     * 导出优惠券抵押明细列表
     */
    @PreAuthorize("@ss.hasPermi('consume:buy:export')")
    @Log(title = "优惠券抵押明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscUserConsumeBuy sxscUserConsumeBuy)
    {
        if(StringUtils.isNull(sxscUserConsumeBuy.getParams())||StringUtils.isNull(sxscUserConsumeBuy.getParams().get("phonenumber"))){
            throw new ServiceException("请输入用户手机号进行导出");
        }
        List<SxscUserConsumeBuy> list = sxscUserConsumeBuyService.selectSxscUserConsumeBuyList(sxscUserConsumeBuy);
        for(SxscUserConsumeBuy consumeBuy:list){
            consumeBuy.setNickName(consumeBuy.getSysUser().getNickName());
            consumeBuy.setUserName(consumeBuy.getSysUser().getUserName());
        }
        ExcelUtil<SxscUserConsumeBuy> util = new ExcelUtil<SxscUserConsumeBuy>(SxscUserConsumeBuy.class);
        util.exportExcel(response, list, "优惠券抵押明细数据");
    }

    /**
     * 获取优惠券抵押明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('consume:buy:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserConsumeBuyService.selectSxscUserConsumeBuyById(id));
    }

    /**
     * 修改优惠券抵押明细
     */
    @PreAuthorize("@ss.hasPermi('consume:buy:edit')")
    @Log(title = "优惠券抵押明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscUserConsumeBuy sxscUserConsumeBuy)
    {
        return toAjax(sxscUserConsumeBuyService.updateSxscUserConsumeBuy(sxscUserConsumeBuy));
    }

}

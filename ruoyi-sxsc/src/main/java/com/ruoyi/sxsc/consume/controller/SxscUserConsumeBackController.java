package com.ruoyi.sxsc.consume.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBack;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBackService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 优惠券回购信息
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@RestController
@RequestMapping("/consume/back")
public class SxscUserConsumeBackController extends BaseController
{
    @Autowired
    private ISxscUserConsumeBackService sxscUserConsumeBackService;

    @Autowired
    private ISxscUserConsumeService iSxscUserConsumeService;

    /**
     * 查询优惠券回购信息列表
     */
    @PreAuthorize("@ss.hasPermi('consume:back:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeBack sxscUserConsumeBack)
    {
        startPage();
        List<SxscUserConsumeBack> list = sxscUserConsumeBackService.selectSxscUserConsumeBackList(sxscUserConsumeBack);
        return getDataTable(list);
    }

    /**
     * 获取优惠券回购信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('consume:back:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscUserConsumeBackService.selectSxscUserConsumeBackById(id));
    }

    /**
     * 新增优惠券回购信息
     */
    @PreAuthorize("@ss.hasPermi('consume:back:add')")
    @Log(title = "优惠券回购信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscUserConsumeBack sxscUserConsumeBack)
    {
        return sxscUserConsumeBackService.insertSxscUserConsumeBack(sxscUserConsumeBack);
    }

    /**
     * 审核优惠券回购信息
     */
    @PreAuthorize("@ss.hasPermi('consume:back:edit')")
    @Log(title = "优惠券回购信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscUserConsumeBack sxscUserConsumeBack)
    {
        return sxscUserConsumeBackService.updateSxscUserConsumeBack(sxscUserConsumeBack);
    }
    /**
     * 优惠券回购单价
     */
    @GetMapping("/unitPrice")
    public AjaxResult unitPrice()
    {
        return AjaxResult.success(iSxscUserConsumeService.currentAmountOne());
    }

}

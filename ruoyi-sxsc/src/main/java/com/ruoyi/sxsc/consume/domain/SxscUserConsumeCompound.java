package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 优惠券合成对象
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeCompound extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 优惠券卡号 */
    @Excel(name = "优惠券卡号")
    private String consumeNumber;

    /** 积分 */
    @Excel(name = "积分")
    private BigDecimal integralTy;

    /** 贡献值 */
    @Excel(name = "贡献值")
    private BigDecimal integralGxz;

    /** 消耗百分比 */
    @Excel(name = "消耗百分比")
    private BigDecimal consume;

    /** 面值 */
    @Excel(name = "面值")
    private BigDecimal amount;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;


}

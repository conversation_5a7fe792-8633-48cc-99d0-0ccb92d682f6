package com.ruoyi.sxsc.consume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBuy;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券抵押明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-04
 */
public interface ISxscUserConsumeBuyService extends IService<SxscUserConsumeBuy>
{
    /**
     * 查询优惠券抵押明细
     * 
     * @param id 优惠券抵押明细主键
     * @return 优惠券抵押明细
     */
    SxscUserConsumeBuy selectSxscUserConsumeBuyById(Long id);

    /**
     * 查询优惠券抵押明细列表
     * 
     * @param sxscUserConsumeBuy 优惠券抵押明细
     * @return 优惠券抵押明细集合
     */
    List<SxscUserConsumeBuy> selectSxscUserConsumeBuyList(SxscUserConsumeBuy sxscUserConsumeBuy);

    /**
     * 校验优惠券抵押是否满足
     *
     * @param sxscCommodityOrder 订单信息
     * @param sxscCommodity 商品信息
     * @return 结果
     */
    void checkSxscUserConsumeBuy(SxscCommodityOrder sxscCommodityOrder, SxscCommodity sxscCommodity);

    /**
     * 新增优惠券抵押明细
     * 
     * @param sxscCommodityOrder 订单信息
     * @return 结果
     */
    int insertSxscUserConsumeBuy(SxscCommodityOrder sxscCommodityOrder);

    /**
     * 修改优惠券抵押明细
     * 
     * @param sxscUserConsumeBuy 优惠券抵押明细
     * @return 结果
     */
    int updateSxscUserConsumeBuy(SxscUserConsumeBuy sxscUserConsumeBuy);


    /**
     * 查询优惠券抵押数量
     *
     * @param userId 用户主键
     * @param consumeAmount 优惠券金额
     * @param buyStartTime 抵押开始时间之后的抵押数量，为空则查全部
     * @return 结果
     */
    long selectSxscUserConsumeBuyCount(Long userId, BigDecimal consumeAmount,String buyStartTime);

    /**
     * 查询优惠券抵押信息
     *
     * @param userId 用户主键
     * @param consumeAmount 优惠券金额
     * @return 结果
     */
    List<SxscUserConsumeBuy> selectSxscUserConsumeBuy(Long userId, BigDecimal consumeAmount);


}

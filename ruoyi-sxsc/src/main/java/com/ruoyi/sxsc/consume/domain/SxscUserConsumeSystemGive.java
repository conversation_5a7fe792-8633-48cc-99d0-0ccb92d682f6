package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 系统赠送优惠券对象
 * 
 * <AUTHOR>
 * @date 2025-03-31
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeSystemGive extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 赠送数量 */
    @Excel(name = "赠送数量")
    private Long numberCount;

    /** 赠送金额 */
    @Excel(name = "赠送金额")
    private Long consumeAmount;

    /** 是否删除 */
    private Long delFlag;

    /** 人员主键 */
    @Excel(name = "人员主键")
    private Long userId;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 用户手机号 */
    @TableField(exist = false)
    private String phonenumber;


}

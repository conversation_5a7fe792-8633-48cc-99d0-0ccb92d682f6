package com.ruoyi.sxsc.consume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillTrustFundService;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityOrderMapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeMapper;
import com.ruoyi.sxsc.consume.model.SxscUserConsumeModel;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import com.ruoyi.sxsc.seting.service.ISxscSetingParameterService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 优惠券Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
public class SxscUserConsumeServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeMapper,SxscUserConsume> implements ISxscUserConsumeService
{

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    SxscUserConsumeMapper sxscUserConsumeMapper;

    @Autowired
    ISysConfigService iSysConfigService;

    @Autowired
    RedisCache redisCache;

    @Autowired
    ISxscBillTrustFundService iSxscBillTrustFundService;

    @Autowired
    private ISxscSetingParameterService sxscSetingCapitalService;
    /**
     * 查询优惠券
     * 
     * @param consumeNumber 优惠券主键
     * @return 优惠券
     */
    @Override
    public SxscUserConsume selectSxscUserConsumeByConsumeNumber(String consumeNumber)
    {
        return getById(consumeNumber);
    }

    /**
     * 根据金额查询可使用的优惠券，日期正序
     *
     * @param consumeAmount 优惠券金额
     * @return 优惠券
     */
    public SxscUserConsume selectSxscUserConsumeByConsumeAmount(String consumeAmount){
        LambdaQueryWrapper<SxscUserConsume> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserConsume::getUserId,SecurityUtils.getUserId());
        wrapper.eq(SxscUserConsume::getConsumeAmount,consumeAmount);
        wrapper.eq(SxscUserConsume::getStatus,1l);
        wrapper.orderByAsc(SxscUserConsume::getCreateTime);
        wrapper.last(" limit 1");
        return getOne(wrapper);
    }

    /**
     * 根据金额查询当前登录人可使用的优惠券，日期正序
     *
     * @param consumeAmount 优惠券金额
     * @param limit 数据条数
     * @return 优惠券
     */
    @Override
    public List<SxscUserConsume> selectSxscUserConsumeByConsumeAmount(Long userId,String consumeAmount,int limit){
        LambdaQueryWrapper<SxscUserConsume> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserConsume::getUserId,userId);
        wrapper.eq(SxscUserConsume::getConsumeAmount,consumeAmount);
        wrapper.eq(SxscUserConsume::getStatus,1l);
        wrapper.orderByAsc(SxscUserConsume::getCreateTime);
        wrapper.last(" limit "+ limit );
        return list(wrapper);
    }

    /**
     * 查询当前优惠券总值
     *
     * @return 优惠券
     */
    @Override
    public BigDecimal selectSxscUserConsumeByConsumeAmountAvailableSum(){
        //优惠券流通中总金额
        BigDecimal consumeAmountOne=sxscUserConsumeMapper.sumConsumeAmount(1l,null);

        //优惠券冻结中总金额
        BigDecimal consumeAmountTwo=sxscUserConsumeMapper.sumConsumeAmount(2l,null);

        //优惠券总金额
        return consumeAmountOne.add(consumeAmountTwo);
    }

    /**
     * 查询当前优惠券总值
     *
     * @return 优惠券
     */
    @Override
    public BigDecimal selectSxscUserConsumeByConsumeAmountSum(){
        //优惠券流通中总金额
        BigDecimal consumeAmountOne=sxscUserConsumeMapper.sumConsumeAmount(1l,null);

        //优惠券冻结中总金额
        BigDecimal consumeAmountTwo=sxscUserConsumeMapper.sumConsumeAmount(2l,null);

        //优惠券回购总金额
        BigDecimal consumeAmountFour=sxscUserConsumeMapper.sumConsumeAmount(4l,null);

        //优惠券兑换总金额
        BigDecimal consumeAmountEight=sxscUserConsumeMapper.sumConsumeAmount(8l,null);

        //优惠券总金额
        return consumeAmountOne.add(consumeAmountTwo).add(consumeAmountFour).add(consumeAmountEight);
    }

    /**
     * 根据金额查询当前登录人可使用的优惠券，日期正序
     *
     * @param consumeAmount 优惠券金额
     * @return 优惠券
     */
    @Override
    public long selectSxscUserConsumeByConsumeAmountCount(String consumeAmount){
        LambdaQueryWrapper<SxscUserConsume> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserConsume::getUserId,SecurityUtils.getUserId());
        wrapper.eq(SxscUserConsume::getConsumeAmount,consumeAmount);
        wrapper.eq(SxscUserConsume::getStatus,1l);
        wrapper.orderByAsc(SxscUserConsume::getCreateTime);
        return count(wrapper);
    }


    /**
     * 统计优惠券
     *
     * @return 优惠券
     */
    @Override
    public List<SxscUserConsumeModel> consumeStatistics(){
        Long userId=null;
        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            userId=SecurityUtils.getUserId();
        }
        List<SxscUserConsumeModel> consumeModels=sxscUserConsumeMapper.consumeCountByStatus(userId);

        if(StringUtils.isNull(consumeModels)||consumeModels.size()==0){
            consumeModels=new ArrayList<>();
            consumeModels.add(new SxscUserConsumeModel("10"));
            consumeModels.add(new SxscUserConsumeModel("100"));
            consumeModels.add(new SxscUserConsumeModel("500"));
            consumeModels.add(new SxscUserConsumeModel("1000"));
        }

        if(consumeModels.size()<4){

            // 使用流来检查是否存在 consumeName 包含 "10" 的对象
            boolean containsConsumeNameWith10 = consumeModels.stream()
                    .anyMatch(model -> model.getConsumeName().contains("10"));
            if(!containsConsumeNameWith10){
                consumeModels.add(new SxscUserConsumeModel("10"));
            }
            // 使用流来检查是否存在 consumeName 包含 "10" 的对象
            boolean containsConsumeNameWith100 = consumeModels.stream()
                    .anyMatch(model -> model.getConsumeName().contains("100"));
            if(!containsConsumeNameWith100){
                consumeModels.add(new SxscUserConsumeModel("100"));
            }
            // 使用流来检查是否存在 consumeName 包含 "10" 的对象
            boolean containsConsumeNameWith500 = consumeModels.stream()
                    .anyMatch(model -> model.getConsumeName().contains("500"));
            if(!containsConsumeNameWith500){
                consumeModels.add(new SxscUserConsumeModel("500"));
            }
            // 使用流来检查是否存在 consumeName 包含 "10" 的对象
            boolean containsConsumeNameWith1000 = consumeModels.stream()
                    .anyMatch(model -> model.getConsumeName().contains("1000"));
            if(!containsConsumeNameWith1000){
                consumeModels.add(new SxscUserConsumeModel("1000"));
            }
        }

        BigDecimal currentAmountOne=currentAmountOne();

        for(SxscUserConsumeModel sxscUserConsumeModel:consumeModels){
            sxscUserConsumeModel.setCurrentAmount(sxscUserConsumeModel.getConsumeAmount().multiply(currentAmountOne));
            sxscUserConsumeModel.setCurrentOneAmount(currentAmountOne);
        }
        return consumeModels;
    }
    /**
     * 计算优惠券一面值回购价格
     *
     * @return BigDecimal
     */
    @Override
    public BigDecimal currentAmountOne(){
        BigDecimal consumeUnitPrice=sxscSetingCapitalService.consumeUnitPrice();
        if(StringUtils.isNotNull(consumeUnitPrice)){
            return consumeUnitPrice;
        }
        //剩余总利润
        BigDecimal bigDecimal=iSxscBillTrustFundService.totalSurplusProfit();

        BigDecimal currentAmountOne=new BigDecimal("0");

        //优惠券总值
        BigDecimal consumeAmountSum=selectSxscUserConsumeByConsumeAmountAvailableSum();

        if(consumeAmountSum.compareTo(new BigDecimal("0"))!=0){
            //一面额的估值价格=当前总估值➗优惠券总金额
            currentAmountOne=bigDecimal.divide(consumeAmountSum,2, RoundingMode.HALF_UP);
        }
        return currentAmountOne;
    }
    /**
     * 查询优惠券列表
     * 
     * @param sxscUserConsume 优惠券
     * @return 优惠券
     */
    @Override
    public List<SxscUserConsume> selectSxscUserConsumeList(SxscUserConsume sxscUserConsume)
    {
        LambdaQueryWrapper<SxscUserConsume> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsume::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserConsume.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsume.getParams().get("phonenumber")+"', '%'))");
        }
        wrapper.like(StringUtils.isNotNull(sxscUserConsume.getConsumeName()),SxscUserConsume::getConsumeName,sxscUserConsume.getConsumeName());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsume.getConsumeAmount()),SxscUserConsume::getConsumeAmount,sxscUserConsume.getConsumeAmount());

        wrapper.like(StringUtils.isNotNull(sxscUserConsume.getConsumeNumber()),SxscUserConsume::getConsumeNumber,sxscUserConsume.getConsumeNumber());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsume.getStatus()),SxscUserConsume::getStatus,sxscUserConsume.getStatus());

        wrapper.ne(SxscUserConsume::getStatus,0);

        wrapper.orderByDesc(SxscUserConsume::getCreateTime);

        List<SxscUserConsume> list=list(wrapper);

        for(SxscUserConsume userConsume:list){
            userConsume.setSysUser(iSysUserService.selectUserMainById(userConsume.getUserId()));
        }

        return list;
    }

    /**
     * 查询优惠券列表
     *
     * @param userId 用户主键
     * @return 优惠券集合
     */
    public List<SxscUserConsume> selectSxscUserConsumeList(Long userId, BigDecimal consumeAmount){
        LambdaQueryWrapper<SxscUserConsume> wrapper=new LambdaQueryWrapper();

        wrapper.eq(SxscUserConsume::getUserId,SecurityUtils.getUserId());

        wrapper.eq(SxscUserConsume::getConsumeAmount,consumeAmount);

        wrapper.eq(SxscUserConsume::getStatus,1l);

        wrapper.orderByDesc(SxscUserConsume::getCreateTime);

        return  list(wrapper);
    }

    /**
     * 新增优惠券
     * 
     * @param sxscUserConsume 优惠券
     * @return 结果
     */
    @Override
    public int insertSxscUserConsume(SxscUserConsume sxscUserConsume)
    {
        sxscUserConsume.setCreateBy(SecurityUtils.getUsername());
        sxscUserConsume.setCreateTime(DateUtils.getNowDate());
        return save(sxscUserConsume)?1:0;
    }

    /**
     * 新增优惠券-系统赠送
     *
     * @param userId 用户主键
     * @param consumeAmount 优惠券金额
     * @return 结果
     */
    @Override

    public AjaxResult insertSxscUserConsumeSystem(Long userId,BigDecimal consumeAmount){
        SxscUserConsume sxscUserConsume=new SxscUserConsume();
        sxscUserConsume.setUserId(userId);
        sxscUserConsume.setConsumeAmount(consumeAmount);
        sxscUserConsume.setConsumeName(String.valueOf(sxscUserConsume.getConsumeAmount()));
        sxscUserConsume.setConsumeNumber(IdUtils.fastSimpleUUID());
        sxscUserConsume.setStatus(1L);
        insertSxscUserConsume(sxscUserConsume);
        return AjaxResult.success();
    }

    /**
     * 修改优惠券
     *
     * @param sxscUserConsume 优惠券
     * @return 结果
     */
    @Override
    public int updateSxscUserConsume(SxscUserConsume sxscUserConsume){
        sxscUserConsume.setUpdateBy(SecurityUtils.getUsername());
        sxscUserConsume.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscUserConsume)?1:0;
    }


}

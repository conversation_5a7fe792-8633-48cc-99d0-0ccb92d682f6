package com.ruoyi.sxsc.consume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBackDetails;

import java.util.List;

/**
 * 优惠券回购明细信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface ISxscUserConsumeBackDetailsService extends IService<SxscUserConsumeBackDetails>
{
    /**
     * 查询优惠券回购明细信息
     * 
     * @param backId 优惠券回购主键
     * @return 优惠券回购明细信息
     */
    List<SxscUserConsumeBackDetails> selectSxscUserConsumeBackDetailsByBackId(String backId);


}

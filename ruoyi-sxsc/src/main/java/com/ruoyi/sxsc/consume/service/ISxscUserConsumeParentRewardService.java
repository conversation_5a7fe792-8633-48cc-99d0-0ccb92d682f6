package com.ruoyi.sxsc.consume.service;

import java.util.List;

import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBuy;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeParentReward;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 优惠券抵押父级获得奖励明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */
public interface ISxscUserConsumeParentRewardService extends IService<SxscUserConsumeParentReward>
{
    /**
     * 查询优惠券抵押父级获得奖励明细
     * 
     * @param id 优惠券抵押父级获得奖励明细主键
     * @return 优惠券抵押父级获得奖励明细
     */
    public SxscUserConsumeParentReward selectSxscUserConsumeParentRewardById(Long id);

    /**
     * 查询优惠券抵押父级获得奖励明细列表
     * 
     * @param sxscUserConsumeParentReward 优惠券抵押父级获得奖励明细
     * @return 优惠券抵押父级获得奖励明细集合
     */
    public List<SxscUserConsumeParentReward> selectSxscUserConsumeParentRewardList(SxscUserConsumeParentReward sxscUserConsumeParentReward);

    /**
     * 新增优惠券抵押父级获得奖励明细
     * 
     * @param sxscUserConsumeBuy 优惠券抵押数据
     * @return 结果
     */
    public void insertSxscUserConsumeParentReward(SxscUserConsumeBuy sxscUserConsumeBuy);


}

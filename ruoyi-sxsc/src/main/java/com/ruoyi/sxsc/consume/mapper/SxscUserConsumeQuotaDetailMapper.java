package com.ruoyi.sxsc.consume.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeQuotaDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 额度明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
public interface SxscUserConsumeQuotaDetailMapper extends BaseMapper<SxscUserConsumeQuotaDetail>
{

    /**
     * 查询用户额度
     * @param  type 额度类型
     * @param  userId 账号主键
     * @param  days 截至days天前
     * @return  总额
     */
    @Select("<script>"+
            "select IFNULL(sum(quota),0) from sxsc_user_consume_quota_detail " +
            "where  create_time  <![CDATA[  <=   ]]> DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "<if test='userId != null '>  and  user_id=#{userId} </if>"+
            "<if test='type != null '>  and  type=#{type} </if>"+
            "</script>")
    BigDecimal sumQuotaDetailObtain(@Param("userId") Long userId, @Param("type") Long type,@Param("days") Long days);



    @Select("<script>"+
            "select IFNULL(sum(quota),0) from sxsc_user_consume_quota_detail " +
            "where  1=1 " +
            "<if test='userId != null '>  and  user_id=#{userId} </if>"+
            "<if test='type != null '>  and  type=#{type} </if>"+
            "</script>")
    BigDecimal sumQuotaDetailUse(@Param("userId") Long userId, @Param("type") Long type);
}

package com.ruoyi.sxsc.consume.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeCompound;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeGive;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeSplit;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeCompoundService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeGiveService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeSplitService;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 优惠券赠送信息
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/consume/give")
public class SxscUserConsumeGiveController extends BaseController
{


    @Autowired
    private ISxscUserConsumeGiveService sxscUserConsumeGiveService;

    /**
     * 查询优惠券列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeGive sxscUserConsumeGive)
    {
        startPage();
        List<SxscUserConsumeGive> list = sxscUserConsumeGiveService.selectSxscUserConsumeGiveList(sxscUserConsumeGive);
        return getDataTable(list);
    }

    /**
     * 获取优惠券赠送详细信息
     */
    @PreAuthorize("@ss.hasPermi('consume:give:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserConsumeGiveService.selectSxscUserConsumeGiveById(id));
    }

    /**
     * 新增富星卡赠送
     */
    @PreAuthorize("@ss.hasPermi('consume:give:add')")
    @Log(title = "富星卡赠送", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody SxscUserConsumeGive sxscUserConsumeGive)
    {
        return sxscUserConsumeGiveService.insertSxscUserConsumeGive(sxscUserConsumeGive);
    }


    /**
     * 导出富星卡赠送列表
     */
    @PreAuthorize("@ss.hasPermi('consume:give:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscUserConsumeGive sxscUserConsumeGive)
    {
        List<SxscUserConsumeGive> list = sxscUserConsumeGiveService.selectSxscUserConsumeGiveList(sxscUserConsumeGive);
        for (SxscUserConsumeGive consumeGive:list){
            consumeGive.setNickName(consumeGive.getSysUser().getNickName());
            consumeGive.setUsername(consumeGive.getSysUser().getUserName());
            consumeGive.setGiveNickName(consumeGive.getGiveSysUser().getUserName());
            consumeGive.setGiveUsername(consumeGive.getGiveSysUser().getUserName());
        }
        ExcelUtil<SxscUserConsumeGive> util = new ExcelUtil<SxscUserConsumeGive>(SxscUserConsumeGive.class);
        util.exportExcel(response, list, "赠送数据");
    }


}

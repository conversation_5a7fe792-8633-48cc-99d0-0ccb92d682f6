package com.ruoyi.sxsc.consume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBack;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBackDetails;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeBackMapper;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBackDetailsService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBackService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 优惠券回购信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class SxscUserConsumeBackServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeBackMapper,SxscUserConsumeBack> implements ISxscUserConsumeBackService
{

    @Autowired
    ISxscUserConsumeBackDetailsService iSxscUserConsumeBackDetailsService;

    @Autowired
    ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    SxscUserConsumeBackMapper sxscUserConsumeBackMapper;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    ISxscUserCommissionService iSxscUserCommissionService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    /**
     * 查询优惠券回购信息
     * 
     * @param id 优惠券回购信息主键
     * @return 优惠券回购信息
     */
    @Override
    public SxscUserConsumeBack selectSxscUserConsumeBackById(String id)
    {
        SxscUserConsumeBack sxscUserConsumeBack=getById(id);
        sxscUserConsumeBack.setSxscUserConsumeBackDetailsList(iSxscUserConsumeBackDetailsService.selectSxscUserConsumeBackDetailsByBackId(id));
        sxscUserConsumeBack.setSysUser(iSysUserService.selectUserMainById(sxscUserConsumeBack.getUserId()));
        return sxscUserConsumeBack;
    }

    /**
     * 查询优惠券回购金额总数
     *
     * @return 优惠券回购信息
     */
    @Override
    public BigDecimal selectSxscUserConsumeBackTotalAmount(){
        return sxscUserConsumeBackMapper.selectSxscUserConsumeBackTotalAmount();
    }

    /**
     * 查询优惠券回购信息列表
     * 
     * @param sxscUserConsumeBack 优惠券回购信息
     * @return 优惠券回购信息
     */
    @Override
    public List<SxscUserConsumeBack> selectSxscUserConsumeBackList(SxscUserConsumeBack sxscUserConsumeBack)
    {
        LambdaQueryWrapper<SxscUserConsumeBack> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsumeBack::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserConsumeBack.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeBack.getParams().get("phonenumber")+"', '%'))");
        }
        wrapper.like(StringUtils.isNotNull(sxscUserConsumeBack.getConsumeName()),SxscUserConsumeBack::getConsumeName,sxscUserConsumeBack.getConsumeName());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeBack.getConsumeAmount()),SxscUserConsumeBack::getConsumeAmount,sxscUserConsumeBack.getConsumeAmount());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeBack.getBuyBack()),SxscUserConsumeBack::getBuyBack,sxscUserConsumeBack.getBuyBack());

        wrapper.orderByDesc(SxscUserConsumeBack::getCreateTime);
        List<SxscUserConsumeBack> list=list(wrapper);
        for(SxscUserConsumeBack consumeBack:list){
            consumeBack.setSysUser(iSysUserService.selectUserMainById(consumeBack.getUserId()));
        }
        return list;
    }

    /**
     * 新增优惠券回购信息
     * 
     * @param sxscUserConsumeBack 优惠券回购信息
     * @return 结果
     */
    @Transactional
    @Override
    public AjaxResult insertSxscUserConsumeBack(SxscUserConsumeBack sxscUserConsumeBack)
    {
        SxscUserInfo sxscUserInfo=iSxscUserInfoService.selectSxscUserInfoByUserId(SecurityUtils.getUserId());
        if(StringUtils.isNull(sxscUserInfo)){
            return AjaxResult.success("用户信息异常，请联系管理员");
        }
        sxscUserConsumeBack.setNumber(1l);
        if(StringUtils.isNull(sxscUserConsumeBack.getConsumeAmount())){
            return AjaxResult.success("请输入优惠券金额");
        }
        List<SxscUserConsume> list=iSxscUserConsumeService.selectSxscUserConsumeList(SecurityUtils.getUserId(),sxscUserConsumeBack.getConsumeAmount());
        if(list.size()<sxscUserConsumeBack.getNumber()){
            return AjaxResult.success("可回收数量不足");
        }
        BigDecimal quota=sxscUserConsumeBack.getConsumeAmount().multiply(new BigDecimal(sxscUserConsumeBack.getNumber()));
        if(sxscUserInfo.getAcceptorAmount().compareTo(quota)<0){
            return AjaxResult.success("承兑商额度不足");
        }
        //优惠券回收价格
        BigDecimal currentTotalAmountOne=iSxscUserConsumeService.currentAmountOne();
        sxscUserConsumeBack.setBuyBack(currentTotalAmountOne.multiply(BigDecimal.valueOf(sxscUserConsumeBack.getNumber())).multiply(sxscUserConsumeBack.getConsumeAmount()));
        sxscUserConsumeBack.setUserId(SecurityUtils.getUserId());
        sxscUserConsumeBack.setCreateBy(SecurityUtils.getUsername());
        sxscUserConsumeBack.setCreateTime(DateUtils.getNowDate());
        sxscUserConsumeBack.setId(IdUtils.fastSimpleUUID());
        save(sxscUserConsumeBack);
        insertSxscUserConsumeBackDetails(sxscUserConsumeBack.getNumber(),list,sxscUserConsumeBack,currentTotalAmountOne);
        iSxscBillIntegralService.insertSxscBillIntegral(sxscUserConsumeBack.getId(),"回收抵扣承兑商额度",8l,quota.multiply(new BigDecimal("-1")),SecurityUtils.getUserId());
        return AjaxResult.success();
    }


    /**
     * 新增优惠券回购信息
     *
     * @param sxscUserConsumeBack 优惠券回购信息
     * @return 结果
     */
    @Transactional
    @Override
    public AjaxResult updateSxscUserConsumeBack(SxscUserConsumeBack sxscUserConsumeBack){
        SxscUserConsumeBack consumeBackData=getById(sxscUserConsumeBack.getId());
        if(StringUtils.isNull(consumeBackData)||consumeBackData.getStatus()!=0){
            return AjaxResult.error("审核数据不存在");
        }
        if(sxscUserConsumeBack.getStatus()==1){
            iSxscUserCommissionService.insertSxscUserCommission(consumeBackData.getUserId(),consumeBackData.getId(),consumeBackData.getBuyBack(),"卡回收佣金");
        }else{
            BigDecimal quota=consumeBackData.getConsumeAmount().multiply(new BigDecimal(consumeBackData.getNumber()));
            iSxscBillIntegralService.insertSxscBillIntegral(consumeBackData.getId(),"回收抵扣承兑商额度",8l,quota,consumeBackData.getUserId());
            List<SxscUserConsumeBackDetails> userConsumeBackDetails=iSxscUserConsumeBackDetailsService.selectSxscUserConsumeBackDetailsByBackId(consumeBackData.getId());
            for (SxscUserConsumeBackDetails backDetails : userConsumeBackDetails)
            {
                SxscUserConsume sxscUserConsume=new SxscUserConsume();
                sxscUserConsume.setConsumeNumber(backDetails.getConsumeNumber());
                sxscUserConsume.setStatus(4l);
                iSxscUserConsumeService.updateSxscUserConsume(sxscUserConsume);
            }
        }
        consumeBackData.setStatus(sxscUserConsumeBack.getStatus());
        consumeBackData.setUpdateBy(SecurityUtils.getUsername());
        consumeBackData.setUpdateTime(DateUtils.getNowDate());
        updateById(consumeBackData);
        return AjaxResult.success();
    }
    /**
     * 新增优惠券回购明细信息信息
     * 
     * @param sxscUserConsumeBack 优惠券回购信息对象
     */
    public void insertSxscUserConsumeBackDetails(Long number,List<SxscUserConsume> list,SxscUserConsumeBack sxscUserConsumeBack,BigDecimal currentTotalAmountOne)
    {
        int i=0;
        List<SxscUserConsumeBackDetails> sxscUserConsumeBackDetailsList = new ArrayList<>();
        String id = sxscUserConsumeBack.getId();
        for (SxscUserConsume sxscUserConsume : list)
        {
            if(i>=number){
                break;
            }
            i++;
            SxscUserConsumeBackDetails details=new SxscUserConsumeBackDetails();
            details.setBuyBack(currentTotalAmountOne.multiply(sxscUserConsume.getConsumeAmount()));
            details.setConsumeNumber(sxscUserConsume.getConsumeNumber());
            details.setConsumeName(sxscUserConsume.getConsumeName());
            details.setConsumeAmount(sxscUserConsume.getConsumeAmount());
            details.setCreateBy(SecurityUtils.getUsername());
            details.setCreateTime(DateUtils.getNowDate());
            details.setBackId(id);
            sxscUserConsumeBackDetailsList.add(details);
            sxscUserConsume.setStatus(4l);
            iSxscUserConsumeService.updateSxscUserConsume(sxscUserConsume);
        }
        iSxscUserConsumeBackDetailsService.saveBatch(sxscUserConsumeBackDetailsList);
    }
}

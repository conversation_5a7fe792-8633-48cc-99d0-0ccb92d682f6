package com.ruoyi.sxsc.consume.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeParentReward;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeParentRewardService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 优惠券抵押父级获得奖励明细
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */
@RestController
@RequestMapping("/consume/parentTeward")
public class SxscUserConsumeParentRewardController extends BaseController
{
    @Autowired
    private ISxscUserConsumeParentRewardService sxscUserConsumeParentRewardService;

    /**
     * 查询优惠券抵押父级获得奖励明细列表
     */
    @PreAuthorize("@ss.hasPermi('consume:parentTeward:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeParentReward sxscUserConsumeParentReward)
    {
        startPage();
        List<SxscUserConsumeParentReward> list = sxscUserConsumeParentRewardService.selectSxscUserConsumeParentRewardList(sxscUserConsumeParentReward);
        return getDataTable(list);
    }

    /**
     * 导出优惠券抵押父级获得奖励明细列表
     */
    @PreAuthorize("@ss.hasPermi('consume:parentTeward:export')")
    @Log(title = "优惠券抵押父级获得奖励明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SxscUserConsumeParentReward sxscUserConsumeParentReward)
    {
        List<SxscUserConsumeParentReward> list = sxscUserConsumeParentRewardService.selectSxscUserConsumeParentRewardList(sxscUserConsumeParentReward);
        ExcelUtil<SxscUserConsumeParentReward> util = new ExcelUtil<SxscUserConsumeParentReward>(SxscUserConsumeParentReward.class);
        util.exportExcel(response, list, "优惠券抵押父级获得奖励明细数据");
    }

    /**
     * 获取优惠券抵押父级获得奖励明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('consume:parentTeward:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserConsumeParentRewardService.selectSxscUserConsumeParentRewardById(id));
    }


}

package com.ruoyi.sxsc.consume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.constant.IntegralBillConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeCompound;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeCompoundMapper;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeCompoundService;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * 优惠券合成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
public class SxscUserConsumeCompoundServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeCompoundMapper,SxscUserConsumeCompound> implements ISxscUserConsumeCompoundService
{


    @Autowired
    private ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    private ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;


    /**
     * 查询优惠券合成
     * 
     * @param id 优惠券合成主键
     * @return 优惠券合成
     */
    @Override
    public SxscUserConsumeCompound selectSxscUserConsumeCompoundById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询优惠券合成列表
     * 
     * @param sxscUserConsumeCompound 优惠券合成
     * @return 优惠券合成
     */
    @Override
    public List<SxscUserConsumeCompound> selectSxscUserConsumeCompoundList(SxscUserConsumeCompound sxscUserConsumeCompound)
    {
        LambdaQueryWrapper<SxscUserConsumeCompound> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserConsumeCompound::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserConsumeCompound.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserConsumeCompound.getParams().get("phonenumber")+"', '%'))");
        }

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeCompound.getConsumeNumber()),SxscUserConsumeCompound::getConsumeNumber,sxscUserConsumeCompound.getConsumeNumber());

        wrapper.eq(StringUtils.isNotNull(sxscUserConsumeCompound.getAmount()),SxscUserConsumeCompound::getAmount,sxscUserConsumeCompound.getAmount());

        List<SxscUserConsumeCompound> list=list(wrapper);

        for(SxscUserConsumeCompound consumeCompound:list){
            consumeCompound.setSysUser(iSysUserService.selectUserMainById(consumeCompound.getUserId()));
        }

        return list;
    }

    /**
     * 新增优惠券合成
     * 
     * @param sxscUserConsumeCompound 优惠券合成
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertSxscUserConsumeCompound(SxscUserConsumeCompound sxscUserConsumeCompound)
    {

        if(StringUtils.isNull(sxscUserConsumeCompound.getAmount())){
            return AjaxResult.error("请填写合成金额");
        }

        SxscUserInfo sxscUserInfo=iSxscUserInfoService.getById(SecurityUtils.getUserId());

        BigDecimal consumeAmountSum=iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmountSum();
        if(consumeAmountSum.compareTo(new BigDecimal("10000000"))>=0){
            return AjaxResult.error("卡面值总计系统发行1000w面值");
        }
        BigDecimal proportion=null;
        if(consumeAmountSum.compareTo(new BigDecimal("9000000"))>=0){
            proportion=new BigDecimal("10");
        }else if(consumeAmountSum.compareTo(new BigDecimal("8000000"))>=0){
            proportion=new BigDecimal("9");
        }else if(consumeAmountSum.compareTo(new BigDecimal("7000000"))>=0){
            proportion=new BigDecimal("8");
        }else if(consumeAmountSum.compareTo(new BigDecimal("6000000"))>=0){
            proportion=new BigDecimal("7");
        }else if(consumeAmountSum.compareTo(new BigDecimal("5000000"))>=0){
            proportion=new BigDecimal("6");
        }else if(consumeAmountSum.compareTo(new BigDecimal("4000000"))>=0){
            proportion=new BigDecimal("5");
        }else if(consumeAmountSum.compareTo(new BigDecimal("3000000"))>=0){
            proportion=new BigDecimal("4");
        }else if(consumeAmountSum.compareTo(new BigDecimal("2000000"))>=0){
            proportion=new BigDecimal("3");
        }else if(consumeAmountSum.compareTo(new BigDecimal("500000"))>=0){
            proportion=new BigDecimal("2");
        }else {
            proportion=new BigDecimal("1");
        }
        BigDecimal gxz=sxscUserConsumeCompound.getAmount().multiply(proportion);

        if(sxscUserInfo.getIntegralGxz().compareTo(gxz)<0){
            return AjaxResult.error("贡献值不足，无法合成");
        }
        if(sxscUserInfo.getIntegralTy().compareTo(sxscUserConsumeCompound.getAmount())<0){
            return AjaxResult.error("积分不足，无法合成");
        }
        sxscUserConsumeCompound.setConsumeNumber(IdUtils.fastSimpleUUID());
        sxscUserConsumeCompound.setIntegralGxz(gxz);
        sxscUserConsumeCompound.setIntegralTy(sxscUserConsumeCompound.getAmount());
        sxscUserConsumeCompound.setUserId(SecurityUtils.getUserId());
        sxscUserConsumeCompound.setCreateBy(SecurityUtils.getUsername());
        sxscUserConsumeCompound.setCreateTime(DateUtils.getNowDate());
        sxscUserConsumeCompound.setConsume(proportion);
        sxscUserConsumeCompound.setId(IdUtils.fastSimpleUUID());
        save(sxscUserConsumeCompound);
        iSxscUserConsumeService.insertSxscUserConsume(new SxscUserConsume(sxscUserConsumeCompound));

        String orderId=sxscUserConsumeCompound.getId();
        iSxscBillIntegralService.insertSxscBillIntegral(orderId, IntegralBillConstants.ConsumeCompound,1l,gxz.multiply(new BigDecimal("-1")));
        iSxscBillIntegralService.insertSxscBillIntegral(orderId, IntegralBillConstants.ConsumeCompound,5l,sxscUserConsumeCompound.getAmount().multiply(new BigDecimal("-1")));
        return AjaxResult.success();
    }


}

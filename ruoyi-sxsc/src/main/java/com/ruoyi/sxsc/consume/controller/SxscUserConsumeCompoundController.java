package com.ruoyi.sxsc.consume.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeCompound;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeCompoundService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 优惠券合成
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/consume/compound")
public class SxscUserConsumeCompoundController extends BaseController
{
    @Autowired
    private ISxscUserConsumeCompoundService sxscUserConsumeCompoundService;

    /**
     * 查询优惠券合成列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SxscUserConsumeCompound sxscUserConsumeCompound)
    {
        startPage();
        List<SxscUserConsumeCompound> list = sxscUserConsumeCompoundService.selectSxscUserConsumeCompoundList(sxscUserConsumeCompound);
        return getDataTable(list);
    }

    /**
     * 获取优惠券合成详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscUserConsumeCompoundService.selectSxscUserConsumeCompoundById(id));
    }


}

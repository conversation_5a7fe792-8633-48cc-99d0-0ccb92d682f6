package com.ruoyi.sxsc.consume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBack;

import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠券回购信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface ISxscUserConsumeBackService extends IService<SxscUserConsumeBack>
{
    /**
     * 查询优惠券回购信息
     * 
     * @param id 优惠券回购信息主键
     * @return 优惠券回购信息
     */
    SxscUserConsumeBack selectSxscUserConsumeBackById(String id);

    /**
     * 查询优惠券回购金额总数
     *
     * @return 优惠券回购信息
     */
    BigDecimal selectSxscUserConsumeBackTotalAmount();

    /**
     * 查询优惠券回购信息列表
     * 
     * @param sxscUserConsumeBack 优惠券回购信息
     * @return 优惠券回购信息集合
     */
    List<SxscUserConsumeBack> selectSxscUserConsumeBackList(SxscUserConsumeBack sxscUserConsumeBack);

    /**
     * 新增优惠券回购信息
     * 
     * @param sxscUserConsumeBack 优惠券回购信息
     * @return 结果
     */
    AjaxResult insertSxscUserConsumeBack(SxscUserConsumeBack sxscUserConsumeBack);

    /**
     * 新增优惠券回购信息
     *
     * @param sxscUserConsumeBack 优惠券回购信息
     * @return 结果
     */
    AjaxResult updateSxscUserConsumeBack(SxscUserConsumeBack sxscUserConsumeBack);

}

package com.ruoyi.sxsc.consume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeCompound;

import java.util.List;

/**
 * 优惠券合成Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface ISxscUserConsumeCompoundService extends IService<SxscUserConsumeCompound>
{
    /**
     * 查询优惠券合成
     * 
     * @param id 优惠券合成主键
     * @return 优惠券合成
     */
    SxscUserConsumeCompound selectSxscUserConsumeCompoundById(Long id);

    /**
     * 查询优惠券合成列表
     * 
     * @param sxscUserConsumeCompound 优惠券合成
     * @return 优惠券合成集合
     */
    List<SxscUserConsumeCompound> selectSxscUserConsumeCompoundList(SxscUserConsumeCompound sxscUserConsumeCompound);

    /**
     * 新增优惠券合成
     * 
     * @param sxscUserConsumeCompound 优惠券合成
     * @return 结果
     */
    AjaxResult insertSxscUserConsumeCompound(SxscUserConsumeCompound sxscUserConsumeCompound);


}

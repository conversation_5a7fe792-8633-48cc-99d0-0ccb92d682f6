package com.ruoyi.sxsc.consume.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBack;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 优惠券回购信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface SxscUserConsumeBackMapper extends BaseMapper<SxscUserConsumeBack>
{

    /**
     * 查询优惠券回购金额总数
     *
     * @return 优惠券回购信息
     */
    @Select("select IFNULL(sum(buy_back), 0) from sxsc_user_consume_back")
    BigDecimal selectSxscUserConsumeBackTotalAmount();

}

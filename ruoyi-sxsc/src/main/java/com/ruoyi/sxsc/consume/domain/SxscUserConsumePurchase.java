package com.ruoyi.sxsc.consume.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 优惠券预购订单对象
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumePurchase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 优惠券预购数量 */
    @Excel(name = "优惠券预购数量")
    private Long purchaseNum;

    /** 是否删除1删除0未删除 */
    private Long delFlag;

    /** 优惠券预购面值 */
    @Excel(name = "优惠券预购面值")
    private BigDecimal purchaseAmount;

    /** 优惠券一面值单价 */
    @Excel(name = "优惠券一面值单价")
    private BigDecimal unitPrice;

    /** 发布人 */
    @Excel(name = "发布人")
    private Long userId;

    /** 卖出人 */
    @Excel(name = "卖出人")
    private Long buyUserId;

    /** 卖出人扣除积分 */
    @Excel(name = "卖出人扣除积分")
    private BigDecimal deductIntegrate;

    /** 卖出人扣除集团股 */
    @Excel(name = "卖出人扣除集团股")
    private BigDecimal deductShare;

    /** 状态0已发布1已成交 */
    @Excel(name = "状态0已发布1已成交")
    private Long status;

    /** 支付类型1支付宝支付4佣金支付 */
    @Excel(name = "支付类型1支付宝支付4佣金支付")
    private Long payType;

    /** 支付状态0待支付1支付成功 */
    @Excel(name = "支付状态0待支付1支付成功")
    private Long payStatus;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain buySysUser;


}

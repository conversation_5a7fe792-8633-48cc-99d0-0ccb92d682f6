package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.UUID;

/**
 * 优惠券对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsume extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    private String consumeName;

    /** 优惠券金额 */
    @Excel(name = "优惠券金额")
    private BigDecimal consumeAmount;

    /** 优惠券卡号 */
    @TableId(type = IdType.INPUT)
    private String consumeNumber;

    /** 状态1可用2惠够中3拆分4回购5合并6卖出7兑换企业股(销毁)8兑换GS9赠送10删除 */
    @Excel(name = "状态")
    private Long status;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 用户手机号 */
    @TableField(exist = false)
    private String phonenumber;

    public SxscUserConsume(SxscUserConsumeCompound consumeCompound){
        this.status=1l;
        this.consumeNumber=consumeCompound.getConsumeNumber();
        this.consumeAmount=consumeCompound.getAmount();
        this.consumeName=consumeCompound.getAmount()+"";
        this.userId=SecurityUtils.getUserId();
    }

    public SxscUserConsume(SxscUserConsumeSplit sxscUserConsumeSplit){
        this.status=1l;
        this.consumeNumber= IdUtils.fastSimpleUUID();
        this.consumeAmount=sxscUserConsumeSplit.getSplitConsumeAmount();
        this.consumeName=sxscUserConsumeSplit.getSplitConsumeAmount()+"";
        this.userId=SecurityUtils.getUserId();
    }



    public SxscUserConsume(SxscUserConsumeMerge sxscUserConsumeMerge){
        this.status=1l;
        this.consumeNumber= IdUtils.fastSimpleUUID();
        this.consumeAmount=sxscUserConsumeMerge.getMergeConsumeAmount();
        this.consumeName=sxscUserConsumeMerge.getMergeConsumeAmount().toString();
        this.userId=sxscUserConsumeMerge.getUserId();
    }

    public SxscUserConsume(BigDecimal consumeAmount,Long userId){
        this.status=1l;
        this.consumeNumber=IdUtils.fastSimpleUUID();
        this.consumeAmount=consumeAmount;
        this.consumeName=consumeAmount.setScale(0, RoundingMode.HALF_UP).toString();
        this.userId=userId;
    }

    public SxscUserConsume(SxscUserConsumeGive sxscUserConsumeGive){
        this.status=1l;
        this.consumeNumber= IdUtils.fastSimpleUUID();
        this.consumeAmount=sxscUserConsumeGive.getConsumeAmount();
        this.consumeName=sxscUserConsumeGive.getConsumeName();
        this.userId=sxscUserConsumeGive.getGiveUserId();
    }
}

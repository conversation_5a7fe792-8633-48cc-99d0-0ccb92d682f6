package com.ruoyi.sxsc.consume.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumePurchase;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 优惠券预购订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
public interface ISxscUserConsumePurchaseService extends IService<SxscUserConsumePurchase>
{
    /**
     * 查询优惠券预购订单
     * 
     * @param id 优惠券预购订单主键
     * @return 优惠券预购订单
     */
    public SxscUserConsumePurchase selectSxscUserConsumePurchaseById(String id);

    /**
     * 查询优惠券预购订单列表
     * 
     * @param sxscUserConsumePurchase 优惠券预购订单
     * @return 优惠券预购订单集合
     */
    public List<SxscUserConsumePurchase> selectSxscUserConsumePurchaseList(SxscUserConsumePurchase sxscUserConsumePurchase);

    /**
     * 新增优惠券预购订单
     * 
     * @param sxscUserConsumePurchase 优惠券预购订单
     * @return 结果
     */
    public AjaxResult insertSxscUserConsumePurchase(SxscUserConsumePurchase sxscUserConsumePurchase);

    /**
     * 修改优惠券预购订单
     * 
     * @param sxscUserConsumePurchase 优惠券预购订单
     * @return 结果
     */
    public int updateSxscUserConsumePurchase(SxscUserConsumePurchase sxscUserConsumePurchase);

    /**
     * 修改优惠券预购订单
     *
     * @param payStatus 支付状态0待支付1支付成功
     * @return 结果
     */
    public void updateSxscUserConsumePurchase(String id,Long payStatus);

    /**
     * 确认优惠券预购订单
     *
     * @param id 优惠券预购订单主键
     * @return 结果
     */
    public AjaxResult confirm(String id);



    /**
     * 删除优惠券预购订单信息
     * 
     * @param id 优惠券预购订单主键
     * @return 结果
     */
    public AjaxResult deleteSxscUserConsumePurchaseById(String id);
}

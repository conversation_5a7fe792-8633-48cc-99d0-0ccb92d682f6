package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券抵押明细对象
 * 
 * <AUTHOR>
 * @date 2024-06-04
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeBuy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(exist = false)
    @Excel(name = "用户手机号")
    private String userName;

    @TableField(exist = false)
    @Excel(name = "用户名称")
    private String nickName;

    /** 用户主键 */
    private Long userId;

    /** 订单主键 */
    @Excel(name = "订单主键")
    private String commodityOrderId;

    /** 优惠券卡号 */
    @Excel(name = "优惠券卡号")
    private String consumeNumber;

    /** 优惠券金额 */
    @Excel(name = "优惠券金额")
    private BigDecimal consumeAmount;

    /** 抵押开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "抵押开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date buyStartTime;

    /** 抵押结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "抵押结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date buyEndTime;

    /** 抵押状态1抵押中0释放 */
    @Excel(name = "抵押状态1抵押中0释放")
    private Long status;

    /** 卖出卡张数 */
    @Excel(name = "卖出卡张数")
    private Long sellOut;

    /** 抵押天数 */
    @Excel(name = "抵押天数")
    private Long mortgageDays;

    /** 积分比例 */
    @Excel(name = "积分比例")
    private BigDecimal proportion;

    @TableField(exist = false)
    private SysUserMain sysUser;

    public  SxscUserConsumeBuy(SxscCommodityOrder order){
        this.buyStartTime= DateUtils.getNowDate();
        this.userId= order.getBuyerUserId();
        this.commodityOrderId= order.getId();
        this.status=1l;
    }

    public SxscUserConsumeBuy(Long id,Long sellOut){
        this.id=id;
        this.sellOut=sellOut;
    }

}

package com.ruoyi.sxsc.consume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeMerge;

import java.util.List;

/**
 * 优惠券合并Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
public interface ISxscUserConsumeMergeService extends IService<SxscUserConsumeMerge>
{
    /**
     * 查询优惠券合并
     * 
     * @param id 优惠券合并主键
     * @return 优惠券合并
     */
    SxscUserConsumeMerge selectSxscUserConsumeMergeById(Long id);

    /**
     * 查询优惠券合并列表
     * 
     * @param sxscUserConsumeMerge 优惠券合并
     * @return 优惠券合并集合
     */
    List<SxscUserConsumeMerge> selectSxscUserConsumeMergeList(SxscUserConsumeMerge sxscUserConsumeMerge);

    /**
     * 新增优惠券合并
     * 
     * @param sxscUserConsumeMerge 优惠券合并
     * @return 结果
     */
    AjaxResult insertSxscUserConsumeMerge(SxscUserConsumeMerge sxscUserConsumeMerge);

    /**
     * 修改优惠券合并
     * 
     * @param sxscUserConsumeMerge 优惠券合并
     * @return 结果
     */
    int updateSxscUserConsumeMerge(SxscUserConsumeMerge sxscUserConsumeMerge);

    /**
     * 批量删除优惠券合并
     * 
     * @param ids 需要删除的优惠券合并主键集合
     * @return 结果
     */
    int deleteSxscUserConsumeMergeByIds(Long[] ids);

    /**
     * 删除优惠券合并信息
     * 
     * @param id 优惠券合并主键
     * @return 结果
     */
    int deleteSxscUserConsumeMergeById(Long id);
}

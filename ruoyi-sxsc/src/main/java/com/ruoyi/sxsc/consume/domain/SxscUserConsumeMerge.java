package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 优惠券合并对象
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeMerge extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 优惠券卡号 */
    @Excel(name = "优惠券卡号")
    private String consumeNumber;

    /** 拆分后的卡号 */
    @Excel(name = "合并后的卡号")
    private String mergeConsumeNumber;

    /** 拆分金额 */
    @Excel(name = "合并的金额")
    private BigDecimal mergeConsumeAmount;

    /** 消耗百分比 */
    @Excel(name = "消耗百分比")
    private BigDecimal consume;

    /** 优惠券金额 */
    @TableField(exist = false)
    private String consumeAmount;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;

}

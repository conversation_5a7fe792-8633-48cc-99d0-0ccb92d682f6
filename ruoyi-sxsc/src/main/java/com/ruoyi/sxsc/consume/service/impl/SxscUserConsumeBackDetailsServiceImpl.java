package com.ruoyi.sxsc.consume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBackDetails;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeBackDetailsMapper;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeBackDetailsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 优惠券回购明细信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class SxscUserConsumeBackDetailsServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserConsumeBackDetailsMapper,SxscUserConsumeBackDetails> implements ISxscUserConsumeBackDetailsService
{

    /**
     * 查询优惠券回购明细信息
     *
     * @param backId 优惠券回购主键
     * @return 优惠券回购明细信息
     */
    public List<SxscUserConsumeBackDetails> selectSxscUserConsumeBackDetailsByBackId(String backId){
        LambdaQueryWrapper<SxscUserConsumeBackDetails> wrapper=new LambdaQueryWrapper();

        wrapper.eq(SxscUserConsumeBackDetails::getBackId,backId);

        wrapper.orderByDesc(SxscUserConsumeBackDetails::getCreateTime);

        return list(wrapper);
    }


}

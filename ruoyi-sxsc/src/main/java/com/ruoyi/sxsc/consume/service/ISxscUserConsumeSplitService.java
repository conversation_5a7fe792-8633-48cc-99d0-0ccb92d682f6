package com.ruoyi.sxsc.consume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeSplit;

import java.util.List;

/**
 * 优惠券拆分Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface ISxscUserConsumeSplitService extends IService<SxscUserConsumeSplit>
{
    /**
     * 查询优惠券拆分
     * 
     * @param id 优惠券拆分主键
     * @return 优惠券拆分
     */
    SxscUserConsumeSplit selectSxscUserConsumeSplitById(Long id);

    /**
     * 查询优惠券拆分列表
     * 
     * @param sxscUserConsumeSplit 优惠券拆分
     * @return 优惠券拆分集合
     */
    List<SxscUserConsumeSplit> selectSxscUserConsumeSplitList(SxscUserConsumeSplit sxscUserConsumeSplit);

    /**
     * 新增优惠券拆分
     * 
     * @param sxscUserConsumeSplit 优惠券拆分
     * @return 结果
     */

    AjaxResult insertSxscUserConsumeSplit(SxscUserConsumeSplit sxscUserConsumeSplit);


}

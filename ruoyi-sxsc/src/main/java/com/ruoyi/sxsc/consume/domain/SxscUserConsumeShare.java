package com.ruoyi.sxsc.consume.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 富星卡兑换对象
 * 
 * <AUTHOR>
 * @date 2024-09-10
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserConsumeShare extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private String id;

    /** 用户主键 */
    @Excel(name = "用户主键")
    private Long userId;

    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    private String consumeName;

    /** 优惠券金额 */
    @Excel(name = "优惠券金额")
    private BigDecimal consumeAmount;

    /** 优惠券卡号 */
    @Excel(name = "优惠券卡号")
    private String consumeNumber;

    /** 兑换集团股份 */
    @Excel(name = "兑换集团股份")
    private BigDecimal share;

    /** 兑换比例 */
    @Excel(name = "兑换比例")
    private BigDecimal proportion;

    @TableField(exist = false)
    private SysUserMain sysUserMain;


}

package com.ruoyi.sxsc.seting.service.impl;

import java.util.List;
import java.util.Arrays;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sxsc.seting.mapper.SxscSetingVersionMapper;
import com.ruoyi.sxsc.seting.domain.SxscSetingVersion;
import com.ruoyi.sxsc.seting.service.ISxscSetingVersionService;

/**
 * APP版本信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@Service
public class SxscSetingVersionServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscSetingVersionMapper,SxscSetingVersion> implements ISxscSetingVersionService
{

    /**
     * 查询APP版本信息
     * 
     * @param id APP版本信息主键
     * @return APP版本信息
     */
    @Override
    public SxscSetingVersion selectSxscSetingVersionById(Long id)
    {
        return getById(id);
    }


    /**
     * 查询APP版本强制更新版本
     *
     * @return APP版本信息
     */
    public SxscSetingVersion selectSxscSetingVersionByForce(){
        LambdaQueryWrapper<SxscSetingVersion> wrapper=new LambdaQueryWrapper();
        wrapper.orderByDesc(SxscSetingVersion::getCreateTime);
        wrapper.eq(SxscSetingVersion::getVersionForce,1);
        wrapper.eq(SxscSetingVersion::getVersionIssue,1);
        wrapper.last(" limit 1");
        return getOne(wrapper);
    }
    /**
     * 查询APP版本信息列表
     * 
     * @param sxscSetingVersion APP版本信息
     * @return APP版本信息
     */
    @Override
    public List<SxscSetingVersion> selectSxscSetingVersionList(SxscSetingVersion sxscSetingVersion)
    {
        LambdaQueryWrapper<SxscSetingVersion> wrapper=new LambdaQueryWrapper();

        wrapper.orderByDesc(SxscSetingVersion::getCreateTime);

        wrapper.like(StringUtils.isNotNull(sxscSetingVersion.getVersionNumber()),SxscSetingVersion::getVersionNumber,sxscSetingVersion.getVersionNumber());

        wrapper.eq(StringUtils.isNotNull(sxscSetingVersion.getVersionUrl()),SxscSetingVersion::getVersionUrl,sxscSetingVersion.getVersionUrl());

        wrapper.eq(StringUtils.isNotNull(sxscSetingVersion.getVersionForce()),SxscSetingVersion::getVersionForce,sxscSetingVersion.getVersionForce());

        wrapper.eq(StringUtils.isNotNull(sxscSetingVersion.getVersionIssue()),SxscSetingVersion::getVersionIssue,sxscSetingVersion.getVersionIssue());

        return list(wrapper);
    }

    /**
     * 新增APP版本信息
     * 
     * @param sxscSetingVersion APP版本信息
     * @return 结果
     */
    @Override
    public int insertSxscSetingVersion(SxscSetingVersion sxscSetingVersion)
    {
        sxscSetingVersion.setCreateBy(SecurityUtils.getUsername());
        sxscSetingVersion.setCreateTime(DateUtils.getNowDate());
        return save(sxscSetingVersion)?1:0;
    }

    /**
     * 修改APP版本信息
     * 
     * @param sxscSetingVersion APP版本信息
     * @return 结果
     */
    @Override
    public int updateSxscSetingVersion(SxscSetingVersion sxscSetingVersion)
    {
        sxscSetingVersion.setUpdateBy(SecurityUtils.getUsername());
        sxscSetingVersion.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscSetingVersion)?1:0;
    }


}

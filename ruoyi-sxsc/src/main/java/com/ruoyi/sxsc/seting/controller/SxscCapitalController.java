package com.ruoyi.sxsc.seting.controller;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.sxsc.bill.mapper.SxscBillTrustFundMapper;
import com.ruoyi.sxsc.bill.service.ISxscBillTrustFundService;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityOrderMapper;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityStaticCodeOrderMapper;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderRefundService;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeMapper;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import com.ruoyi.sxsc.payment.service.ISxscAliPayWithdrawalService;
import com.ruoyi.sxsc.person.mapper.SxscUserInfoMapper;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.sxsc.seting.service.ISxscSetingParameterService;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资金详细信息
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/capital")
public class SxscCapitalController extends BaseController
{


    @Autowired
    private SxscCommodityOrderMapper sxscCommodityOrderMapper;

    @Autowired
    private SxscUserInfoMapper sxscUserInfoMapper;

    @Autowired
    SxscCommodityStaticCodeOrderMapper sxscCommodityStaticCodeOrderMapper;

    @Autowired
    private ISxscBillTrustFundService iSxscBillTrustFundService;

    @Autowired
    private ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    private SxscUserConsumeMapper sxscUserConsumeMapper;


    /**
     * 获取资金详细信息
     */
    @GetMapping()
    public AjaxResult getInfo()
    {
        Map<String,BigDecimal> map=new HashMap<>();
        //平台总用户
        map.put("peopleCount", sxscUserInfoMapper.peopleCount());
        //平台总贡献值
        map.put("integralGxzSum",sxscUserInfoMapper.integralGxzSum());
        //当日利润=当日所有进入池子金额
        map.put("dailyProfit",iSxscBillTrustFundService.totalProfit(DateUtils.getDate()));
        //当日流水总额
        map.put("transactionAmount",sxscCommodityOrderMapper.transactionAmount(DateUtils.getDate()).add(sxscCommodityStaticCodeOrderMapper.transactionAmount(DateUtils.getDate())));
        //平台总积分
        map.put("integralTySum",sxscUserInfoMapper.integralTySum());
        //总利润=池子实际金额
        map.put("totalProfit",iSxscBillTrustFundService.totalProfit(null));
        //当前结余利润
        map.put("totalSurplusProfit",iSxscBillTrustFundService.totalSurplusProfit());
        //优惠券总面值
        map.put("consumeSum",iSxscUserConsumeService.selectSxscUserConsumeByConsumeAmountSum());
        //优惠券总销毁
        map.put("consumeSumDestroy",sxscUserConsumeMapper.sumConsumeAmount(4l,null));
        //优惠券总兑换
        map.put("consumeSumExchange",sxscUserConsumeMapper.sumConsumeAmount(8l,null));
        //优惠券总质押
        map.put("consumeSumPledge",sxscUserConsumeMapper.sumConsumeAmount(2l,null));
        //优惠券总可用
        map.put("consumeSumAvailable",sxscUserConsumeMapper.sumConsumeAmount(1l,null));
        //gs值
        map.put("shareSum",sxscUserInfoMapper.integralShareSum());

        return success(map);
    }


    /**
     * 获取订单个数统计数据
     */
    @GetMapping("/orderCountLine")
    public AjaxResult orderCountLine(@RequestParam("startDate") String startDate,@RequestParam("endDate") String endDate)
    {
        Map<String,Object> ajaxMap=new HashMap<>();

        List<String> dates=DateUtils.getAllDatesBetween(startDate,endDate);

        List<String> xData=new ArrayList<>();
        List<Long> orderCountList=new ArrayList<>();
        List<Long> orderPayCountList=new ArrayList<>();

        for(String date:dates){
            //日期数据
            xData.add(date);
            //订单总数
            orderCountList.add(sxscCommodityOrderMapper.orderCount(date));
            //订单支付总数
            orderPayCountList.add(sxscCommodityOrderMapper.orderPayCount(date,null));
        }

        ajaxMap.put("xData",xData);
        ajaxMap.put("orderCount",orderCountList);
        ajaxMap.put("orderPayCount",orderPayCountList);

        return success(ajaxMap);
    }


    /**
     * 获取订单支付统计数据
     */
    @GetMapping("/orderPayLine")
    public AjaxResult orderPayLine(@RequestParam("startDate") String startDate,@RequestParam("endDate") String endDate)
    {

        Map<String,Object> ajaxMap=new HashMap<>();

        List<String> dates=DateUtils.getAllDatesBetween(startDate,endDate);

        List<String> xData=new ArrayList<>();
        List<BigDecimal> orderPaySumAmountList=new ArrayList<>();
        List<BigDecimal> orderPaySumCostList=new ArrayList<>();
        List<BigDecimal> orderPaySumProfitList=new ArrayList<>();

        for(String date:dates){
            //日期数据
            xData.add(date);
            //订单支付总额
            BigDecimal paySumAmount=sxscCommodityOrderMapper.orderPaySumAmount(date);
            orderPaySumAmountList.add(paySumAmount);
            //订单支付总成本
            BigDecimal paySumCost=sxscCommodityOrderMapper.orderPaySumCost(date);
            orderPaySumCostList.add(paySumCost);
            //订单支付总利润
            orderPaySumProfitList.add(paySumAmount.subtract(paySumCost));
        }

        ajaxMap.put("xData",xData);
        ajaxMap.put("orderPaySumAmount",orderPaySumAmountList);
        ajaxMap.put("orderPaySumCost",orderPaySumCostList);
        ajaxMap.put("orderPaySumProfit",orderPaySumProfitList);

        return success(ajaxMap);
    }

    /**
     * 获取订单利润总额
     */
    @GetMapping("/orderProfitAmount")
    public AjaxResult orderProfitAmount()
    {
        Map<String,Object> ajaxMap=new HashMap<>();
        //已支付
        ajaxMap.put("payment",sxscCommodityOrderMapper.orderProfitTotalAmount(2l));
        //已发货
        ajaxMap.put("send",sxscCommodityOrderMapper.orderProfitTotalAmount(3l));
        //已收货
        ajaxMap.put("delivery",sxscCommodityOrderMapper.orderProfitTotalAmount(4l));
        //已完成
        ajaxMap.put("complete",sxscCommodityOrderMapper.orderProfitTotalAmount(5l));
        return success(ajaxMap);
    }


    /**
     * 获取订单总销售额
     */
    @GetMapping("/orderSalesAmount")
    public AjaxResult orderSales()
    {
        Map<String,Object> ajaxMap=new HashMap<>();
        //已支付
        ajaxMap.put("payment",sxscCommodityOrderMapper.orderTotalAmount(2l));
        //已发货
        ajaxMap.put("send",sxscCommodityOrderMapper.orderTotalAmount(3l));
        //已收货
        ajaxMap.put("delivery",sxscCommodityOrderMapper.orderTotalAmount(4l));
        //已完成
        ajaxMap.put("complete",sxscCommodityOrderMapper.orderTotalAmount(5l));
        return success(ajaxMap);
    }

    /**
     * 获取个板块订单个数
     */
    @GetMapping("/orderPlateCountLine")
    public AjaxResult orderPlateCountLine(@RequestParam("startDate") String startDate,@RequestParam("endDate") String endDate)
    {

        Map<String,Object> ajaxMap=new HashMap<>();

        List<String> dates=DateUtils.getAllDatesBetween(startDate,endDate);

        List<String> xData=new ArrayList<>();
        //普通商品：0
        List<Long> orderCountOrdinaryList=new ArrayList<>();
        //超值购商品：根据查询专区id查询
        List<Long> orderCountBenefitRightsList=new ArrayList<>();
        //品牌购商品：2
        List<Long> orderCountBuyingMerchantsList=new ArrayList<>();
        //拼团购商品：3
        List<Long> orderPayCountTeamLeaderList=new ArrayList<>();
        //新人特惠：4
        List<Long> orderPayCountEmployeeList=new ArrayList<>();

        for(String date:dates){
            //日期数据
            xData.add(date);
            orderCountOrdinaryList.add(sxscCommodityOrderMapper.orderPayCount(date,0l));
            orderCountBenefitRightsList.add(sxscCommodityOrderMapper.orderPayCount(date,1l));
            orderCountBuyingMerchantsList.add(sxscCommodityOrderMapper.orderPayCount(date,2l));
            orderPayCountTeamLeaderList.add(sxscCommodityOrderMapper.orderPayCount(date,3l));
            orderPayCountEmployeeList.add(sxscCommodityOrderMapper.orderPayCount(date,4l));
        }

        ajaxMap.put("xData",xData);
        ajaxMap.put("orderCountOrdinary",orderCountOrdinaryList);
        ajaxMap.put("orderCountBenefitRights",orderCountBenefitRightsList);
        ajaxMap.put("orderCountBuyingMerchants",orderCountBuyingMerchantsList);
        ajaxMap.put("orderPayCountTeamLeader",orderPayCountTeamLeaderList);
        ajaxMap.put("orderPayCountEmployee",orderPayCountEmployeeList);

        return success(ajaxMap);
    }
    /**
     * 获取板块订单利润总额
     */
    @GetMapping("/orderPlateProfitAmountLine")
    public AjaxResult orderPlateProfitAmountLine(@RequestParam("startDate") String startDate,@RequestParam("endDate") String endDate)
    {

        Map<String,Object> ajaxMap=new HashMap<>();

        List<String> dates=DateUtils.getAllDatesBetween(startDate,endDate);

        List<String> xData=new ArrayList<>();
        //普通商品：0
        List<BigDecimal> orderCountOrdinaryList=new ArrayList<>();
        //超值购商品：根据查询专区id查询
        List<BigDecimal> orderCountBenefitRightsList=new ArrayList<>();
        //品牌购商品：2
        List<BigDecimal> orderCountBuyingMerchantsList=new ArrayList<>();
        //拼团购商品：3
        List<BigDecimal> orderPayCountTeamLeaderList=new ArrayList<>();
        //新人特惠：4
        List<BigDecimal> orderPayCountEmployeeList=new ArrayList<>();

        for(String date:dates){
            //日期数据
            xData.add(date);
            orderCountOrdinaryList.add(sxscCommodityOrderMapper.orderPayProfitAmount(date,0l));
            orderCountBenefitRightsList.add(sxscCommodityOrderMapper.orderPayProfitAmount(date,1l));
            orderCountBuyingMerchantsList.add(sxscCommodityOrderMapper.orderPayProfitAmount(date,2l));
            orderPayCountTeamLeaderList.add(sxscCommodityOrderMapper.orderPayProfitAmount(date,3l));
            orderPayCountEmployeeList.add(sxscCommodityOrderMapper.orderPayProfitAmount(date,4l));
        }

        ajaxMap.put("xData",xData);
        ajaxMap.put("orderProfitAmountOrdinary",orderCountOrdinaryList);
        ajaxMap.put("orderProfitAmountBenefitRights",orderCountBenefitRightsList);
        ajaxMap.put("orderProfitAmountBuyingMerchants",orderCountBuyingMerchantsList);
        ajaxMap.put("orderPayProfitAmountTeamLeader",orderPayCountTeamLeaderList);
        ajaxMap.put("orderPayProfitAmountEmployee",orderPayCountEmployeeList);

        return success(ajaxMap);
    }



    @Autowired
    private ISxscAliPayOrderService iSxscAliPayOrderService;

    @Autowired
    private ISxscAliPayWithdrawalService iSxscAliPayWithdrawalService;

    @Autowired
    private ISxscCommodityOrderRefundService iSxscCommodityOrderRefundService;


    /**
     * 获取支付宝累计金额数据
     */
    @GetMapping("/aliPayAmount")
    public AjaxResult aliPayAmount(@RequestParam(value = "month",required = false) String month)
    {

        Map<String,Object> ajaxMap=new HashMap<>();
        ajaxMap.put("aliPaySum",iSxscAliPayOrderService.aliPaySum(month,null));
        ajaxMap.put("aliPayWithdrawalCommissionSum",iSxscAliPayWithdrawalService.aliPayWithdrawalSum("佣金提现",month));
        ajaxMap.put("aliPayWithdrawalSum",iSxscAliPayWithdrawalService.aliPayWithdrawalSum(null,month));
        ajaxMap.put("aliPayRefundSum",iSxscCommodityOrderRefundService.aliPayRefundSum(month));
        ajaxMap.put("orderNewcomerSupplyAmountSum",sxscCommodityOrderMapper.orderSupplyAmount(month,4l));
        return success(ajaxMap);
    }

}

package com.ruoyi.sxsc.seting.service;

import java.util.List;
import com.ruoyi.sxsc.seting.domain.SxscSetingVersion;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * APP版本信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface ISxscSetingVersionService extends IService<SxscSetingVersion>
{
    /**
     * 查询APP版本信息
     * 
     * @param id APP版本信息主键
     * @return APP版本信息
     */
    public SxscSetingVersion selectSxscSetingVersionById(Long id);

    /**
     * 查询APP版本强制更新版本
     *
     * @return APP版本信息
     */
    public SxscSetingVersion selectSxscSetingVersionByForce();

    /**
     * 查询APP版本信息列表
     * 
     * @param sxscSetingVersion APP版本信息
     * @return APP版本信息集合
     */
    public List<SxscSetingVersion> selectSxscSetingVersionList(SxscSetingVersion sxscSetingVersion);

    /**
     * 新增APP版本信息
     * 
     * @param sxscSetingVersion APP版本信息
     * @return 结果
     */
    public int insertSxscSetingVersion(SxscSetingVersion sxscSetingVersion);

    /**
     * 修改APP版本信息
     * 
     * @param sxscSetingVersion APP版本信息
     * @return 结果
     */
    public int updateSxscSetingVersion(SxscSetingVersion sxscSetingVersion);


}

package com.ruoyi.sxsc.seting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.seting.domain.SxscSetingFile;

import java.util.List;

/**
 * 附件信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface ISxscSetingFileService extends IService<SxscSetingFile>
{

    /**
     * 查询附件信息列表
     * 
     * @param sxscFile 附件信息
     * @return 附件信息集合
     */
    List<SxscSetingFile> selectSxscFileList(SxscSetingFile sxscFile);

    /**
     * 新增附件信息
     * 
     * @param setingFile 附件信息
     * @return 结果
     */
    AjaxResult insertSxscFile(SxscSetingFile setingFile);


}

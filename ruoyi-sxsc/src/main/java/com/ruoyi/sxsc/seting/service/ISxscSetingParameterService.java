package com.ruoyi.sxsc.seting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.seting.domain.SxscSetingParameter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资金设置信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface ISxscSetingParameterService extends IService<SxscSetingParameter>
{
    /**
     * 查询资金设置信息
     * 
     * @param type 资金设置信息类型
     * @return 资金设置信息
     */
    SxscSetingParameter selectSxscSetingParameterByType(Long type);


    /**
     * 查询优惠券单价（一面值价格）
     *
     * @return 金额
     */
    BigDecimal consumeUnitPrice();

    /**
     * 查询资金设置信息列表
     * 
     * @param sxscSetingCapital 资金设置信息
     * @return 资金设置信息集合
     */
    List<SxscSetingParameter> selectSxscSetingParameterList(SxscSetingParameter sxscSetingCapital);

    /**
     * 新增资金设置信息
     * 
     * @param sxscSetingCapital 资金设置信息
     * @return 结果
     */
    int insertSxscSetingParameter(SxscSetingParameter sxscSetingCapital);

    /**
     * 修改资金设置信息
     * 
     * @return 结果
     */
    int updateSxscSetingParameter(Long type);


}

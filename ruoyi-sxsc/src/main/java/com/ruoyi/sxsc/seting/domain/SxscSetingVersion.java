package com.ruoyi.sxsc.seting.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP版本信息对象
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscSetingVersion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 版本号 */
    @Excel(name = "版本号")
    private String versionNumber;

    /** 版本更新地址 */
    @Excel(name = "版本更新地址")
    private String versionUrl;

    /** 版本是否强制更新1是0否 */
    @Excel(name = "版本是否强制更新1是0否")
    private Long versionForce;

    /** 版本标题 */
    @Excel(name = "版本标题")
    private String versionTitle;

    /** 版本内容 */
    @Excel(name = "版本内容")
    private String versionContent;

    /** 版本是否发行 */
    @Excel(name = "版本是否发行")
    private Long versionIssue;



}

package com.ruoyi.sxsc.seting.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.seting.domain.SxscSetingFile;
import com.ruoyi.sxsc.seting.service.ISxscSetingFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 附件信息
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/seting/file")
public class SxscSetingFileController extends BaseController
{
    @Autowired
    private ISxscSetingFileService sxscFileService;

    /**
     * 查询附件信息列表
     */
    @PreAuthorize("@ss.hasPermi('seting:file:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscSetingFile sxscFile)
    {
        startPage();
        List<SxscSetingFile> list = sxscFileService.selectSxscFileList(sxscFile);
        return getDataTable(list);
    }

    /**
     * 新增附件信息
     */
    @PreAuthorize("@ss.hasPermi('seting:file:add')")
    @Log(title = "附件信息", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody SxscSetingFile setingFile)
    {
        return sxscFileService.insertSxscFile(setingFile);
    }


}

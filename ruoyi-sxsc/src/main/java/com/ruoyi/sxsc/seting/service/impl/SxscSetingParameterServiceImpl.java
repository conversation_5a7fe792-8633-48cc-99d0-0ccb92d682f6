package com.ruoyi.sxsc.seting.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.operations.domain.SxscSystemMessage;
import com.ruoyi.sxsc.operations.service.ISxscSystemMessageService;
import com.ruoyi.sxsc.seting.domain.SxscSetingParameter;
import com.ruoyi.sxsc.seting.mapper.SxscSetingParameterMapper;
import com.ruoyi.sxsc.seting.service.ISxscSetingParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资金设置信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class SxscSetingParameterServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscSetingParameterMapper, SxscSetingParameter> implements ISxscSetingParameterService
{

    @Autowired
    ISxscSystemMessageService iSxscSystemMessageService;

    /**
     * 查询资金设置信息
     * 
     * @param type 资金设置信息类型
     * @return 资金设置信息
     */
    @Override
    public SxscSetingParameter selectSxscSetingParameterByType(Long type)
    {
        LambdaQueryWrapper<SxscSetingParameter> wrapper=new LambdaQueryWrapper();

        wrapper.eq(SxscSetingParameter::getType,type);

        wrapper.eq(SxscSetingParameter::getDelFlag,0l);

        wrapper.orderByDesc(SxscSetingParameter::getCreateTime);

        return getOne(wrapper);
    }


    /**
     * 查询资产总量
     *
     * @return 金额
     */
    @Override
    public BigDecimal consumeUnitPrice(){
        SxscSetingParameter sxscSetingCapital=selectSxscSetingParameterByType(1l);
        return  StringUtils.isNotNull(sxscSetingCapital)?sxscSetingCapital.getAmount():null;
    }


    /**
     * 查询资金设置信息列表
     * 
     * @param sxscSetingCapital 资金设置信息
     * @return 资金设置信息
     */
    @Override
    public List<SxscSetingParameter> selectSxscSetingParameterList(SxscSetingParameter sxscSetingCapital)
    {
        LambdaQueryWrapper<SxscSetingParameter> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscSetingCapital.getType()), SxscSetingParameter::getType,sxscSetingCapital.getType());

        wrapper.eq(StringUtils.isNotNull(sxscSetingCapital.getDelFlag()), SxscSetingParameter::getDelFlag,sxscSetingCapital.getDelFlag());

        wrapper.orderByDesc(SxscSetingParameter::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增资金设置信息
     * 
     * @param sxscSetingCapital 资金设置信息
     * @return 结果
     */
    @Override
    public int insertSxscSetingParameter(SxscSetingParameter sxscSetingCapital)
    {
        updateSxscSetingParameter(sxscSetingCapital.getType());
        sxscSetingCapital.setCreateBy(SecurityUtils.getUsername());
        sxscSetingCapital.setCreateTime(DateUtils.getNowDate());
        if(sxscSetingCapital.getType()==1){
            iSxscSystemMessageService.insertSxscSystemMessage(new SxscSystemMessage("优惠券最新价格","自本消息发布时间开始，优惠券的一面值价格为《"+sxscSetingCapital.getAmount()+"》元（RMB）"));
        }
        return save(sxscSetingCapital)?1:0;
    }

    /**
     * 修改资金设置信息
     * 
     * @return 结果
     */
    @Override
    public int updateSxscSetingParameter(Long type)
    {
        LambdaUpdateWrapper<SxscSetingParameter> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(SxscSetingParameter::getType,type);
        wrapper.set(SxscSetingParameter::getUpdateBy,SecurityUtils.getUsername());
        wrapper.set(SxscSetingParameter::getUpdateTime,DateUtils.getNowDate());
        wrapper.set(SxscSetingParameter::getDelFlag,1l);
        return update(wrapper)?1:0;
    }


}

package com.ruoyi.sxsc.seting.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 资金设置信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscSetingParameter extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 具体数值 */
    @Excel(name = "具体数值")
    private BigDecimal amount;

    /** 类型 1 当前1优惠券面值 */
    @Excel(name = "类型")
    private Long type;

    /** 是否有效 */
    @Excel(name = "是否有效")
    private Long delFlag;

    /** 参数说明 */
    private String remark;


}

package com.ruoyi.sxsc.seting.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.seting.domain.SxscSetingVersion;
import com.ruoyi.sxsc.seting.service.ISxscSetingVersionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP版本信息
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@RestController
@RequestMapping("/seting/version")
public class SxscSetingVersionController extends BaseController
{
    @Autowired
    private ISxscSetingVersionService sxscSetingVersionService;

    /**
     * 查询APP版本信息列表
     */
    @PreAuthorize("@ss.hasPermi('seting:version:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscSetingVersion sxscSetingVersion)
    {
        startPage();
        List<SxscSetingVersion> list = sxscSetingVersionService.selectSxscSetingVersionList(sxscSetingVersion);
        return getDataTable(list);
    }



    /**
     * 获取APP版本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('seting:version:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscSetingVersionService.selectSxscSetingVersionById(id));
    }

    /**
     * 新增APP版本信息
     */
    @PreAuthorize("@ss.hasPermi('seting:version:add')")
    @Log(title = "APP版本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscSetingVersion sxscSetingVersion)
    {
        return toAjax(sxscSetingVersionService.insertSxscSetingVersion(sxscSetingVersion));
    }

    /**
     * 修改APP版本信息
     */
    @PreAuthorize("@ss.hasPermi('seting:version:edit')")
    @Log(title = "APP版本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscSetingVersion sxscSetingVersion)
    {
        return toAjax(sxscSetingVersionService.updateSxscSetingVersion(sxscSetingVersion));
    }


}

package com.ruoyi.sxsc.seting.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.seting.domain.SxscSetingParameter;
import com.ruoyi.sxsc.seting.service.ISxscSetingParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资金设置信息
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/seting/parameter")
public class SxscSettingParameterController extends BaseController
{
    @Autowired
    private ISxscSetingParameterService sxscSetingCapitalService;

    /**
     * 查询资金设置信息列表
     */
    @PreAuthorize("@ss.hasPermi('seting:parameter:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscSetingParameter sxscSetingCapital)
    {
        startPage();
        List<SxscSetingParameter> list = sxscSetingCapitalService.selectSxscSetingParameterList(sxscSetingCapital);
        return getDataTable(list);
    }

    /**
     * 获取资金设置信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('seting:parameter:query')")
    @GetMapping(value = "/{type}")
    public AjaxResult getInfo(@PathVariable("type") Long type)
    {
        return success(sxscSetingCapitalService.selectSxscSetingParameterByType(type));
    }

    /**
     * 新增资金设置信息
     */
    @PreAuthorize("@ss.hasPermi('seting:parameter:add')")
    @Log(title = "资金设置信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscSetingParameter sxscSetingCapital)
    {
        return toAjax(sxscSetingCapitalService.insertSxscSetingParameter(sxscSetingCapital));
    }


}

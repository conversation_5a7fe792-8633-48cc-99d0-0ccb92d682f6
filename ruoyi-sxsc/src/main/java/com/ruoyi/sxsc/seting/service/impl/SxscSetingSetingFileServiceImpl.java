package com.ruoyi.sxsc.seting.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.seting.domain.SxscSetingFile;
import com.ruoyi.sxsc.seting.mapper.SxscSetingFileMapper;
import com.ruoyi.sxsc.seting.service.ISxscSetingFileService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 附件信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class SxscSetingSetingFileServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscSetingFileMapper, SxscSetingFile> implements ISxscSetingFileService
{

    /**
     * 查询附件信息列表
     * 
     * @param sxscFile 附件信息
     * @return 附件信息
     */
    @Override
    public List<SxscSetingFile> selectSxscFileList(SxscSetingFile sxscFile)
    {
        LambdaQueryWrapper<SxscSetingFile> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscFile.getType()), SxscSetingFile::getType,sxscFile.getType());

        wrapper.eq(StringUtils.isNotNull(sxscFile.getDelFlag()),SxscSetingFile::getDelFlag,sxscFile.getDelFlag());

        wrapper.orderByDesc(SxscSetingFile::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增附件信息
     * 
     * @param setingFile 附件信息
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscFile(SxscSetingFile setingFile)
    {
        LambdaUpdateWrapper<SxscSetingFile> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(SxscSetingFile::getName,setingFile.getName());
        wrapper.set(SxscSetingFile::getDelFlag,1l);
        wrapper.set(SxscSetingFile::getUpdateBy,SecurityUtils.getUsername());
        wrapper.set(SxscSetingFile::getUpdateTime,DateUtils.getNowDate());
        update(wrapper);
        setingFile.setDelFlag(0l);
        setingFile.setCreateBy(SecurityUtils.getUsername());
        setingFile.setCreateTime(DateUtils.getNowDate());
        save(setingFile);
        return AjaxResult.success();
    }

}

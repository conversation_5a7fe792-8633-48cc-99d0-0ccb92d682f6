package com.ruoyi.sxsc.seting.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 附件信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscSetingFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String name;

    /** 文件地址 */
    @Excel(name = "文件地址")
    private String url;

    /** 文件类型 1合资公司 2孵化项目 3系统内容*/
    @Excel(name = "文件类型")
    private Long type;

    /** 是否删除0未删除1删除 */
    @Excel(name = "是否删除0未删除1删除")
    private Long delFlag;




}

package com.ruoyi.sxsc.enterprise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterprise;

import java.util.List;

/**
 * 企业信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public interface ISxscEnterpriseService extends IService<SxscEnterprise>
{
    /**
     * 查询企业信息
     * 
     * @param id 企业信息主键
     * @return 企业信息
     */
    SxscEnterprise selectSxscEnterpriseById(Long id);

    /**
     * 查询商家信息
     *
     * @param userId 账号主键
     * @return 商家信息
     */
    SxscEnterprise selectEnterpriseByUserId(Long userId);

    /**
     * 查询企业信息列表
     * 
     * @param sxscEnterprise 企业信息
     * @return 企业信息集合
     */
    List<SxscEnterprise> selectSxscEnterpriseList(SxscEnterprise sxscEnterprise);

    /**
     * 新增企业信息
     * 
     * @param sxscEnterprise 企业信息
     * @return 结果
     */
    AjaxResult insertSxscEnterprise(SxscEnterprise sxscEnterprise);

    /**
     * 修改企业信息
     * 
     * @param sxscEnterprise 企业信息
     * @return 结果
     */
    AjaxResult updateSxscEnterprise(SxscEnterprise sxscEnterprise);

    /**
     * 修改商家状态信息
     *
     * @param status 状态
     * @param id 主键
     * @return 结果
     */
    AjaxResult updateEnterpriseStatus(Long status,Long  id);

    /**
     * 修改商家自营信息
     *
     * @param self 自营
     * @param id 主键
     * @return 结果
     */
    AjaxResult updateEnterpriseSelf(Long self,Long  id);

    /**
     * 批量删除企业信息
     * 
     * @param ids 需要删除的企业信息主键集合
     * @return 结果
     */
    int deleteSxscEnterpriseByIds(Long[] ids);

    /**
     * 删除企业信息信息
     * 
     * @param id 企业信息主键
     * @return 结果
     */
    int deleteSxscEnterpriseById(Long id);

    /**
     * 根据经纬度获取附近商家
     * @param  longitude 经度
     * @param  latitude 纬度
     * */
    AjaxResult nearby(String longitude,String latitude);
}

package com.ruoyi.sxsc.enterprise.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 企业审核记录对象
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscEnterpriseExamine extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 企业ID */
    @Excel(name = "企业ID")
    private Long enterpriseId;

    /** 审核描述 */
    @Excel(name = "审核描述")
    private String remarks;

    /** 审核状态1审核通过2拒绝 */
    @Excel(name = "审核状态1审核通过2拒绝")
    private Long status;

    /** 企业信息 */
    @TableField(exist = false)
    private SxscEnterprise enterprise;


}

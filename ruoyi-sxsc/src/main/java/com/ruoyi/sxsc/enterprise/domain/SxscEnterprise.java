package com.ruoyi.sxsc.enterprise.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 企业信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscEnterprise extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 账号主键 */
    @Excel(name = "账号主键")
    private Long userId;

    /** 企业名称 */
    @Excel(name = "企业名称")
    private String name;

    /** 营业执照图片地址 */
    @Excel(name = "营业执照图片地址")
    private String license;

    /** 对公账户所属银行 */
    @Excel(name = "对公账户所属银行")
    private String corporateAccountBank;

    /** 对公账户 */
    @Excel(name = "对公账户")
    private String corporateAccount;

    /** 法人姓名 */
    @Excel(name = "法人姓名")
    private String legalPersonName;

    /** 法人手机号 */
    @Excel(name = "法人手机号")
    private String legalPersonPhone;

    /** 审核状态0待审核中1审核通过2拒绝 */
    @Excel(name = "审核状态0待审核中1审核通过2拒绝")
    private Long status;

    /** 企业类型1企业2个体户 */
    @Excel(name = "企业类型1企业2个体户")
    private Long type;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 自营商家1自营2不是 */
    @Excel(name = "自营商家1自营2不是")
    private Long self;

    /** 是否删除0未删除1删除 */
    private Long delFlag;

    /** 省 */
    @Excel(name = "省")
    private String province;

    /** 省编码 */
    @Excel(name = "省编码")
    private String provinceCode;

    /** 市 */
    @Excel(name = "市")
    private String city;

    /** 市编码 */
    @Excel(name = "市编码")
    private String cityCode;

    /** 县 */
    @Excel(name = "县")
    private String county;

    /** 县编码 */
    @Excel(name = "县编码")
    private String countyCode;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 验证码 */
    @TableField(exist = false)
    private String verificationCode;

    /** 商品描述评分 */
    @TableField(exist = false)
    private BigDecimal describeStarLevel;

    /** 卖家服务评分 */
    @TableField(exist = false)
    private BigDecimal sellerStarLevel;

    /** 物流服务评分 */
    @TableField(exist = false)
    private BigDecimal logisticsStarLevel;
}

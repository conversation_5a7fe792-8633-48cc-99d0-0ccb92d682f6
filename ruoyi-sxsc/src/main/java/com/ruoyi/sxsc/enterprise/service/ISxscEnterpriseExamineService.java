package com.ruoyi.sxsc.enterprise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterpriseExamine;

import java.util.List;

/**
 * 企业审核记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public interface ISxscEnterpriseExamineService extends IService<SxscEnterpriseExamine>
{
    /**
     * 查询企业审核记录
     * 
     * @param id 企业审核记录主键
     * @return 企业审核记录
     */
    SxscEnterpriseExamine selectSxscEnterpriseExamineById(Long id);

    /**
     * 查询企业审核记录列表
     * 
     * @param sxscEnterpriseExamine 企业审核记录
     * @return 企业审核记录集合
     */
    List<SxscEnterpriseExamine> selectSxscEnterpriseExamineList(SxscEnterpriseExamine sxscEnterpriseExamine);

    /**
     * 新增企业审核记录
     * 
     * @param sxscEnterpriseExamine 企业审核记录
     * @return 结果
     */
    AjaxResult insertSxscEnterpriseExamine(SxscEnterpriseExamine sxscEnterpriseExamine);


}

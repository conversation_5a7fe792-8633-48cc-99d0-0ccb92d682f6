package com.ruoyi.sxsc.enterprise.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterpriseExamine;
import com.ruoyi.sxsc.enterprise.service.ISxscEnterpriseExamineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 企业审核记录
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@RestController
@RequestMapping("/enterprise/examine")
public class SxscEnterpriseExamineController extends BaseController
{
    @Autowired
    private ISxscEnterpriseExamineService sxscEnterpriseExamineService;

    /**
     * 查询企业审核记录列表
     */
    @PreAuthorize("@ss.hasPermi('enterprise:examine:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscEnterpriseExamine sxscEnterpriseExamine)
    {
        startPage();
        List<SxscEnterpriseExamine> list = sxscEnterpriseExamineService.selectSxscEnterpriseExamineList(sxscEnterpriseExamine);
        return getDataTable(list);
    }


    /**
     * 获取企业审核记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('enterprise:examine:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscEnterpriseExamineService.selectSxscEnterpriseExamineById(id));
    }

    /**
     * 新增企业审核记录
     */
    @PreAuthorize("@ss.hasPermi('enterprise:examine:add')")
    @Log(title = "企业审核记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscEnterpriseExamine sxscEnterpriseExamine)
    {
        return sxscEnterpriseExamineService.insertSxscEnterpriseExamine(sxscEnterpriseExamine);
    }


}

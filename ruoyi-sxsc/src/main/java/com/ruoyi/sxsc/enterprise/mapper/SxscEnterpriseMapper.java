package com.ruoyi.sxsc.enterprise.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterprise;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 企业信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public interface SxscEnterpriseMapper extends BaseMapper<SxscEnterprise>
{


    @Select("<script>"+
            " SELECT " +
            "  <![CDATA[   ROUND(6378.138*2*ASIN(SQRT(POW(SIN((#{latitude}*PI()/180-latitude*PI()/180)/2),2)+" +
            "     COS(#{latitude}*PI()/180)*COS(latitude*PI()/180)*POW(SIN((#{longitude}*PI()/180- " +
            "     longitude*PI()/180)/2),2)))*1000)  ]]> AS distance , a.*,s.avatar" +
            " FROM " +
            "    sxsc_enterprise as a,sys_user as s where a.user_Id=s.user_id" +
            " HAVING " +
            "     distance <![CDATA[<= ]]>10000 ORDER BY distance ASC"+
            "</script>")
    List<SxscEnterprise> nearby(@Param("longitude") String longitude, @Param("latitude")String latitude);
}

package com.ruoyi.sxsc.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityEvaluateService;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterprise;
import com.ruoyi.sxsc.enterprise.mapper.SxscEnterpriseMapper;
import com.ruoyi.sxsc.enterprise.service.ISxscEnterpriseService;
import com.ruoyi.sxsc.utils.SMSUtils;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 企业信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Service
public class SxscEnterpriseServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscEnterpriseMapper,SxscEnterprise> implements ISxscEnterpriseService
{


    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    SxscEnterpriseMapper sxscEnterpriseMapper;

    @Autowired
    ISxscCommodityEvaluateService iSxscCommodityEvaluateService;

    @Autowired
    SMSUtils smsUtils;
    /**
     * 查询企业信息
     * 
     * @param id 企业信息主键
     * @return 企业信息
     */
    @Override
    public SxscEnterprise selectSxscEnterpriseById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询商家信息
     *
     * @param userId 账号主键
     * @return 商家信息
     */
    @Override
    public SxscEnterprise selectEnterpriseByUserId(Long userId)
    {
        LambdaQueryWrapper<SxscEnterprise> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SxscEnterprise::getDelFlag,0);
        lambdaQueryWrapper.eq(SxscEnterprise::getUserId,userId);
        SxscEnterprise enterprise=getOne(lambdaQueryWrapper);
        if(StringUtils.isNull(enterprise)){
            return null;
        }
        enterprise.setSysUser(iSysUserService.selectUserMainById(userId));
        enterprise.setDescribeStarLevel(iSxscCommodityEvaluateService.describeStarLevel(userId));
        enterprise.setLogisticsStarLevel(iSxscCommodityEvaluateService.logisticsStarLevel(userId));
        enterprise.setSellerStarLevel(iSxscCommodityEvaluateService.sellerStarLevel(userId));
        return enterprise;
    }

    /**
     * 查询企业信息列表
     * 
     * @param sxscEnterprise 企业信息
     * @return 企业信息
     */
    @Override
    public List<SxscEnterprise> selectSxscEnterpriseList(SxscEnterprise sxscEnterprise)
    {
        LambdaQueryWrapper<SxscEnterprise> wrapper=new LambdaQueryWrapper();

        wrapper.like(StringUtils.isNotNull(sxscEnterprise.getName()),SxscEnterprise::getName,sxscEnterprise.getName());

        wrapper.like(StringUtils.isNotNull(sxscEnterprise.getLegalPersonName()),SxscEnterprise::getLegalPersonName,sxscEnterprise.getLegalPersonName());

        wrapper.like(StringUtils.isNotNull(sxscEnterprise.getLegalPersonPhone()),SxscEnterprise::getLegalPersonPhone,sxscEnterprise.getLegalPersonPhone());

        wrapper.eq(StringUtils.isNotNull(sxscEnterprise.getStatus()),SxscEnterprise::getStatus,sxscEnterprise.getStatus());

        wrapper.eq(StringUtils.isNotNull(sxscEnterprise.getType()),SxscEnterprise::getType,sxscEnterprise.getType());

        wrapper.eq(StringUtils.isNotNull(sxscEnterprise.getSelf()),SxscEnterprise::getSelf,sxscEnterprise.getSelf());

        wrapper.eq(StringUtils.isNotNull(sxscEnterprise.getCreateBy()), SxscEnterprise::getCreateBy,sxscEnterprise.getCreateBy());

        wrapper.eq(SxscEnterprise::getDelFlag,0l);

        wrapper.orderByDesc(SxscEnterprise::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增企业信息
     * 
     * @param sxscEnterprise 企业信息
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscEnterprise(SxscEnterprise sxscEnterprise)
    {
        smsUtils.verifySmsCode(sxscEnterprise.getLegalPersonPhone(),sxscEnterprise.getVerificationCode());

        SxscEnterprise enterpriseData=selectEnterpriseByUserId(SecurityUtils.getUserId());
        if(StringUtils.isNotNull(enterpriseData)){
            enterpriseData.setDelFlag(1L);
            enterpriseData.setUpdateBy(SecurityUtils.getUsername());
            enterpriseData.setUpdateTime(DateUtils.getNowDate());
            updateById(enterpriseData);
        }
        sxscEnterprise.setStatus(0l);
        sxscEnterprise.setUserId(SecurityUtils.getUserId());
        sxscEnterprise.setCreateBy(SecurityUtils.getUsername());
        sxscEnterprise.setCreateTime(DateUtils.getNowDate());
        save(sxscEnterprise);
        return AjaxResult.success();
    }

    /**
     * 修改企业信息
     * 
     * @param sxscEnterprise 企业信息
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscEnterprise(SxscEnterprise sxscEnterprise)
    {
        SxscEnterprise enterpriseData=getById(sxscEnterprise.getId());
        if(StringUtils.isNull(enterpriseData)){
            return AjaxResult.warn("商家信息不存在，无法修改状态");
        }
        if(enterpriseData.getStatus()==1){
            return AjaxResult.warn("已通过审核，无法修改！");
        }
        if(enterpriseData.getStatus()==2){
            sxscEnterprise.setStatus(0L);
        }
        sxscEnterprise.setDelFlag(0L);
        sxscEnterprise.setUserId(SecurityUtils.getUserId());
        sxscEnterprise.setUpdateBy(SecurityUtils.getUsername());
        sxscEnterprise.setUpdateTime(DateUtils.getNowDate());
        updateById(sxscEnterprise);
        return AjaxResult.success();
    }


    @Override
    public AjaxResult updateEnterpriseStatus(Long status,Long  id)
    {
        SxscEnterprise enterpriseData=getById(id);
        if(StringUtils.isNull(enterpriseData)){
            return AjaxResult.warn("商家信息不存在，无法修改");
        }
        enterpriseData.setId(id);
        enterpriseData.setStatus(status);
        enterpriseData.setDelFlag(0L);
        enterpriseData.setUpdateBy(SecurityUtils.getUsername());
        enterpriseData.setUpdateTime(DateUtils.getNowDate());
        return AjaxResult.success(updateById(enterpriseData)?1:0);
    }

    @Override
    public AjaxResult updateEnterpriseSelf(Long self,Long  id)
    {
        SxscEnterprise enterpriseData=getById(id);
        if(StringUtils.isNull(enterpriseData)){
            return AjaxResult.warn("商家信息不存在，无法修改");
        }
        enterpriseData.setId(id);
        enterpriseData.setSelf(self);
        enterpriseData.setDelFlag(0L);
        enterpriseData.setUpdateBy(SecurityUtils.getUsername());
        enterpriseData.setUpdateTime(DateUtils.getNowDate());
        return AjaxResult.success(updateById(enterpriseData)?1:0);
    }

    /**
     * 批量删除企业信息
     * 
     * @param ids 需要删除的企业信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscEnterpriseByIds(Long[] ids)
    {
        return removeByIds(Arrays.asList(ids))?1:0;
    }

    /**
     * 删除企业信息信息
     * 
     * @param id 企业信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscEnterpriseById(Long id)
    {
        return removeById(id)?1:0;
    }

    @Override
    public AjaxResult nearby(String longitude, String latitude) {

        return AjaxResult.success(sxscEnterpriseMapper.nearby(longitude,latitude));
    }
}

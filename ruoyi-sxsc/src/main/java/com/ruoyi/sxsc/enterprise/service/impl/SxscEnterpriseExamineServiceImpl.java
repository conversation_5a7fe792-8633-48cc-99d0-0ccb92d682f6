package com.ruoyi.sxsc.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterprise;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterpriseExamine;
import com.ruoyi.sxsc.enterprise.mapper.SxscEnterpriseExamineMapper;
import com.ruoyi.sxsc.enterprise.service.ISxscEnterpriseExamineService;
import com.ruoyi.sxsc.enterprise.service.ISxscEnterpriseService;
import com.ruoyi.system.domain.SysUserOnline;
import com.ruoyi.system.service.ISysUserOnlineService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 企业审核记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Service
public class SxscEnterpriseExamineServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscEnterpriseExamineMapper,SxscEnterpriseExamine> implements ISxscEnterpriseExamineService
{

    @Autowired
    ISxscEnterpriseService iSxscEnterpriseService;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    private ISysUserOnlineService userOnlineService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询企业审核记录
     * 
     * @param id 企业审核记录主键
     * @return 企业审核记录
     */
    @Override
    public SxscEnterpriseExamine selectSxscEnterpriseExamineById(Long id)
    {
        return getById(id);
    }

    /**
     * 查询企业审核记录列表
     * 
     * @param sxscEnterpriseExamine 企业审核记录
     * @return 企业审核记录
     */
    @Override
    public List<SxscEnterpriseExamine> selectSxscEnterpriseExamineList(SxscEnterpriseExamine sxscEnterpriseExamine)
    {
        LambdaQueryWrapper<SxscEnterpriseExamine> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscEnterpriseExamine.getEnterpriseId()),SxscEnterpriseExamine::getEnterpriseId,sxscEnterpriseExamine.getEnterpriseId());

        wrapper.like(StringUtils.isNotNull(sxscEnterpriseExamine.getRemarks()),SxscEnterpriseExamine::getRemarks,sxscEnterpriseExamine.getRemarks());

        wrapper.eq(StringUtils.isNotNull(sxscEnterpriseExamine.getStatus()),SxscEnterpriseExamine::getStatus,sxscEnterpriseExamine.getStatus());

        wrapper.orderByDesc(SxscEnterpriseExamine::getCreateTime);

        List<SxscEnterpriseExamine> list=list(wrapper);

        for(SxscEnterpriseExamine examine:list){
            examine.setEnterprise(iSxscEnterpriseService.selectSxscEnterpriseById(examine.getEnterpriseId()));
        }
        return list;
    }

    /**
     * 新增企业审核记录
     * 
     * @param sxscEnterpriseExamine 企业审核记录
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscEnterpriseExamine(SxscEnterpriseExamine sxscEnterpriseExamine)
    {
        SxscEnterprise sxscEnterprise=iSxscEnterpriseService.selectSxscEnterpriseById(sxscEnterpriseExamine.getEnterpriseId());
        if(StringUtils.isNull(sxscEnterprise)||sxscEnterprise.getStatus()!=0){
            return AjaxResult.error("无法审核");
        }
        if(sxscEnterpriseExamine.getStatus()==1){
            SysUser sysUser=new SysUser();
            sysUser.setUserId(sxscEnterprise.getUserId());
            sysUser.setUserType("02");
            sysUser.setDeptId(102l);
            iSysUserService.insertUserAuth(sxscEnterprise.getUserId(),new Long[]{102l});
            iSysUserService.updateUserStatus(sysUser);
            // 更改用户类型，需要重新登录
            SysUserMain sysUserMain=iSysUserService.selectUserMainById(sxscEnterprise.getUserId());
            List<SysUserOnline> list=userOnlineService.userOnlineList("",sysUserMain.getUserName());
            for(SysUserOnline userOnline:list){
                redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + userOnline.getTokenId());
            }
        }
        iSxscEnterpriseService.updateEnterpriseStatus(sxscEnterpriseExamine.getStatus(), sxscEnterpriseExamine.getEnterpriseId());
        sxscEnterpriseExamine.setCreateBy(SecurityUtils.getUsername());
        sxscEnterpriseExamine.setCreateTime(DateUtils.getNowDate());
        save(sxscEnterpriseExamine);
        return AjaxResult.success();
    }


}
